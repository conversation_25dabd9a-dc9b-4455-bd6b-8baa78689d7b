<?php
require_once 'auth.php';
require_once 'config/database_sqlite.php';

$start_date = $_GET['start'] ?? date('Y-m-d', strtotime('monday this week'));
$end_date = $_GET['end'] ?? date('Y-m-d', strtotime('sunday this week'));

// التحقق من صحة التواريخ
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
    echo '<div class="alert alert-danger">تواريخ غير صحيحة</div>';
    exit;
}

try {
    // جلب بيانات التحصيلات للفترة المحددة
    $stmt = $pdo->prepare("
        SELECT
            a.agent_name,
            SUM(c.gateway_amount) as total_gateway,
            SUM(c.riyal_mobile_amount) as total_riyal,
            SUM(c.gateway_amount + c.riyal_mobile_amount) as total_amount,
            COUNT(c.id) as days_count
        FROM agents a
        LEFT JOIN collections c ON a.agent_id = c.agent_id
            AND c.collection_date BETWEEN ? AND ?
        GROUP BY a.agent_id, a.agent_name
        ORDER BY a.agent_name
    ");
    $stmt->execute([$start_date, $end_date]);
    $collections = $stmt->fetchAll();

    // حساب الإجماليات
    $total_gateway = 0;
    $total_riyal = 0;
    $total_amount = 0;
    $active_agents = 0;

    foreach ($collections as $collection) {
        if ($collection['total_amount'] > 0) {
            $active_agents++;
        }
        $total_gateway += $collection['total_gateway'] ?? 0;
        $total_riyal += $collection['total_riyal'] ?? 0;
        $total_amount += $collection['total_amount'] ?? 0;
    }

    // تنسيق التواريخ للعرض
    $formatted_start = date('d/m/Y', strtotime($start_date));
    $formatted_end = date('d/m/Y', strtotime($end_date));

    ?>

    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-calendar-week"></i>
                التقرير الأسبوعي من <?= $formatted_start ?> إلى <?= $formatted_end ?>
            </h5>
        </div>
        <div class="card-body">
            <!-- ملخص سريع -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي البوابة</h6>
                            <h4 class="text-primary"><?= number_format($total_gateway, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي ريال موبايل</h6>
                            <h4 class="text-info"><?= number_format($total_riyal, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الإجمالي العام</h6>
                            <h4 class="text-success"><?= number_format($total_amount, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الوكلاء النشطون</h6>
                            <h4 class="text-warning"><?= $active_agents ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التفاصيل -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>اسم الوكيل</th>
                            <th>البوابة</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                            <th>عدد الأيام</th>
                            <th>المتوسط اليومي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($collections as $collection): ?>
                        <?php
                        $daily_avg = $collection['days_count'] > 0 ? $collection['total_amount'] / $collection['days_count'] : 0;
                        ?>
                        <tr class="<?= ($collection['total_amount'] > 0) ? 'table-success' : '' ?>">
                            <td><?= htmlspecialchars($collection['agent_name']) ?></td>
                            <td><?= number_format($collection['total_gateway'] ?? 0, 2) ?></td>
                            <td><?= number_format($collection['total_riyal'] ?? 0, 2) ?></td>
                            <td><strong><?= number_format($collection['total_amount'] ?? 0, 2) ?></strong></td>
                            <td><?= $collection['days_count'] ?></td>
                            <td><?= number_format($daily_avg, 2) ?></td>
                            <td>
                                <?php if ($collection['total_amount'] > 0): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th>الإجمالي العام</th>
                            <th><?= number_format($total_gateway, 2) ?></th>
                            <th><?= number_format($total_riyal, 2) ?></th>
                            <th><?= number_format($total_amount, 2) ?></th>
                            <th>-</th>
                            <th><?= number_format($active_agents > 0 ? $total_amount / $active_agents : 0, 2) ?></th>
                            <th><?= $active_agents ?> وكيل</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-3">
                <div class="col-md-6">
                    <?php if ($total_amount == 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            لا توجد تحصيلات لهذه الفترة
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>إحصائيات الفترة:</strong><br>
                            - إجمالي التحصيلات: <?= number_format($total_amount, 2) ?> ريال<br>
                            - متوسط التحصيل للوكيل: <?= number_format($active_agents > 0 ? $total_amount / $active_agents : 0, 2) ?> ريال<br>
                            - نسبة الوكلاء النشطين: <?= number_format(($active_agents / count($collections)) * 100, 1) ?>%
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <i class="fas fa-calendar"></i>
                        <strong>معلومات الفترة:</strong><br>
                        - من: <?= $formatted_start ?><br>
                        - إلى: <?= $formatted_end ?><br>
                        - عدد الأيام: <?= (strtotime($end_date) - strtotime($start_date)) / (60*60*24) + 1 ?> يوم
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
