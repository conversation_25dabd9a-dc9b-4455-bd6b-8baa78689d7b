<?php
// قسم النسخ الاحتياطي
$message = '';
$error = '';

// معالجة إنشاء نسخة احتياطية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_backup'])) {
    try {
        $backup_dir = 'backups';
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = $backup_dir . '/backup_' . $timestamp . '.sql';
        
        // إنشاء النسخة الاحتياطية
        $tables = ['users', 'agents', 'collections'];
        $backup_content = "-- نسخة احتياطية من قاعدة البيانات\n";
        $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($tables as $table) {
            $backup_content .= "-- جدول $table\n";
            $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
            
            // هيكل الجدول
            $create_stmt = $pdo->query("SHOW CREATE TABLE `$table`");
            $create_row = $create_stmt->fetch();
            $backup_content .= $create_row['Create Table'] . ";\n\n";
            
            // بيانات الجدول
            $data_stmt = $pdo->query("SELECT * FROM `$table`");
            while ($row = $data_stmt->fetch(PDO::FETCH_ASSOC)) {
                $values = array_map(function($value) use ($pdo) {
                    return $value === null ? 'NULL' : $pdo->quote($value);
                }, array_values($row));
                
                $backup_content .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup_content .= "\n";
        }
        
        file_put_contents($backup_file, $backup_content);
        $message = "تم إنشاء النسخة الاحتياطية بنجاح: $backup_file";
        
    } catch (Exception $e) {
        $error = 'خطأ في إنشاء النسخة الاحتياطية: ' . $e->getMessage();
    }
}

// جلب قائمة النسخ الاحتياطية الموجودة
$backup_files = [];
$backup_dir = 'backups';
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($backup_dir . '/' . $file),
                'date' => filemtime($backup_dir . '/' . $file)
            ];
        }
    }
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($backup_files, function($a, $b) {
        return $b['date'] - $a['date'];
    });
}

// حساب إحصائيات قاعدة البيانات
$stats = [];
$tables = ['users', 'agents', 'collections'];
foreach ($tables as $table) {
    $count_stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
    $stats[$table] = $count_stmt->fetch()['count'];
}
?>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-database"></i> إنشاء نسخة احتياطية</h5>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>
                
                <p>إنشاء نسخة احتياطية كاملة من قاعدة البيانات تشمل:</p>
                <ul>
                    <li><i class="fas fa-users"></i> المستخدمين (<?= $stats['users'] ?> مستخدم)</li>
                    <li><i class="fas fa-user-tie"></i> الوكلاء (<?= $stats['agents'] ?> وكيل)</li>
                    <li><i class="fas fa-coins"></i> التحصيلات (<?= $stats['collections'] ?> تحصيل)</li>
                </ul>
                
                <form method="POST">
                    <button type="submit" name="create_backup" class="btn btn-primary" onclick="return confirm('هل تريد إنشاء نسخة احتياطية جديدة؟')">
                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                    </button>
                </form>
                
                <hr>
                
                <h6><i class="fas fa-info-circle"></i> معلومات مهمة:</h6>
                <ul class="small text-muted">
                    <li>يتم حفظ النسخ في مجلد backups</li>
                    <li>النسخة تشمل جميع البيانات والهيكل</li>
                    <li>يُنصح بإنشاء نسخة احتياطية يومياً</li>
                    <li>احتفظ بالنسخ في مكان آمن</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-history"></i> النسخ الاحتياطية الموجودة</h5>
            </div>
            <div class="card-body">
                <?php if (empty($backup_files)): ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-folder-open fa-3x mb-3"></i>
                        <p>لا توجد نسخ احتياطية</p>
                        <small>قم بإنشاء أول نسخة احتياطية</small>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>الحجم</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($backup_files as $file): ?>
                                <tr>
                                    <td>
                                        <i class="fas fa-file-archive text-primary"></i>
                                        <?= htmlspecialchars($file['name']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= number_format($file['size'] / 1024, 1) ?> KB
                                        </span>
                                    </td>
                                    <td>
                                        <small><?= date('d/m/Y H:i', $file['date']) ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="backups/<?= $file['name'] ?>" 
                                               class="btn btn-success" 
                                               download
                                               title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button class="btn btn-danger" 
                                                    onclick="deleteBackup('<?= $file['name'] ?>')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- أدوات إضافية -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h6><i class="fas fa-upload"></i> استعادة نسخة احتياطية</h6>
            </div>
            <div class="card-body">
                <p class="small">رفع ملف نسخة احتياطية لاستعادة البيانات</p>
                <form enctype="multipart/form-data">
                    <div class="mb-2">
                        <input type="file" class="form-control form-control-sm" accept=".sql">
                    </div>
                    <button type="button" class="btn btn-warning btn-sm" onclick="alert('ميزة الاستعادة قيد التطوير')">
                        <i class="fas fa-upload"></i> استعادة
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6><i class="fas fa-file-excel"></i> تصدير البيانات</h6>
            </div>
            <div class="card-body">
                <p class="small">تصدير البيانات بصيغة Excel</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-info btn-sm" onclick="exportData('agents')">
                        <i class="fas fa-users"></i> تصدير الوكلاء
                    </button>
                    <button class="btn btn-info btn-sm" onclick="exportData('collections')">
                        <i class="fas fa-coins"></i> تصدير التحصيلات
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h6><i class="fas fa-cogs"></i> إعدادات النظام</h6>
            </div>
            <div class="card-body">
                <p class="small">إعدادات وصيانة النظام</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-secondary btn-sm" onclick="optimizeDatabase()">
                        <i class="fas fa-tools"></i> تحسين قاعدة البيانات
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="clearCache()">
                        <i class="fas fa-broom"></i> مسح الذاكرة المؤقتة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteBackup(filename) {
    if (confirm('هل تريد حذف هذه النسخة الاحتياطية؟\n' + filename)) {
        // إرسال طلب حذف
        fetch('delete_backup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({filename: filename})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطأ في حذف الملف');
            }
        });
    }
}

function exportData(type) {
    window.open(`export_data.php?type=${type}`, '_blank');
}

function optimizeDatabase() {
    if (confirm('هل تريد تحسين قاعدة البيانات؟')) {
        alert('ميزة تحسين قاعدة البيانات قيد التطوير');
    }
}

function clearCache() {
    if (confirm('هل تريد مسح الذاكرة المؤقتة؟')) {
        alert('تم مسح الذاكرة المؤقتة');
    }
}
</script>
