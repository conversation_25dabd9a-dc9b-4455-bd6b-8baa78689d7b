<?php
require_once 'config/database.php';

$message = '';
$error = '';

if ($_POST && isset($_FILES['excel_file'])) {
    $import_date = $_POST['import_date'] ?? '';
    $file = $_FILES['excel_file'];

    if ($import_date && $file['error'] == 0) {
        $allowed_types = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

        if (in_array($file['type'], $allowed_types) || pathinfo($file['name'], PATHINFO_EXTENSION) == 'csv') {
            try {
                $handle = fopen($file['tmp_name'], 'r');
                $imported_count = 0;
                $errors = [];
                $line_num = 0;

                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $line_num++;

                    // تخطي السطر الأول إذا كان يحتوي على عناوين
                    if ($line_num == 1 && (stripos($data[0], 'وكيل') !== false || stripos($data[0], 'agent') !== false)) {
                        continue;
                    }

                    if (count($data) >= 1) {
                        $agent_name = trim($data[0]);

                        // العمود الأول = البوابة (الفهرس 1)
                        $gateway_amount = 0;
                        if (isset($data[1]) && !empty(trim($data[1]))) {
                            $gateway_amount = floatval(str_replace(',', '', trim($data[1])));
                        }

                        // العمود الثاني = الريال موبايل (الفهرس 2)
                        $riyal_amount = 0;
                        if (isset($data[2]) && !empty(trim($data[2]))) {
                            $riyal_amount = floatval(str_replace(',', '', trim($data[2])));
                        }

                        // البحث عن الوكيل
                        $agent_stmt = $pdo->prepare("SELECT agent_id FROM agents WHERE agent_name LIKE ?");
                        $agent_stmt->execute(["%$agent_name%"]);
                        $agent_id = $agent_stmt->fetchColumn();

                        if ($agent_id) {
                            // التحقق من وجود البيانات
                            $check_stmt = $pdo->prepare("SELECT id FROM collections WHERE agent_id = ? AND collection_date = ?");
                            $check_stmt->execute([$agent_id, $import_date]);

                            if ($check_stmt->fetchColumn()) {
                                $update_stmt = $pdo->prepare("UPDATE collections SET riyal_mobile_amount = ?, gateway_amount = ? WHERE agent_id = ? AND collection_date = ?");
                                $update_stmt->execute([$riyal_amount, $gateway_amount, $agent_id, $import_date]);
                            } else {
                                $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, riyal_mobile_amount, gateway_amount, collection_date) VALUES (?, ?, ?, ?)");
                                $insert_stmt->execute([$agent_id, $riyal_amount, $gateway_amount, $import_date]);
                            }
                            $imported_count++;
                        } else {
                            $errors[] = "السطر $line_num: تم تخطي الوكيل '$agent_name' - غير موجود في قاعدة البيانات";
                        }
                    }
                }

                fclose($handle);

                if ($imported_count > 0) {
                    $message = "تم استيراد $imported_count سجل من ملف Excel بنجاح";
                    if (!empty($errors)) {
                        $message .= " مع تخطي " . count($errors) . " خطأ";
                        $error = "الأخطاء المتخطاة:<br>" . implode("<br>", array_slice($errors, 0, 10)); // عرض أول 10 أخطاء فقط
                        if (count($errors) > 10) {
                            $error .= "<br>... و " . (count($errors) - 10) . " أخطاء أخرى";
                        }
                    }
                } else {
                    $error = "لم يتم استيراد أي سجل. تحقق من صحة البيانات والوكلاء.";
                    if (!empty($errors)) {
                        $error .= "<br><br>الأخطاء:<br>" . implode("<br>", array_slice($errors, 0, 10));
                    }
                }

            } catch (Exception $e) {
                $error = "خطأ في قراءة الملف: " . $e->getMessage();
            }
        } else {
            $error = "نوع الملف غير مدعوم. يرجى استخدام ملف CSV أو Excel";
        }
    } else {
        $error = "يرجى تحديد التاريخ ورفع ملف صحيح";
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>استيراد من Excel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">استيراد البيانات من Excel/CSV</h2>

    <?php if ($message): ?>
        <div class="alert alert-success"><?= $message ?></div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">رفع ملف Excel/CSV</div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label class="form-label">تاريخ التحصيل:</label>
                            <input type="date" name="import_date" class="form-control" required
                                   value="<?= $_POST['import_date'] ?? date('Y-m-d') ?>">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملف Excel/CSV:</label>
                            <input type="file" name="excel_file" class="form-control"
                                   accept=".csv,.xls,.xlsx" required>
                            <small class="text-muted">
                                الملف يجب أن يحتوي على 3 أعمدة: اسم الوكيل، الريال موبايل، البوابة
                            </small>
                        </div>

                        <button type="submit" class="btn btn-primary">استيراد الملف</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">تنسيق الملف المطلوب</div>
                <div class="card-body">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>اسم الوكيل</th>
                                <th>البوابة</th>
                                <th>الريال موبايل</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>الوكيل الأول</td>
                                <td>1500.50</td>
                                <td>2000.75</td>
                            </tr>
                            <tr>
                                <td>الشرق</td>
                                <td>1200.00</td>
                                <td>1800.25</td>
                            </tr>
                            <tr>
                                <td>جوث</td>
                                <td>900.75</td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="import_data.php" class="btn btn-info">استيراد نصي</a>
        <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
    </div>
</div>
</body>
</html>

