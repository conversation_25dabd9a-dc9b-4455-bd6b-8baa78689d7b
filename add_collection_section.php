<?php
// قسم إضافة التحصيلات المبسط
$selected_date = $_GET['date'] ?? date('Y-m-d');

// معالجة إضافة التحصيلات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_collections'])) {
    $collection_date = $_POST['collection_date'];
    $collections_data = $_POST['collections'] ?? [];

    try {
        $pdo->beginTransaction();

        // حذف البيانات الموجودة لهذا التاريخ
        $delete_stmt = $pdo->prepare("DELETE FROM collections WHERE collection_date = ?");
        $delete_stmt->execute([$collection_date]);

        // إدراج البيانات الجديدة
        $insert_stmt = $pdo->prepare("
            INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date)
            VALUES (?, ?, ?, ?)
        ");

        foreach ($collections_data as $agent_id => $data) {
            $gateway = floatval($data['gateway'] ?? 0);
            $riyal = floatval($data['riyal'] ?? 0);

            if ($gateway > 0 || $riyal > 0) {
                $insert_stmt->execute([$agent_id, $gateway, $riyal, $collection_date]);
            }
        }

        $pdo->commit();
        echo '<div class="alert alert-success">تم حفظ التحصيلات بنجاح</div>';

    } catch (PDOException $e) {
        $pdo->rollback();
        echo '<div class="alert alert-danger">خطأ في حفظ البيانات: ' . $e->getMessage() . '</div>';
    }
}

// جلب البيانات الموجودة
$existing_data = [];
$stmt = $pdo->prepare("SELECT agent_id, gateway_amount, riyal_mobile_amount FROM collections WHERE collection_date = ?");
$stmt->execute([$selected_date]);
while ($row = $stmt->fetch()) {
    $existing_data[$row['agent_id']] = $row;
}
?>

<div class="row mb-3">
    <div class="col-md-4">
        <label class="form-label">التاريخ:</label>
        <input type="date" id="collection-date" class="form-control" value="<?= $selected_date ?>" onchange="loadCollectionData()">
    </div>
    <div class="col-md-4 d-flex align-items-end">
        <button class="btn btn-info" onclick="loadCollectionData()">
            <i class="fas fa-refresh"></i> تحديث البيانات
        </button>
    </div>
</div>

<form method="POST" id="collectionsForm">
    <input type="hidden" name="collection_date" value="<?= $selected_date ?>">

    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-plus-circle"></i> إدخال تحصيلات يوم <?= date('d/m/Y', strtotime($selected_date)) ?></h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الوكيل</th>
                            <th>البوابة</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($agents as $agent): ?>
                        <?php
                        $existing = $existing_data[$agent['agent_id']] ?? null;
                        $gateway_value = $existing ? $existing['gateway_amount'] : 0;
                        $riyal_value = $existing ? $existing['riyal_mobile_amount'] : 0;
                        ?>
                        <tr class="<?= $existing ? 'table-success' : '' ?>">
                            <td><strong><?= htmlspecialchars($agent['agent_name']) ?></strong></td>
                            <td>
                                <input type="number"
                                       name="collections[<?= $agent['agent_id'] ?>][gateway]"
                                       class="form-control"
                                       step="0.01"
                                       min="0"
                                       value="<?= $gateway_value ?>"
                                       onchange="calculateTotal(<?= $agent['agent_id'] ?>)">
                            </td>
                            <td>
                                <input type="number"
                                       name="collections[<?= $agent['agent_id'] ?>][riyal]"
                                       class="form-control"
                                       step="0.01"
                                       min="0"
                                       value="<?= $riyal_value ?>"
                                       onchange="calculateTotal(<?= $agent['agent_id'] ?>)">
                            </td>
                            <td>
                                <span class="badge bg-secondary total-amount" id="total_<?= $agent['agent_id'] ?>">
                                    <?= number_format($gateway_value + $riyal_value, 2) ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th>الإجمالي العام</th>
                            <th><span id="total_gateway" class="badge bg-primary">0.00</span></th>
                            <th><span id="total_riyal" class="badge bg-info">0.00</span></th>
                            <th><span id="grand_total" class="badge bg-success">0.00</span></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <div>
                    <button type="submit" name="save_collections" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التحصيلات
                    </button>
                    <button type="button" class="btn btn-warning" onclick="clearAll()">
                        <i class="fas fa-eraser"></i> مسح الكل
                    </button>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        <a href="import_instructions.php" class="btn btn-success">
                            <i class="fas fa-file-import"></i> استيراد البوابة
                        </a>
                        <a href="import_riyal_mobile.php" class="btn btn-info">
                            <i class="fas fa-mobile-alt"></i> استيراد ريال موبايل
                        </a>
                    </div>
                    <button type="button" class="btn btn-info" onclick="fillSampleData()">
                        <i class="fas fa-magic"></i> بيانات تجريبية
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- نافذة استيراد البيانات -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد البيانات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="importTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="direct-tab" data-bs-toggle="tab" data-bs-target="#direct" type="button">
                            <i class="fas fa-keyboard"></i> الاستيراد المباشر
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="excel-tab" data-bs-toggle="tab" data-bs-target="#excel" type="button">
                            <i class="fas fa-file-excel"></i> استيراد من Excel
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="importTabContent">
                    <!-- الاستيراد المباشر -->
                    <div class="tab-pane fade show active" id="direct" role="tabpanel">
                        <form id="directImportForm">
                            <div class="mb-3">
                                <label class="form-label">البيانات (كل سطر: اسم الوكيل البوابة ريال_موبايل):</label>
                                <textarea id="directImportData" class="form-control" rows="8"
                                          placeholder="الوكيل الأول 1500.50 2000.75
الشرق 1200.00 1800.25
جوث 900.75 0
الشمال 0 1100.50
سقف 800.00 1200.75"
                                          style="font-family: monospace; direction: ltr; text-align: left;"></textarea>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>تعليمات:</strong><br>
                                - كل سطر يحتوي على: اسم الوكيل، مبلغ البوابة، مبلغ ريال موبايل<br>
                                - افصل بين القيم بمسافات<br>
                                - إذا لم يكن هناك مبلغ، اكتب 0 أو اتركه فارغاً
                            </div>
                            <button type="button" class="btn btn-primary" onclick="importDirectData()">
                                <i class="fas fa-upload"></i> استيراد البيانات
                            </button>
                        </form>
                    </div>

                    <!-- استيراد من Excel -->
                    <div class="tab-pane fade" id="excel" role="tabpanel">
                        <form id="excelImportForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label class="form-label">اختر ملف Excel:</label>
                                <input type="file" id="excelFile" class="form-control" accept=".xlsx,.xls,.csv">
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>تنسيق الملف المطلوب:</strong><br>
                                العمود الأول: اسم الوكيل<br>
                                العمود الثاني: مبلغ البوابة<br>
                                العمود الثالث: مبلغ ريال موبايل
                            </div>
                            <button type="button" class="btn btn-success" onclick="importExcelData()">
                                <i class="fas fa-file-excel"></i> استيراد من Excel
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// حساب إجمالي وكيل واحد
function calculateTotal(agentId) {
    const gateway = parseFloat(document.querySelector(`input[name="collections[${agentId}][gateway]"]`).value) || 0;
    const riyal = parseFloat(document.querySelector(`input[name="collections[${agentId}][riyal]"]`).value) || 0;
    const total = gateway + riyal;

    document.getElementById(`total_${agentId}`).textContent = total.toFixed(2);
    updateGrandTotals();
}

// تحديث الإجماليات العامة
function updateGrandTotals() {
    let totalGateway = 0;
    let totalRiyal = 0;

    document.querySelectorAll('input[name*="[gateway]"]').forEach(input => {
        totalGateway += parseFloat(input.value) || 0;
    });

    document.querySelectorAll('input[name*="[riyal]"]').forEach(input => {
        totalRiyal += parseFloat(input.value) || 0;
    });

    document.getElementById('total_gateway').textContent = totalGateway.toFixed(2);
    document.getElementById('total_riyal').textContent = totalRiyal.toFixed(2);
    document.getElementById('grand_total').textContent = (totalGateway + totalRiyal).toFixed(2);
}

// مسح جميع الحقول
function clearAll() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.value = '';
        });
        updateGrandTotals();
    }
}

// ملء بيانات تجريبية
function fillSampleData() {
    const inputs = document.querySelectorAll('input[name*="[gateway]"]');
    inputs.forEach((input, index) => {
        input.value = (Math.random() * 1000 + 500).toFixed(2);
        const agentId = input.name.match(/\[(\d+)\]/)[1];
        calculateTotal(agentId);
    });
}

// تحميل بيانات التحصيلات لتاريخ محدد
function loadCollectionData() {
    const date = document.getElementById('collection-date').value;
    if (date) {
        window.location.href = `main.php?section=add-collection&date=${date}`;
    }
}

// تحديث الإجماليات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrandTotals();

    // إضافة مستمع للأحداث لجميع الحقول
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            const agentId = this.name.match(/\[(\d+)\]/)[1];
            calculateTotal(agentId);
        });
    });
});
</script>
