<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>دليل إعداد MySQL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h1><i class="fas fa-database"></i> دليل إعداد قاعدة بيانات MySQL</h1>
        <p>دليل شامل لتحويل النظام من SQLite إلى MySQL واستخدام phpMyAdmin</p>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3>📋 خطوات الإعداد</h3>
                </div>
                <div class="card-body">
                    
                    <h4>1. تشغيل XAMPP/WAMP</h4>
                    <div class="alert alert-info">
                        <ul>
                            <li>تأكد من تشغيل <strong>Apache</strong> و <strong>MySQL</strong> في XAMPP</li>
                            <li>افتح XAMPP Control Panel</li>
                            <li>اضغط <strong>Start</strong> بجانب Apache و MySQL</li>
                            <li>تأكد من ظهور اللون الأخضر</li>
                        </ul>
                    </div>

                    <h4>2. الوصول إلى phpMyAdmin</h4>
                    <div class="alert alert-warning">
                        <p><strong>الرابط:</strong> <a href="https://localhost/phpmyadmin/" target="_blank">https://localhost/phpmyadmin/</a></p>
                        <p><strong>أو:</strong> <a href="http://localhost/phpmyadmin/" target="_blank">http://localhost/phpmyadmin/</a></p>
                        <ul>
                            <li><strong>اسم المستخدم:</strong> root</li>
                            <li><strong>كلمة المرور:</strong> (فارغة عادة)</li>
                        </ul>
                    </div>

                    <h4>3. إنشاء قاعدة البيانات</h4>
                    <div class="alert alert-primary">
                        <p><strong>الطريقة الأولى - استخدام أداة التحويل:</strong></p>
                        <ol>
                            <li>اضغط على الزر أدناه لبدء عملية التحويل التلقائي</li>
                            <li>اتبع الخطوات المعروضة</li>
                            <li>سيتم إنشاء قاعدة البيانات وتحويل النظام تلقائياً</li>
                        </ol>
                        <a href="migrate_to_mysql.php" class="btn btn-success btn-lg">
                            <i class="fas fa-magic"></i> بدء التحويل التلقائي
                        </a>
                    </div>

                    <div class="alert alert-secondary">
                        <p><strong>الطريقة الثانية - الاستيراد اليدوي:</strong></p>
                        <ol>
                            <li>افتح phpMyAdmin</li>
                            <li>اضغط على "Import" أو "استيراد"</li>
                            <li>اختر ملف <code>collections_system_mysql.sql</code></li>
                            <li>اضغط "Go" أو "تنفيذ"</li>
                        </ol>
                    </div>

                    <h4>4. التحقق من النجاح</h4>
                    <div class="alert alert-success">
                        <p>بعد الإعداد، يجب أن ترى:</p>
                        <ul>
                            <li>قاعدة بيانات باسم <strong>collections_system</strong></li>
                            <li>3 جداول: users, agents, collections</li>
                            <li>بيانات افتراضية في الجداول</li>
                        </ul>
                    </div>

                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>🔗 روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="https://localhost/phpmyadmin/" target="_blank" class="btn btn-primary">
                            <i class="fas fa-database"></i> phpMyAdmin
                        </a>
                        <a href="migrate_to_mysql.php" class="btn btn-success">
                            <i class="fas fa-cogs"></i> أداة التحويل
                        </a>
                        <a href="main.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> النظام الرئيسي
                        </a>
                        <a href="collections_system_mysql.sql" download class="btn btn-warning">
                            <i class="fas fa-download"></i> تحميل ملف SQL
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6>⚠️ معلومات مهمة</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>قاعدة البيانات:</strong> collections_system</li>
                        <li><strong>المستخدم:</strong> root</li>
                        <li><strong>كلمة المرور:</strong> (فارغة)</li>
                        <li><strong>الخادم:</strong> localhost</li>
                        <li><strong>المنفذ:</strong> 3306</li>
                    </ul>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h6>✅ مميزات MySQL</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> أداء أفضل</li>
                        <li><i class="fas fa-check text-success"></i> دعم أكبر للبيانات</li>
                        <li><i class="fas fa-check text-success"></i> واجهة phpMyAdmin</li>
                        <li><i class="fas fa-check text-success"></i> نسخ احتياطي سهل</li>
                        <li><i class="fas fa-check text-success"></i> استعلامات متقدمة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5>🛠️ استكشاف الأخطاء وإصلاحها</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>مشاكل شائعة:</h6>
                            <ul>
                                <li><strong>لا يمكن الوصول لـ phpMyAdmin:</strong>
                                    <ul>
                                        <li>تأكد من تشغيل Apache في XAMPP</li>
                                        <li>جرب http بدلاً من https</li>
                                        <li>تأكد من المنفذ 80</li>
                                    </ul>
                                </li>
                                <li><strong>خطأ في الاتصال بـ MySQL:</strong>
                                    <ul>
                                        <li>تأكد من تشغيل MySQL في XAMPP</li>
                                        <li>تحقق من المنفذ 3306</li>
                                        <li>أعد تشغيل XAMPP</li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>نصائح:</h6>
                            <ul>
                                <li>احفظ نسخة احتياطية من SQLite قبل التحويل</li>
                                <li>تأكد من تشغيل XAMPP قبل البدء</li>
                                <li>استخدم أداة التحويل التلقائي للسهولة</li>
                                <li>اختبر النظام بعد التحويل</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

</body>
</html>
