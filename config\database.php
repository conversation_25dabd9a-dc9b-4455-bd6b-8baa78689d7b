<?php
// إعدادات قاعدة البيانات - MySQL الجديدة
$db_type = 'mysql';
$db_host = 'localhost';
$db_port = '3306';
$db_name = 'collections_system';
$db_user = 'root';
$db_pass = '';

// إنشاء اتصال PDO
try {
    $dsn = "$db_type:host=$db_host;port=$db_port;dbname=$db_name;charset=utf8mb4";
    $pdo = new PDO($dsn, $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
} catch (PDOException $e) {
    die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
}

// إعدادات النظام
$system_name = 'نظام إدارة التحصيلات';
$version = '2.0 - MySQL الجديد';
$timezone = 'Asia/Riyadh';
date_default_timezone_set($timezone);

// إعدادات الجلسة
session_start();

// دالة للتحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

// دالة للحصول على معلومات المستخدم
function getCurrentUser() {
    global $pdo;
    if (isset($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }
    return null;
}

// دالة لتسجيل العمليات
function logActivity($action, $details = '') {
    global $pdo;
    if (isset($_SESSION['user_id'])) {
        try {
            $stmt = $pdo->prepare("INSERT INTO activity_log (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$_SESSION['user_id'], $action, $details]);
        } catch (Exception $e) {
            // تجاهل أخطاء السجل
        }
    }
}
?>