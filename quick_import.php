<?php
require_once 'config/database.php';

$message = '';
$error = '';

if ($_POST) {
    $import_date = $_POST['import_date'] ?? '';
    $excel_data = $_POST['excel_data'] ?? '';
    
    if ($import_date && $excel_data) {
        try {
            $lines = explode("\n", trim($excel_data));
            $imported_count = 0;
            $errors = [];
            
            foreach ($lines as $line_num => $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                // تقسيم البيانات بالمسافات أو التاب
                $data = preg_split('/[\s\t]+/', $line, 3);
                
                if (count($data) >= 1) {
                    $agent_name = trim($data[0]);
                    
                    // العمود الأول = البوابة
                    $gateway_amount = 0;
                    if (isset($data[1]) && !empty(trim($data[1]))) {
                        $gateway_amount = floatval(str_replace(',', '', trim($data[1])));
                    }
                    
                    // العمود الثاني = الريال موبايل
                    $riyal_amount = 0;
                    if (isset($data[2]) && !empty(trim($data[2]))) {
                        $riyal_amount = floatval(str_replace(',', '', trim($data[2])));
                    }
                    
                    // البحث عن الوكيل
                    $agent_stmt = $pdo->prepare("SELECT agent_id FROM agents WHERE agent_name = ? OR agent_name LIKE ?");
                    $agent_stmt->execute([$agent_name, "%$agent_name%"]);
                    $agent_id = $agent_stmt->fetchColumn();
                    
                    if ($agent_id) {
                        // التحقق من وجود البيانات لنفس التاريخ
                        $check_stmt = $pdo->prepare("SELECT id FROM collections WHERE agent_id = ? AND collection_date = ?");
                        $check_stmt->execute([$agent_id, $import_date]);
                        
                        if ($check_stmt->fetchColumn()) {
                            // تحديث البيانات الموجودة
                            $update_stmt = $pdo->prepare("UPDATE collections SET gateway_amount = ?, riyal_mobile_amount = ? WHERE agent_id = ? AND collection_date = ?");
                            $update_stmt->execute([$gateway_amount, $riyal_amount, $agent_id, $import_date]);
                        } else {
                            // إدراج بيانات جديدة
                            $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, ?, ?, ?)");
                            $insert_stmt->execute([$agent_id, $gateway_amount, $riyal_amount, $import_date]);
                        }
                        $imported_count++;
                    } else {
                        $errors[] = "السطر " . ($line_num + 1) . ": الوكيل '$agent_name' غير موجود";
                    }
                }
            }
            
            $message = "تم استيراد $imported_count سجل بنجاح";
            if (!empty($errors)) {
                $error = implode("<br>", $errors);
            }
            
        } catch (Exception $e) {
            $error = "خطأ في الاستيراد: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>استيراد سريع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .paste-area {
            min-height: 300px;
            font-family: monospace;
            background: #f8f9fa;
            direction: ltr;
            text-align: left;
        }
        .format-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">استيراد سريع من Excel</h2>
    
    <?php if ($message): ?>
        <div class="alert alert-success"><?= $message ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <h5>نسخ ولصق من Excel مباشرة</h5>
        </div>
        <div class="card-body">
            <div class="format-info mb-3">
                <h6><strong>ترتيب الأعمدة الجديد:</strong></h6>
                <p class="mb-1">اسم الوكيل | البوابة | الريال موبايل</p>
                <small class="text-muted">
                    العمود الأول = البوابة، العمود الثاني = الريال موبايل<br>
                    يمكن ترك أي عمود فارغ (سيعتبر صفر)
                </small>
            </div>
            
            <div class="alert alert-info">
                <strong>طريقة الاستخدام:</strong><br>
                1. افتح ملف Excel الخاص بك<br>
                2. رتب البيانات: اسم الوكيل | البوابة | الريال موبايل<br>
                3. حدد البيانات وانسخها (Ctrl+C)<br>
                4. الصق هنا (Ctrl+V)<br>
                5. اضغط استيراد
            </div>
            
            <form method="POST">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">تاريخ التحصيل:</label>
                        <input type="date" name="import_date" class="form-control" required 
                               value="<?= $_POST['import_date'] ?? date('Y-m-d') ?>">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الصق البيانات من Excel هنا:</label>
                    <textarea name="excel_data" class="form-control paste-area" 
                              placeholder="الصق البيانات المنسوخة من Excel هنا...
مثال:
الوكيل الأول	1500.50	2000.75
الشرق	1200.00	1800.25
جوث	900.75	
الشمال		1100.50
سقف	800.00	1200.75" required><?= $_POST['excel_data'] ?? '' ?></textarea>
                </div>
                
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary">استيراد البيانات</button>
                    <button type="button" class="btn btn-secondary" onclick="clearData()">مسح</button>
                    <button type="button" class="btn btn-info" onclick="showExample()">مثال</button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="text-center mt-3">
        <a href="import_data.php" class="btn btn-outline-primary">استيراد متقدم</a>
        <a href="reports.php" class="btn btn-outline-secondary">العودة للتقارير</a>
    </div>
</div>

<script>
function clearData() {
    document.querySelector('textarea[name="excel_data"]').value = '';
}

function showExample() {
    const example = `الوكيل الأول	1500.50	2000.75
الشرق	1200.00	1800.25
جوث	900.75	
الشمال		1100.50
سقف	800.00	1200.75
الأثير		
المريخ	500.00	750.25`;
    
    document.querySelector('textarea[name="excel_data"]').value = example;
}

// تحسين تجربة اللصق
document.querySelector('textarea[name="excel_data"]').addEventListener('paste', function(e) {
    setTimeout(() => {
        // تنظيف البيانات الملصقة
        let data = this.value;
        // إزالة الأسطر الفارغة الزائدة
        data = data.replace(/\n\s*\n/g, '\n');
        this.value = data.trim();
    }, 100);
});
</script>
</body>
</html>


