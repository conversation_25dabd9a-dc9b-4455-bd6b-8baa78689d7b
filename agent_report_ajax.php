<?php
require_once 'auth.php';
require_once 'config/database_sqlite.php';

$agent_id = $_GET['agent'] ?? '';
$start_date = $_GET['start'] ?? date('Y-m-01');
$end_date = $_GET['end'] ?? date('Y-m-d');

// التحقق من صحة التواريخ
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
    echo '<div class="alert alert-danger">تواريخ غير صحيحة</div>';
    exit;
}

try {
    if (empty($agent_id)) {
        // تقرير جميع الوكلاء
        $stmt = $pdo->prepare("
            SELECT 
                a.agent_name,
                c.collection_date,
                c.gateway_amount,
                c.riyal_mobile_amount,
                (c.gateway_amount + c.riyal_mobile_amount) as total_amount
            FROM collections c
            JOIN agents a ON c.agent_id = a.agent_id
            WHERE c.collection_date BETWEEN ? AND ?
            ORDER BY c.collection_date DESC, a.agent_name
        ");
        $stmt->execute([$start_date, $end_date]);
        $collections = $stmt->fetchAll();
        
        $agent_name = 'جميع الوكلاء';
    } else {
        // تقرير وكيل محدد
        $agent_stmt = $pdo->prepare("SELECT agent_name FROM agents WHERE agent_id = ?");
        $agent_stmt->execute([$agent_id]);
        $agent_info = $agent_stmt->fetch();
        
        if (!$agent_info) {
            echo '<div class="alert alert-danger">الوكيل غير موجود</div>';
            exit;
        }
        
        $agent_name = $agent_info['agent_name'];
        
        $stmt = $pdo->prepare("
            SELECT 
                collection_date,
                gateway_amount,
                riyal_mobile_amount,
                (gateway_amount + riyal_mobile_amount) as total_amount
            FROM collections
            WHERE agent_id = ? AND collection_date BETWEEN ? AND ?
            ORDER BY collection_date DESC
        ");
        $stmt->execute([$agent_id, $start_date, $end_date]);
        $collections = $stmt->fetchAll();
    }

    // حساب الإحصائيات
    $total_gateway = 0;
    $total_riyal = 0;
    $total_amount = 0;
    $days_with_collections = 0;

    foreach ($collections as $collection) {
        $total_gateway += $collection['gateway_amount'] ?? 0;
        $total_riyal += $collection['riyal_mobile_amount'] ?? 0;
        $total_amount += $collection['total_amount'] ?? 0;
        if ($collection['total_amount'] > 0) {
            $days_with_collections++;
        }
    }

    // حساب عدد الأيام في الفترة
    $total_days = (strtotime($end_date) - strtotime($start_date)) / (60*60*24) + 1;
    $daily_average = $days_with_collections > 0 ? $total_amount / $days_with_collections : 0;

    // تنسيق التواريخ للعرض
    $formatted_start = date('d/m/Y', strtotime($start_date));
    $formatted_end = date('d/m/Y', strtotime($end_date));

    ?>
    
    <div class="card">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
                <i class="fas fa-user-chart"></i> 
                تقرير <?= htmlspecialchars($agent_name) ?> من <?= $formatted_start ?> إلى <?= $formatted_end ?>
            </h5>
        </div>
        <div class="card-body">
            <!-- ملخص سريع -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي جيت واي</h6>
                            <h4 class="text-primary"><?= number_format($total_gateway, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي ريال موبايل</h6>
                            <h4 class="text-info"><?= number_format($total_riyal, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الإجمالي العام</h6>
                            <h4 class="text-success"><?= number_format($total_amount, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">أيام التحصيل</h6>
                            <h4 class="text-warning"><?= $days_with_collections ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">المتوسط اليومي</h6>
                            <h4 class="text-secondary"><?= number_format($daily_average, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي الأيام</h6>
                            <h4 class="text-dark"><?= $total_days ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التفاصيل -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <?php if (empty($agent_id)): ?>
                            <th>اسم الوكيل</th>
                            <?php endif; ?>
                            <th>التاريخ</th>
                            <th>اليوم</th>
                            <th>جيت واي</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($collections)): ?>
                        <tr>
                            <td colspan="<?= empty($agent_id) ? '7' : '6' ?>" class="text-center text-muted">
                                لا توجد تحصيلات لهذه الفترة
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($collections as $collection): ?>
                        <?php 
                        $day_names = [
                            'Sunday' => 'الأحد', 'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء',
                            'Wednesday' => 'الأربعاء', 'Thursday' => 'الخميس', 'Friday' => 'الجمعة', 'Saturday' => 'السبت'
                        ];
                        $day_name = $day_names[date('l', strtotime($collection['collection_date']))];
                        ?>
                        <tr class="<?= ($collection['total_amount'] > 0) ? 'table-success' : '' ?>">
                            <?php if (empty($agent_id)): ?>
                            <td><?= htmlspecialchars($collection['agent_name']) ?></td>
                            <?php endif; ?>
                            <td><?= date('d/m/Y', strtotime($collection['collection_date'])) ?></td>
                            <td><?= $day_name ?></td>
                            <td><?= number_format($collection['gateway_amount'] ?? 0, 2) ?></td>
                            <td><?= number_format($collection['riyal_mobile_amount'] ?? 0, 2) ?></td>
                            <td><strong><?= number_format($collection['total_amount'] ?? 0, 2) ?></strong></td>
                            <td>
                                <?php if ($collection['total_amount'] > 0): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">لا يوجد</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <?php if (empty($agent_id)): ?>
                            <th>الإجمالي</th>
                            <?php endif; ?>
                            <th><?= count($collections) ?> سجل</th>
                            <th>-</th>
                            <th><?= number_format($total_gateway, 2) ?></th>
                            <th><?= number_format($total_riyal, 2) ?></th>
                            <th><?= number_format($total_amount, 2) ?></th>
                            <th><?= $days_with_collections ?> يوم</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-3">
                <div class="col-md-6">
                    <?php if ($total_amount == 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            لا توجد تحصيلات لهذه الفترة
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-chart-line"></i> 
                            <strong>إحصائيات الأداء:</strong><br>
                            - إجمالي التحصيلات: <?= number_format($total_amount, 2) ?> ريال<br>
                            - متوسط التحصيل اليومي: <?= number_format($daily_average, 2) ?> ريال<br>
                            - نسبة أيام النشاط: <?= number_format(($days_with_collections / $total_days) * 100, 1) ?>%<br>
                            - أعلى تحصيل: <?= number_format(max(array_column($collections, 'total_amount')), 2) ?> ريال
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <i class="fas fa-info-circle"></i> 
                        <strong>معلومات الفترة:</strong><br>
                        - الوكيل: <?= htmlspecialchars($agent_name) ?><br>
                        - من: <?= $formatted_start ?><br>
                        - إلى: <?= $formatted_end ?><br>
                        - إجمالي الأيام: <?= $total_days ?> يوم<br>
                        - أيام التحصيل: <?= $days_with_collections ?> يوم
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
