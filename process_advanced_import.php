<?php
require_once 'config/database_sqlite.php';

header('Content-Type: application/json');

try {
    // تسجيل محاولة الوصول
    error_log("Advanced import process started at " . date('Y-m-d H:i:s'));

    // قراءة البيانات المرسلة
    $raw_input = file_get_contents('php://input');
    error_log("Raw input received: " . substr($raw_input, 0, 200) . "...");

    $input = json_decode($raw_input, true);

    if (!$input) {
        throw new Exception('لم يتم استلام بيانات صالحة');
    }

    $dataType = $input['dataType'] ?? '';
    $fileData = $input['fileData'] ?? [];
    $combinedDate = $input['combinedDate'] ?? null;

    if (empty($dataType) || empty($fileData)) {
        throw new Exception('بيانات غير مكتملة');
    }

    // جلب الوكلاء من قاعدة البيانات
    $agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents");
    $agents_map = [];
    while ($agent = $agents_stmt->fetch()) {
        $agents_map[trim($agent['agent_name'])] = $agent['agent_id'];
    }

    $imported_count = 0;
    $updated_count = 0;
    $error_count = 0;
    $details = [];

    // تخطي الصف الأول (العناوين)
    for ($row = 1; $row < count($fileData); $row++) {
        $row_data = $fileData[$row];

        if (empty($row_data[0])) {
            continue; // تخطي الصفوف الفارغة
        }

        $agent_name = trim($row_data[0]);

        // البحث عن الوكيل
        $agent_id = null;
        $matched_name = '';

        foreach ($agents_map as $db_name => $db_id) {
            if ($db_name === $agent_name ||
                stripos($db_name, $agent_name) !== false ||
                stripos($agent_name, $db_name) !== false) {
                $agent_id = $db_id;
                $matched_name = $db_name;
                break;
            }
        }

        if (!$agent_id) {
            $error_count++;
            $details[] = "❌ الوكيل '$agent_name' غير موجود في قاعدة البيانات";
            continue;
        }

        if ($dataType === 'combined') {
            // البيانات المختلطة - ليوم واحد فقط
            if (!$combinedDate) {
                $error_count++;
                $details[] = "❌ لم يتم تحديد تاريخ للبيانات المختلطة";
                continue;
            }

            $gateway_amount = 0;
            $riyal_amount = 0;

            // العمود الثاني = البوابة، العمود الثالث = ريال موبايل
            if (isset($row_data[1]) && is_numeric($row_data[1])) {
                $gateway_amount = floatval($row_data[1]);
            }
            if (isset($row_data[2]) && is_numeric($row_data[2])) {
                $riyal_amount = floatval($row_data[2]);
            }

            if ($gateway_amount > 0 || $riyal_amount > 0) {
                // حذف السجل الموجود
                $delete_stmt = $pdo->prepare("DELETE FROM collections WHERE agent_id = ? AND collection_date = ?");
                $delete_stmt->execute([$agent_id, $combinedDate]);

                // إدراج السجل الجديد
                $insert_stmt = $pdo->prepare("
                    INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date)
                    VALUES (?, ?, ?, ?)
                ");

                if ($insert_stmt->execute([$agent_id, $gateway_amount, $riyal_amount, $combinedDate])) {
                    $imported_count++;
                    $details[] = "✅ $matched_name: البوابة " . number_format($gateway_amount, 2) .
                                " + ريال موبايل " . number_format($riyal_amount, 2) . " في $combinedDate";
                } else {
                    $error_count++;
                    $details[] = "❌ فشل في حفظ بيانات $matched_name";
                }
            }

        } else {
            // بيانات منفردة (البوابة أو ريال موبايل) - عدة أيام
            $row_imported = 0;
            $row_updated = 0;

            for ($col = 1; $col < count($row_data); $col++) {
                $amount = trim($row_data[$col]);

                if (empty($amount) || !is_numeric($amount) || floatval($amount) <= 0) {
                    continue;
                }

                // تحديد التاريخ بناءً على العمود
                $days_back = $col - 1;
                $date = date('Y-m-d', strtotime("-$days_back days"));

                if ($dataType === 'riyal') {
                    // ريال موبايل: تحديث أو إنشاء
                    $check_stmt = $pdo->prepare("SELECT id, gateway_amount FROM collections WHERE agent_id = ? AND collection_date = ?");
                    $check_stmt->execute([$agent_id, $date]);
                    $existing = $check_stmt->fetch();

                    if ($existing) {
                        // تحديث السجل الموجود
                        $update_stmt = $pdo->prepare("UPDATE collections SET riyal_mobile_amount = ? WHERE agent_id = ? AND collection_date = ?");
                        if ($update_stmt->execute([floatval($amount), $agent_id, $date])) {
                            $updated_count++;
                            $row_updated++;
                            $details[] = "🔄 $matched_name: تحديث ريال موبايل " . number_format($amount, 2) . " في $date";
                        } else {
                            $error_count++;
                            $details[] = "❌ فشل في تحديث ريال موبايل لـ $matched_name في $date";
                        }
                    } else {
                        // إنشاء سجل جديد
                        $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, 0, ?, ?)");
                        if ($insert_stmt->execute([$agent_id, floatval($amount), $date])) {
                            $imported_count++;
                            $row_imported++;
                            $details[] = "✅ $matched_name: إنشاء سجل جديد ريال موبايل " . number_format($amount, 2) . " في $date";
                        } else {
                            $error_count++;
                            $details[] = "❌ فشل في إنشاء سجل ريال موبايل لـ $matched_name في $date";
                        }
                    }

                } else { // gateway
                    // البوابة: حذف وإنشاء جديد
                    $delete_stmt = $pdo->prepare("DELETE FROM collections WHERE agent_id = ? AND collection_date = ?");
                    $delete_stmt->execute([$agent_id, $date]);

                    $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, ?, 0, ?)");
                    if ($insert_stmt->execute([$agent_id, floatval($amount), $date])) {
                        $imported_count++;
                        $row_imported++;
                        $details[] = "✅ $matched_name: إنشاء سجل البوابة " . number_format($amount, 2) . " في $date";
                    } else {
                        $error_count++;
                        $details[] = "❌ فشل في إنشاء سجل البوابة لـ $matched_name في $date";
                    }
                }
            }

            if ($row_imported > 0 || $row_updated > 0) {
                $type_name = $dataType === 'riyal' ? 'ريال موبايل' : 'البوابة';
                $details[] = "✅ $matched_name ($type_name): $row_imported سجل جديد، $row_updated سجل محدث";
            }
        }
    }

    // حساب الإحصائيات النهائية
    $summary = $pdo->query("
        SELECT
            COUNT(*) as total_records,
            SUM(gateway_amount) as total_gateway,
            SUM(riyal_mobile_amount) as total_riyal,
            SUM(gateway_amount + riyal_mobile_amount) as grand_total
        FROM collections
    ")->fetch();

    echo json_encode([
        'success' => true,
        'imported' => $imported_count,
        'updated' => $updated_count,
        'errors' => $error_count,
        'total' => $imported_count + $updated_count,
        'details' => $details,
        'summary' => [
            'total_records' => $summary['total_records'],
            'total_gateway' => number_format($summary['total_gateway'], 2),
            'total_riyal' => number_format($summary['total_riyal'], 2),
            'grand_total' => number_format($summary['grand_total'], 2)
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
