<?php
require_once 'auth.php';
require_once 'config/database_sqlite.php';

// التحقق من صلاحيات المدير
checkPermission('admin');

$message = '';
$error = '';

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_user'])) {
    $employee_name = trim($_POST['employee_name']);
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $permissions = $_POST['permissions'];

    if (empty($employee_name) || empty($username) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        try {
            // التحقق من عدم وجود اسم المستخدم
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $check_stmt->execute([$username]);

            if ($check_stmt->fetchColumn() > 0) {
                $error = 'اسم المستخدم موجود بالفعل';
            } else {
                // إضافة المستخدم الجديد
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO users (employee_name, username, password, permissions) VALUES (?, ?, ?, ?)");
                $stmt->execute([$employee_name, $username, $hashed_password, $permissions]);
                $message = 'تم إضافة المستخدم بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// معالجة حذف مستخدم
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $user_id = $_GET['delete'];

    // منع حذف المستخدم الحالي
    if ($user_id != $_SESSION['user_id']) {
        try {
            $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $message = 'تم حذف المستخدم بنجاح';
        } catch (PDOException $e) {
            $error = 'خطأ في حذف المستخدم';
        }
    } else {
        $error = 'لا يمكن حذف حسابك الشخصي';
    }
}

// جلب جميع المستخدمين
$users_stmt = $pdo->query("SELECT * FROM users ORDER BY created_at DESC");
$users = $users_stmt->fetchAll();
?>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h5>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>

                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">اسم الموظف:</label>
                        <input type="text" name="employee_name" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم:</label>
                        <input type="text" name="username" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور:</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الصلاحيات:</label>
                        <select name="permissions" class="form-control" required>
                            <option value="viewer">مشاهد</option>
                            <option value="user" selected>مستخدم</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>

                    <button type="submit" name="add_user" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة المستخدم
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> قائمة المستخدمين</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الموظف</th>
                                <th>اسم المستخدم</th>
                                <th>الصلاحيات</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?= htmlspecialchars($user['employee_name']) ?></td>
                                <td><?= htmlspecialchars($user['username']) ?></td>
                                <td>
                                    <?php
                                    $permission_labels = [
                                        'admin' => '<span class="badge bg-danger">مدير</span>',
                                        'user' => '<span class="badge bg-primary">مستخدم</span>',
                                        'viewer' => '<span class="badge bg-secondary">مشاهد</span>'
                                    ];
                                    echo $permission_labels[$user['permissions']] ?? $user['permissions'];
                                    ?>
                                </td>
                                <td>
                                    <?= $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'لم يسجل دخول' ?>
                                </td>
                                <td>
                                    <?php if ($user['user_id'] != $_SESSION['user_id']): ?>
                                        <a href="?delete=<?= $user['user_id'] ?>"
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل تريد حذف هذا المستخدم؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php else: ?>
                                        <span class="badge bg-info">أنت</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
