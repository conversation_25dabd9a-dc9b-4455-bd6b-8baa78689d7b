<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>حالة قاعدة البيانات الجديدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-card { transition: all 0.3s; }
        .status-card:hover { transform: translateY(-2px); }
        .table-sm th, .table-sm td { padding: 0.3rem; font-size: 0.85em; }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <div class="alert alert-success text-center">
        <h1><i class="fas fa-database"></i> حالة قاعدة البيانات الجديدة</h1>
        <p>تم إنشاء الهيكل الجديد بنجاح - فحص شامل للجداول والبيانات</p>
    </div>

    <?php
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // فحص الجداول الموجودة
        $tables = $pdo->query("SHOW TABLES")->fetchAll();
        $table_names = array_map(function($table) { return array_values($table)[0]; }, $tables);
        
        // الجداول المطلوبة
        $required_tables = ['users', 'agents', 'days', 'gateway', 'riyal_mobile'];
        $old_tables = ['collections']; // الجداول القديمة التي يجب أن تكون محذوفة
        
        echo "<div class='row mb-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-primary text-white'>";
        echo "<h5><i class='fas fa-check-circle'></i> فحص الجداول المطلوبة</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        
        foreach ($required_tables as $table) {
            $exists = in_array($table, $table_names);
            $icon = $exists ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';
            $status = $exists ? 'موجود' : 'مفقود';
            $color = $exists ? 'success' : 'danger';
            
            echo "<div class='col-md-2'>";
            echo "<div class='card status-card border-$color'>";
            echo "<div class='card-body text-center'>";
            echo "<i class='$icon fa-2x'></i>";
            echo "<h6 class='mt-2'>$table</h6>";
            echo "<span class='badge bg-$color'>$status</span>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // فحص الجداول القديمة المحذوفة
        echo "<div class='row mb-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-warning text-dark'>";
        echo "<h5><i class='fas fa-trash'></i> فحص الجداول المحذوفة</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        foreach ($old_tables as $table) {
            $exists = in_array($table, $table_names);
            $icon = $exists ? 'fas fa-exclamation-triangle text-warning' : 'fas fa-check-circle text-success';
            $status = $exists ? 'ما زال موجود' : 'تم حذفه';
            $color = $exists ? 'warning' : 'success';
            
            echo "<div class='alert alert-$color'>";
            echo "<i class='$icon'></i> جدول <strong>$table</strong>: $status";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // إحصائيات الجداول
        echo "<div class='row mb-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h5><i class='fas fa-chart-bar'></i> إحصائيات البيانات</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        
        foreach ($required_tables as $table) {
            if (in_array($table, $table_names)) {
                try {
                    $count = $pdo->query("SELECT COUNT(*) as count FROM `$table`")->fetch()['count'];
                    
                    $icon_map = [
                        'users' => 'fas fa-users',
                        'agents' => 'fas fa-building',
                        'days' => 'fas fa-calendar',
                        'gateway' => 'fas fa-door-open',
                        'riyal_mobile' => 'fas fa-mobile-alt'
                    ];
                    
                    $icon = $icon_map[$table] ?? 'fas fa-table';
                    
                    echo "<div class='col-md-2'>";
                    echo "<div class='text-center'>";
                    echo "<i class='$icon fa-2x text-primary'></i>";
                    echo "<h4 class='mt-2'>$count</h4>";
                    echo "<small>$table</small>";
                    echo "</div>";
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='col-md-2'>";
                    echo "<div class='text-center'>";
                    echo "<i class='fas fa-exclamation-triangle fa-2x text-danger'></i>";
                    echo "<h4 class='mt-2'>خطأ</h4>";
                    echo "<small>$table</small>";
                    echo "</div>";
                    echo "</div>";
                }
            }
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // تفاصيل الجداول الجديدة
        $new_tables = ['days', 'gateway', 'riyal_mobile'];
        
        foreach ($new_tables as $table) {
            if (in_array($table, $table_names)) {
                echo "<div class='row mb-4'>";
                echo "<div class='col-md-6'>";
                echo "<div class='card'>";
                
                $table_titles = [
                    'days' => 'جدول الأيام',
                    'gateway' => 'جدول البوابة', 
                    'riyal_mobile' => 'جدول ريال موبايل'
                ];
                
                $table_colors = [
                    'days' => 'info',
                    'gateway' => 'warning',
                    'riyal_mobile' => 'danger'
                ];
                
                $color = $table_colors[$table];
                $title = $table_titles[$table];
                
                echo "<div class='card-header bg-$color text-white'>";
                echo "<h6>🏗️ بنية $title</h6>";
                echo "</div>";
                echo "<div class='card-body'>";
                
                try {
                    $columns = $pdo->query("DESCRIBE `$table`")->fetchAll();
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm'>";
                    echo "<thead><tr><th>العمود</th><th>النوع</th><th>مطلوب</th><th>مفتاح</th></tr></thead>";
                    echo "<tbody>";
                    
                    foreach ($columns as $column) {
                        echo "<tr>";
                        echo "<td><strong>" . $column['Field'] . "</strong></td>";
                        echo "<td><small>" . $column['Type'] . "</small></td>";
                        echo "<td>" . ($column['Null'] == 'NO' ? '✅' : '❌') . "</td>";
                        echo "<td>" . ($column['Key'] ? $column['Key'] : '-') . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>خطأ في قراءة بنية الجدول</div>";
                }
                
                echo "</div>";
                echo "</div>";
                echo "</div>";
                
                // عينة من البيانات
                echo "<div class='col-md-6'>";
                echo "<div class='card'>";
                echo "<div class='card-header bg-light'>";
                echo "<h6>📊 عينة من البيانات</h6>";
                echo "</div>";
                echo "<div class='card-body'>";
                
                try {
                    $sample = $pdo->query("SELECT * FROM `$table` LIMIT 5")->fetchAll();
                    
                    if (!empty($sample)) {
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr>";
                        foreach (array_keys($sample[0]) as $column) {
                            echo "<th><small>$column</small></th>";
                        }
                        echo "</tr></thead>";
                        echo "<tbody>";
                        
                        foreach ($sample as $row) {
                            echo "<tr>";
                            foreach ($row as $value) {
                                echo "<td><small>" . htmlspecialchars($value ?? '') . "</small></td>";
                            }
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-info'>لا توجد بيانات</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-warning'>خطأ في قراءة البيانات</div>";
                }
                
                echo "</div>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
        }
        
        // فحص صلاحيات المستخدمين
        echo "<div class='row mb-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h5><i class='fas fa-user-shield'></i> صلاحيات المستخدمين الجديدة</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        try {
            $users = $pdo->query("SELECT username, employee_name, permissions, can_view_all, can_add_data, can_edit_data, can_delete_data, is_active FROM users")->fetchAll();
            
            echo "<div class='table-responsive'>";
            echo "<table class='table table-striped'>";
            echo "<thead>";
            echo "<tr>";
            echo "<th>المستخدم</th>";
            echo "<th>النوع</th>";
            echo "<th>مشاهدة</th>";
            echo "<th>إضافة</th>";
            echo "<th>تعديل</th>";
            echo "<th>حذف</th>";
            echo "<th>نشط</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td><strong>" . $user['employee_name'] . "</strong><br><small>" . $user['username'] . "</small></td>";
                echo "<td><span class='badge bg-" . ($user['permissions'] == 'admin' ? 'danger' : 'primary') . "'>" . $user['permissions'] . "</span></td>";
                echo "<td>" . ($user['can_view_all'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['can_add_data'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['can_edit_data'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['can_delete_data'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['is_active'] ? '✅' : '❌') . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody></table>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>خطأ في قراءة بيانات المستخدمين</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // أزرار التحكم
        echo "<div class='row'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-dark text-white'>";
        echo "<h5><i class='fas fa-tools'></i> أدوات إدارة النظام</h5>";
        echo "</div>";
        echo "<div class='card-body text-center'>";
        echo "<div class='row'>";
        
        echo "<div class='col-md-2'>";
        echo "<a href='/add_gateway_data.php' class='btn btn-warning w-100'>";
        echo "<i class='fas fa-door-open'></i><br>بيانات البوابة";
        echo "</a>";
        echo "</div>";
        
        echo "<div class='col-md-2'>";
        echo "<a href='/add_riyal_data.php' class='btn btn-danger w-100'>";
        echo "<i class='fas fa-mobile-alt'></i><br>بيانات ريال موبايل";
        echo "</a>";
        echo "</div>";
        
        echo "<div class='col-md-2'>";
        echo "<a href='/manage_users.php' class='btn btn-primary w-100'>";
        echo "<i class='fas fa-users'></i><br>إدارة المستخدمين";
        echo "</a>";
        echo "</div>";
        
        echo "<div class='col-md-2'>";
        echo "<a href='/daily_report_new.php' class='btn btn-info w-100'>";
        echo "<i class='fas fa-chart-bar'></i><br>التقرير اليومي";
        echo "</a>";
        echo "</div>";
        
        echo "<div class='col-md-2'>";
        echo "<a href='/view_new_structure.php' class='btn btn-secondary w-100'>";
        echo "<i class='fas fa-sitemap'></i><br>عرض الهيكل";
        echo "</a>";
        echo "</div>";
        
        echo "<div class='col-md-2'>";
        echo "<a href='/' class='btn btn-success w-100'>";
        echo "<i class='fas fa-home'></i><br>الصفحة الرئيسية";
        echo "</a>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // ملخص نهائي
        $total_tables = count($required_tables);
        $existing_tables = count(array_intersect($required_tables, $table_names));
        $success_rate = ($existing_tables / $total_tables) * 100;
        
        echo "<div class='row mt-4'>";
        echo "<div class='col-12'>";
        echo "<div class='alert alert-" . ($success_rate == 100 ? 'success' : 'warning') . " text-center'>";
        echo "<h4><i class='fas fa-chart-pie'></i> ملخص حالة قاعدة البيانات</h4>";
        echo "<p><strong>معدل النجاح:</strong> " . number_format($success_rate, 1) . "%</p>";
        echo "<p><strong>الجداول الموجودة:</strong> $existing_tables من $total_tables</p>";
        
        if ($success_rate == 100) {
            echo "<p class='text-success'><i class='fas fa-check-circle'></i> جميع الجداول المطلوبة موجودة والنظام جاهز للاستخدام!</p>";
        } else {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle'></i> بعض الجداول مفقودة - يرجى إعادة تشغيل إنشاء قاعدة البيانات</p>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ خطأ في الاتصال بقاعدة البيانات</h5>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
</div>

</body>
</html>
