<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>عرض هيكل قاعدة البيانات الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-container { max-height: 300px; overflow-y: auto; }
        .table th { position: sticky; top: 0; background: #f8f9fa; }
        .schema-table { font-size: 0.9em; }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-sitemap"></i> هيكل قاعدة البيانات الجديد</h2>
        <p>عرض الجداول الجديدة والعلاقات بينها</p>
    </div>

    <?php
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ متصل بقاعدة البيانات: collections_system</h5>";
        echo "</div>";
        
        // عرض جميع الجداول
        $tables = $pdo->query("SHOW TABLES")->fetchAll();
        
        echo "<div class='row'>";
        echo "<div class='col-md-12'>";
        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h5>📋 الجداول الموجودة</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        
        foreach ($tables as $table) {
            $table_name = array_values($table)[0];
            $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
            
            $icon = '';
            $color = '';
            switch ($table_name) {
                case 'users': $icon = 'fas fa-users'; $color = 'primary'; break;
                case 'agents': $icon = 'fas fa-building'; $color = 'success'; break;
                case 'days': $icon = 'fas fa-calendar'; $color = 'info'; break;
                case 'gateway': $icon = 'fas fa-door-open'; $color = 'warning'; break;
                case 'riyal_mobile': $icon = 'fas fa-mobile-alt'; $color = 'danger'; break;
                default: $icon = 'fas fa-table'; $color = 'secondary';
            }
            
            echo "<div class='col-md-2'>";
            echo "<div class='alert alert-$color text-center'>";
            echo "<i class='$icon fa-2x'></i>";
            echo "<h6 class='mt-2'>$table_name</h6>";
            echo "<h4>$count</h4>";
            echo "<small>سجل</small>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // عرض تفاصيل كل جدول
        $table_details = [
            'days' => ['title' => 'جدول الأيام', 'icon' => 'fas fa-calendar', 'color' => 'info'],
            'users' => ['title' => 'جدول المستخدمين', 'icon' => 'fas fa-users', 'color' => 'primary'],
            'agents' => ['title' => 'جدول الوكلاء', 'icon' => 'fas fa-building', 'color' => 'success'],
            'gateway' => ['title' => 'جدول البوابة', 'icon' => 'fas fa-door-open', 'color' => 'warning'],
            'riyal_mobile' => ['title' => 'جدول ريال موبايل', 'icon' => 'fas fa-mobile-alt', 'color' => 'danger']
        ];
        
        foreach ($table_details as $table_name => $details) {
            if (in_array($table_name, array_column($tables, 0))) {
                echo "<div class='row mt-4'>";
                echo "<div class='col-md-6'>";
                echo "<div class='card'>";
                echo "<div class='card-header bg-{$details['color']} text-white'>";
                echo "<h5><i class='{$details['icon']}'></i> {$details['title']}</h5>";
                echo "</div>";
                echo "<div class='card-body'>";
                
                // عرض بنية الجدول
                $columns = $pdo->query("DESCRIBE `$table_name`")->fetchAll();
                echo "<div class='table-container'>";
                echo "<table class='table table-sm schema-table'>";
                echo "<thead>";
                echo "<tr><th>العمود</th><th>النوع</th><th>مطلوب</th><th>مفتاح</th><th>افتراضي</th></tr>";
                echo "</thead>";
                echo "<tbody>";
                
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td><strong>" . $column['Field'] . "</strong></td>";
                    echo "<td>" . $column['Type'] . "</td>";
                    echo "<td>" . ($column['Null'] == 'NO' ? 'نعم' : 'لا') . "</td>";
                    echo "<td>" . $column['Key'] . "</td>";
                    echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                
                echo "</tbody></table>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                
                // عرض عينة من البيانات
                echo "<div class='col-md-6'>";
                echo "<div class='card'>";
                echo "<div class='card-header bg-light'>";
                echo "<h6>📊 عينة من البيانات</h6>";
                echo "</div>";
                echo "<div class='card-body'>";
                
                try {
                    $sample_data = $pdo->query("SELECT * FROM `$table_name` LIMIT 5")->fetchAll();
                    
                    if (!empty($sample_data)) {
                        echo "<div class='table-container'>";
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr>";
                        foreach (array_keys($sample_data[0]) as $column) {
                            echo "<th>" . $column . "</th>";
                        }
                        echo "</tr></thead>";
                        echo "<tbody>";
                        
                        foreach ($sample_data as $row) {
                            echo "<tr>";
                            foreach ($row as $value) {
                                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                            }
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-info'>لا توجد بيانات</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-warning'>خطأ في قراءة البيانات</div>";
                }
                
                echo "</div>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
        }
        
        // عرض العلاقات
        echo "<div class='row mt-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-dark text-white'>";
        echo "<h5>🔗 العلاقات بين الجداول</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<h6>العلاقات الأساسية:</h6>";
        echo "<ul class='list-group'>";
        echo "<li class='list-group-item'>";
        echo "<strong>gateway.agent_id</strong> → <strong>agents.agent_id</strong>";
        echo "<br><small class='text-muted'>كل سجل بوابة مرتبط بوكيل</small>";
        echo "</li>";
        echo "<li class='list-group-item'>";
        echo "<strong>gateway.day_id</strong> → <strong>days.day_id</strong>";
        echo "<br><small class='text-muted'>كل سجل بوابة مرتبط بيوم</small>";
        echo "</li>";
        echo "<li class='list-group-item'>";
        echo "<strong>riyal_mobile.agent_id</strong> → <strong>agents.agent_id</strong>";
        echo "<br><small class='text-muted'>كل سجل ريال موبايل مرتبط بوكيل</small>";
        echo "</li>";
        echo "<li class='list-group-item'>";
        echo "<strong>riyal_mobile.day_id</strong> → <strong>days.day_id</strong>";
        echo "<br><small class='text-muted'>كل سجل ريال موبايل مرتبط بيوم</small>";
        echo "</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='col-md-6'>";
        echo "<h6>علاقات المستخدمين:</h6>";
        echo "<ul class='list-group'>";
        echo "<li class='list-group-item'>";
        echo "<strong>gateway.created_by</strong> → <strong>users.user_id</strong>";
        echo "<br><small class='text-muted'>من أنشأ السجل</small>";
        echo "</li>";
        echo "<li class='list-group-item'>";
        echo "<strong>gateway.updated_by</strong> → <strong>users.user_id</strong>";
        echo "<br><small class='text-muted'>من عدل السجل</small>";
        echo "</li>";
        echo "<li class='list-group-item'>";
        echo "<strong>riyal_mobile.created_by</strong> → <strong>users.user_id</strong>";
        echo "<br><small class='text-muted'>من أنشأ السجل</small>";
        echo "</li>";
        echo "<li class='list-group-item'>";
        echo "<strong>riyal_mobile.updated_by</strong> → <strong>users.user_id</strong>";
        echo "<br><small class='text-muted'>من عدل السجل</small>";
        echo "</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // عرض الصلاحيات الجديدة
        echo "<div class='row mt-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h5>🔐 صلاحيات المستخدمين الجديدة</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $users = $pdo->query("SELECT * FROM users")->fetchAll();
        
        if (!empty($users)) {
            echo "<div class='table-container'>";
            echo "<table class='table table-striped'>";
            echo "<thead>";
            echo "<tr>";
            echo "<th>المستخدم</th>";
            echo "<th>الصلاحية</th>";
            echo "<th>مشاهدة شاملة</th>";
            echo "<th>إضافة</th>";
            echo "<th>تعديل</th>";
            echo "<th>حذف</th>";
            echo "<th>نشط</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td><strong>" . $user['employee_name'] . "</strong><br><small>" . $user['username'] . "</small></td>";
                echo "<td><span class='badge bg-" . ($user['permissions'] == 'admin' ? 'danger' : 'primary') . "'>" . $user['permissions'] . "</span></td>";
                echo "<td>" . ($user['can_view_all'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['can_add_data'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['can_edit_data'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['can_delete_data'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($user['is_active'] ? '✅' : '❌') . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody></table>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        // روابط مفيدة
        echo "<div class='row mt-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-secondary text-white'>";
        echo "<h5>🔗 أدوات إدارة البيانات</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        
        echo "<div class='col-md-3'>";
        echo "<h6>إدارة البيانات:</h6>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='add_gateway_data.php' class='btn btn-warning'>إضافة بيانات البوابة</a>";
        echo "<a href='add_riyal_data.php' class='btn btn-danger'>إضافة بيانات ريال موبايل</a>";
        echo "<a href='manage_days.php' class='btn btn-info'>إدارة الأيام</a>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<h6>إدارة المستخدمين:</h6>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='manage_users.php' class='btn btn-primary'>إدارة المستخدمين</a>";
        echo "<a href='user_permissions.php' class='btn btn-secondary'>إدارة الصلاحيات</a>";
        echo "<a href='assign_agents.php' class='btn btn-success'>تخصيص الوكلاء</a>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<h6>التقارير:</h6>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='daily_report_new.php' class='btn btn-info'>التقرير اليومي</a>";
        echo "<a href='monthly_report.php' class='btn btn-success'>التقرير الشهري</a>";
        echo "<a href='agent_report.php' class='btn btn-warning'>تقرير الوكلاء</a>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<h6>أدوات أخرى:</h6>";
        echo "<div class='d-grid gap-2'>";
        echo "<a href='backup_new_structure.php' class='btn btn-dark'>نسخ احتياطي</a>";
        echo "<a href='import_old_data.php' class='btn btn-warning'>استيراد البيانات القديمة</a>";
        echo "<a href='main.php' class='btn btn-primary'>الصفحة الرئيسية</a>";
        echo "</div>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ خطأ في الاتصال بقاعدة البيانات</h5>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
</div>

</body>
</html>
