<?php
$passwords = [
    '' => 'فارغة (افتراضي XAMPP)',
    'root' => 'root',
    'password' => 'password', 
    'admin' => 'admin',
    '123456' => '123456',
    'mysql' => 'mysql'
];

$successful_connection = null;

echo "<h6>نتائج اختبار كلمات المرور:</h6>";

foreach ($passwords as $password => $description) {
    try {
        $pdo = new PDO("mysql:host=localhost;port=3306", 'root', $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p class='text-success'><i class='fas fa-check'></i> <strong>نجح:</strong> $description</p>";
        $successful_connection = $password;
        break;
        
    } catch (PDOException $e) {
        echo "<p class='text-danger'><i class='fas fa-times'></i> <strong>فشل:</strong> $description</p>";
    }
}

if ($successful_connection !== null) {
    echo "<div class='alert alert-success mt-3'>";
    echo "<h6>🎉 تم العثور على كلمة المرور الصحيحة!</h6>";
    echo "<p><strong>كلمة المرور:</strong> " . ($successful_connection === '' ? '(فارغة)' : $successful_connection) . "</p>";
    echo "<button onclick='useThisPassword(\"$successful_connection\")' class='btn btn-success'>استخدام هذه كلمة المرور</button>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-3'>";
    echo "<h6>❌ لم يتم العثور على كلمة مرور صحيحة</h6>";
    echo "<p>المشاكل المحتملة:</p>";
    echo "<ul>";
    echo "<li>MySQL غير مشغل</li>";
    echo "<li>كلمة مرور مختلفة عن المعتاد</li>";
    echo "<li>مشكلة في إعدادات MySQL</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<script>";
echo "function useThisPassword(password) {";
echo "  localStorage.setItem('mysql_password', password);";
echo "  alert('تم حفظ كلمة المرور. يمكنك الآن إنشاء قاعدة البيانات.');";
echo "  location.reload();";
echo "}";
echo "</script>";
?>
