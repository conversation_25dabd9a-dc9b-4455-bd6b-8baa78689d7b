<?php
try {
    echo "<div class='card'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5><i class='fas fa-table'></i> جداول قاعدة البيانات</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    // جلب جميع الجداول
    $tables = $pdo->query("
        SELECT name, sql 
        FROM sqlite_master 
        WHERE type='table' 
        ORDER BY name
    ")->fetchAll();
    
    if (empty($tables)) {
        echo "<div class='alert alert-warning'>";
        echo "<h6>لا توجد جداول</h6>";
        echo "<p>قاعدة البيانات فارغة أو لا تحتوي على جداول.</p>";
        echo "</div>";
    } else {
        foreach ($tables as $table) {
            $table_name = $table['name'];
            
            // جلب معلومات الجدول
            $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
            
            // جلب أعمدة الجدول
            $columns = $pdo->query("PRAGMA table_info(`$table_name`)")->fetchAll();
            
            echo "<div class='card mb-3'>";
            echo "<div class='card-header d-flex justify-content-between align-items-center'>";
            echo "<h6 class='mb-0'>";
            echo "<i class='fas fa-table'></i> $table_name";
            echo "<span class='badge bg-secondary ms-2'>$count سجل</span>";
            echo "</h6>";
            echo "<div>";
            echo "<a href='?action=table&table=$table_name' class='btn btn-sm btn-outline-primary'>";
            echo "<i class='fas fa-eye'></i> عرض البيانات";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            
            echo "<div class='card-body'>";
            
            // عرض الأعمدة
            echo "<h6>الأعمدة:</h6>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm table-striped'>";
            echo "<thead class='table-dark'>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>افتراضي</th><th>مفتاح أساسي</th></tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td><code>{$column['name']}</code></td>";
                echo "<td><span class='badge bg-info'>{$column['type']}</span></td>";
                echo "<td>" . ($column['notnull'] ? '<span class="text-danger">NO</span>' : '<span class="text-success">YES</span>') . "</td>";
                echo "<td>" . ($column['dflt_value'] ? "<code>{$column['dflt_value']}</code>" : '<span class="text-muted">-</span>') . "</td>";
                echo "<td>" . ($column['pk'] ? '<i class="fas fa-key text-warning"></i>' : '-') . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody>";
            echo "</table>";
            echo "</div>";
            
            // عرض عينة من البيانات
            if ($count > 0) {
                echo "<h6>عينة من البيانات (أول 5 سجلات):</h6>";
                $sample_data = $pdo->query("SELECT * FROM `$table_name` LIMIT 5")->fetchAll();
                
                echo "<div class='table-responsive'>";
                echo "<table class='table table-sm table-hover'>";
                echo "<thead class='table-light'>";
                echo "<tr>";
                foreach ($columns as $column) {
                    echo "<th>{$column['name']}</th>";
                }
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                foreach ($sample_data as $row) {
                    echo "<tr>";
                    foreach ($columns as $column) {
                        $value = $row[$column['name']];
                        if (is_null($value)) {
                            echo "<td><span class='text-muted'>NULL</span></td>";
                        } elseif (is_numeric($value)) {
                            echo "<td><span class='text-primary'>$value</span></td>";
                        } else {
                            $display_value = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                            echo "<td>" . htmlspecialchars($display_value) . "</td>";
                        }
                    }
                    echo "</tr>";
                }
                
                echo "</tbody>";
                echo "</table>";
                echo "</div>";
                
                if ($count > 5) {
                    echo "<p class='text-muted'><small>عرض 5 من أصل $count سجل. <a href='?action=table&table=$table_name'>عرض الكل</a></small></p>";
                }
            } else {
                echo "<div class='alert alert-info'>";
                echo "<i class='fas fa-info-circle'></i> الجدول فارغ - لا يحتوي على بيانات";
                echo "</div>";
            }
            
            echo "</div>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ في قراءة الجداول</h5>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
