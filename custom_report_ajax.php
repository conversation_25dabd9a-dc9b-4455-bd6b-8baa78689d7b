<?php
require_once 'auth.php';
require_once 'config/database_sqlite.php';

$type = $_GET['type'] ?? 'summary';
$start_date = $_GET['start'] ?? date('Y-m-01');
$end_date = $_GET['end'] ?? date('Y-m-d');

// التحقق من صحة التواريخ
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
    echo '<div class="alert alert-danger">تواريخ غير صحيحة</div>';
    exit;
}

try {
    $formatted_start = date('d/m/Y', strtotime($start_date));
    $formatted_end = date('d/m/Y', strtotime($end_date));

    switch ($type) {
        case 'summary':
            // تقرير ملخص إجمالي
            $stmt = $pdo->prepare("
                SELECT
                    a.agent_name,
                    SUM(c.gateway_amount) as total_gateway,
                    SUM(c.riyal_mobile_amount) as total_riyal,
                    SUM(c.gateway_amount + c.riyal_mobile_amount) as total_amount,
                    COUNT(c.id) as days_count,
                    AVG(c.gateway_amount + c.riyal_mobile_amount) as avg_amount
                FROM agents a
                LEFT JOIN collections c ON a.agent_id = c.agent_id
                    AND c.collection_date BETWEEN ? AND ?
                GROUP BY a.agent_id, a.agent_name
                ORDER BY total_amount DESC
            ");
            $stmt->execute([$start_date, $end_date]);
            $data = $stmt->fetchAll();

            echo '<div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-chart-pie"></i> التقرير الإجمالي من ' . $formatted_start . ' إلى ' . $formatted_end . '</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الوكيل</th>
                                        <th>البوابة</th>
                                        <th>ريال موبايل</th>
                                        <th>الإجمالي</th>
                                        <th>عدد الأيام</th>
                                        <th>المتوسط</th>
                                    </tr>
                                </thead>
                                <tbody>';

            $total_all = 0;
            foreach ($data as $row) {
                $total_all += $row['total_amount'] ?? 0;
                echo '<tr>
                        <td>' . htmlspecialchars($row['agent_name']) . '</td>
                        <td>' . number_format($row['total_gateway'] ?? 0, 2) . '</td>
                        <td>' . number_format($row['total_riyal'] ?? 0, 2) . '</td>
                        <td><strong>' . number_format($row['total_amount'] ?? 0, 2) . '</strong></td>
                        <td>' . ($row['days_count'] ?? 0) . '</td>
                        <td>' . number_format($row['avg_amount'] ?? 0, 2) . '</td>
                      </tr>';
            }

            echo '      </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th>الإجمالي العام</th>
                                <th colspan="2">-</th>
                                <th>' . number_format($total_all, 2) . '</th>
                                <th colspan="2">-</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>';
            break;

        case 'detailed':
            // تقرير تفصيلي
            $stmt = $pdo->prepare("
                SELECT
                    a.agent_name,
                    c.collection_date,
                    c.gateway_amount,
                    c.riyal_mobile_amount,
                    (c.gateway_amount + c.riyal_mobile_amount) as total_amount
                FROM collections c
                JOIN agents a ON c.agent_id = a.agent_id
                WHERE c.collection_date BETWEEN ? AND ?
                ORDER BY c.collection_date DESC, a.agent_name
            ");
            $stmt->execute([$start_date, $end_date]);
            $data = $stmt->fetchAll();

            echo '<div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-list-alt"></i> التقرير التفصيلي من ' . $formatted_start . ' إلى ' . $formatted_end . '</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الوكيل</th>
                                        <th>البوابة</th>
                                        <th>ريال موبايل</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>';

            $total_detailed = 0;
            foreach ($data as $row) {
                $total_detailed += $row['total_amount'];
                echo '<tr>
                        <td>' . date('d/m/Y', strtotime($row['collection_date'])) . '</td>
                        <td>' . htmlspecialchars($row['agent_name']) . '</td>
                        <td>' . number_format($row['gateway_amount'], 2) . '</td>
                        <td>' . number_format($row['riyal_mobile_amount'], 2) . '</td>
                        <td><strong>' . number_format($row['total_amount'], 2) . '</strong></td>
                      </tr>';
            }

            echo '      </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="4">الإجمالي العام</th>
                                <th>' . number_format($total_detailed, 2) . '</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>';
            break;

        case 'comparison':
            // تقرير مقارنة
            $stmt = $pdo->prepare("
                SELECT
                    a.agent_name,
                    SUM(CASE WHEN c.collection_date BETWEEN ? AND ? THEN c.gateway_amount + c.riyal_mobile_amount ELSE 0 END) as current_total,
                    COUNT(CASE WHEN c.collection_date BETWEEN ? AND ? THEN 1 END) as current_days
                FROM agents a
                LEFT JOIN collections c ON a.agent_id = c.agent_id
                GROUP BY a.agent_id, a.agent_name
                ORDER BY current_total DESC
            ");
            $stmt->execute([$start_date, $end_date, $start_date, $end_date]);
            $data = $stmt->fetchAll();

            echo '<div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-balance-scale"></i> تقرير المقارنة من ' . $formatted_start . ' إلى ' . $formatted_end . '</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الترتيب</th>
                                        <th>الوكيل</th>
                                        <th>إجمالي التحصيلات</th>
                                        <th>عدد الأيام</th>
                                        <th>المتوسط اليومي</th>
                                        <th>التقييم</th>
                                    </tr>
                                </thead>
                                <tbody>';

            $rank = 1;
            foreach ($data as $row) {
                $avg = $row['current_days'] > 0 ? $row['current_total'] / $row['current_days'] : 0;
                $rating = '';
                $badge_class = '';

                if ($row['current_total'] > 10000) {
                    $rating = 'ممتاز';
                    $badge_class = 'bg-success';
                } elseif ($row['current_total'] > 5000) {
                    $rating = 'جيد جداً';
                    $badge_class = 'bg-primary';
                } elseif ($row['current_total'] > 2000) {
                    $rating = 'جيد';
                    $badge_class = 'bg-info';
                } elseif ($row['current_total'] > 0) {
                    $rating = 'مقبول';
                    $badge_class = 'bg-warning';
                } else {
                    $rating = 'غير نشط';
                    $badge_class = 'bg-secondary';
                }

                echo '<tr>
                        <td>' . ($row['current_total'] > 0 ? $rank++ : '-') . '</td>
                        <td>' . htmlspecialchars($row['agent_name']) . '</td>
                        <td><strong>' . number_format($row['current_total'], 2) . '</strong></td>
                        <td>' . $row['current_days'] . '</td>
                        <td>' . number_format($avg, 2) . '</td>
                        <td><span class="badge ' . $badge_class . '">' . $rating . '</span></td>
                      </tr>';
            }

            echo '      </tbody>
                    </table>
                </div>
            </div>
        </div>';
            break;

        default:
            echo '<div class="alert alert-danger">نوع تقرير غير مدعوم</div>';
    }

} catch (PDOException $e) {
    echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
