-- نسخة احتياطية من قاعدة بيانات نظام التحصيلات
-- تاريخ الإنشاء: 2025-07-15
-- نوع قاعدة البيانات: PostgreSQL
-- اسم قاعدة البيانات: agent

-- الاتصال بقاعدة البيانات agent
\c agent;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    user_id SERIAL PRIMARY KEY,
    employee_name VARCHAR(255) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    permissions VARCHAR(20) DEFAULT 'user' CHECK (permissions IN ('admin', 'user')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الوكلاء
CREATE TABLE IF NOT EXISTS agents (
    agent_id SERIAL PRIMARY KEY,
    agent_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول التحصيلات
CREATE TABLE IF NOT EXISTS collections (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER REFERENCES agents(agent_id) ON DELETE CASCADE,
    gateway_amount DECIMAL(15,2) DEFAULT 0.00,
    riyal_mobile_amount DECIMAL(15,2) DEFAULT 0.00,
    collection_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_collections_agent_date ON collections(agent_id, collection_date);
CREATE INDEX IF NOT EXISTS idx_collections_date ON collections(collection_date);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_agents_name ON agents(agent_name);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at في جدول التحصيلات
DROP TRIGGER IF EXISTS update_collections_updated_at ON collections;
CREATE TRIGGER update_collections_updated_at
    BEFORE UPDATE ON collections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- إدراج المستخدمين الافتراضيين
INSERT INTO users (employee_name, username, password, permissions) VALUES
('المدير العام', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('مستخدم تجريبي', 'user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user')
ON CONFLICT (username) DO NOTHING;

-- إدراج الوكلاء الافتراضيين
INSERT INTO agents (agent_name) VALUES
('المامون'),
('الشرعبي'),
('عومان'),
('الكون'),
('سكافا'),
('أبو اسامه'),
('أسس'),
('الاثير'),
('المترب'),
('باتكو'),
('بران')
ON CONFLICT DO NOTHING;

-- إدراج بيانات تجريبية للتحصيلات (اختيارية)
INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES
(1, 25000.50, 15000.25, CURRENT_DATE),
(2, 22000.75, 18000.50, CURRENT_DATE),
(3, 18000.25, 12000.75, CURRENT_DATE),
(4, 30000.00, 20000.00, CURRENT_DATE),
(5, 16000.50, 14000.25, CURRENT_DATE),
(6, 28000.75, 22000.50, CURRENT_DATE),
(7, 19000.25, 16000.75, CURRENT_DATE),
(8, 24000.50, 19000.25, CURRENT_DATE),
(9, 21000.75, 17000.50, CURRENT_DATE),
(10, 26000.25, 21000.75, CURRENT_DATE),
(11, 23000.50, 18500.25, CURRENT_DATE);

-- إنشاء views مفيدة
CREATE OR REPLACE VIEW daily_summary AS
SELECT 
    collection_date,
    COUNT(*) as total_records,
    SUM(gateway_amount) as total_gateway,
    SUM(riyal_mobile_amount) as total_riyal,
    SUM(gateway_amount + riyal_mobile_amount) as grand_total
FROM collections
GROUP BY collection_date
ORDER BY collection_date DESC;

CREATE OR REPLACE VIEW agent_summary AS
SELECT 
    a.agent_id,
    a.agent_name,
    COUNT(c.id) as total_records,
    COALESCE(SUM(c.gateway_amount), 0) as total_gateway,
    COALESCE(SUM(c.riyal_mobile_amount), 0) as total_riyal,
    COALESCE(SUM(c.gateway_amount + c.riyal_mobile_amount), 0) as grand_total,
    MAX(c.collection_date) as last_collection_date
FROM agents a
LEFT JOIN collections c ON a.agent_id = c.agent_id
GROUP BY a.agent_id, a.agent_name
ORDER BY grand_total DESC;

-- إنشاء stored functions مفيدة
CREATE OR REPLACE FUNCTION get_daily_report(report_date DATE)
RETURNS TABLE(
    agent_name VARCHAR(255),
    gateway_amount DECIMAL(15,2),
    riyal_mobile_amount DECIMAL(15,2),
    total_amount DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.agent_name,
        COALESCE(c.gateway_amount, 0) as gateway_amount,
        COALESCE(c.riyal_mobile_amount, 0) as riyal_mobile_amount,
        COALESCE(c.gateway_amount + c.riyal_mobile_amount, 0) as total_amount
    FROM agents a
    LEFT JOIN collections c ON a.agent_id = c.agent_id AND c.collection_date = report_date
    ORDER BY a.agent_name;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_agent_report(agent_id_param INTEGER, start_date DATE, end_date DATE)
RETURNS TABLE(
    collection_date DATE,
    gateway_amount DECIMAL(15,2),
    riyal_mobile_amount DECIMAL(15,2),
    total_amount DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.collection_date,
        c.gateway_amount,
        c.riyal_mobile_amount,
        (c.gateway_amount + c.riyal_mobile_amount) as total_amount
    FROM collections c
    WHERE c.agent_id = agent_id_param 
    AND c.collection_date BETWEEN start_date AND end_date
    ORDER BY c.collection_date DESC;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة للحصول على إحصائيات شاملة
CREATE OR REPLACE FUNCTION get_system_stats()
RETURNS TABLE(
    total_users INTEGER,
    total_agents INTEGER,
    total_collections INTEGER,
    total_gateway_amount DECIMAL(15,2),
    total_riyal_amount DECIMAL(15,2),
    grand_total_amount DECIMAL(15,2),
    earliest_date DATE,
    latest_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM users),
        (SELECT COUNT(*)::INTEGER FROM agents),
        (SELECT COUNT(*)::INTEGER FROM collections),
        (SELECT COALESCE(SUM(gateway_amount), 0) FROM collections),
        (SELECT COALESCE(SUM(riyal_mobile_amount), 0) FROM collections),
        (SELECT COALESCE(SUM(gateway_amount + riyal_mobile_amount), 0) FROM collections),
        (SELECT MIN(collection_date) FROM collections),
        (SELECT MAX(collection_date) FROM collections);
END;
$$ LANGUAGE plpgsql;

-- تعليقات مفيدة
-- كلمة مرور المستخدمين الافتراضية: admin123 للمدير، user123 للمستخدم العادي
-- يمكن الوصول لقاعدة البيانات عبر: psql -h localhost -p 5432 -U postgres -d agent
-- أو استخدام pgAdmin أو أي أداة إدارة PostgreSQL أخرى

-- إعطاء صلاحيات (إذا لزم الأمر)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- عرض معلومات قاعدة البيانات
SELECT 'تم إنشاء قاعدة بيانات نظام التحصيلات بنجاح!' as message;
SELECT 'اسم قاعدة البيانات: agent' as database_info;
SELECT 'نوع قاعدة البيانات: PostgreSQL' as database_type;
