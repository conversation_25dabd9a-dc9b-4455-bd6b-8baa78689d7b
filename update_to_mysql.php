<?php
try {
    echo "<div class='alert alert-info'>";
    echo "<h6>🔄 تحديث ملفات النظام...</h6>";
    echo "</div>";
    
    // 1. إنشاء نسخة احتياطية من ملف قاعدة البيانات الحالي
    $current_db_file = __DIR__ . '/config/database_sqlite.php';
    if (file_exists($current_db_file)) {
        $backup_file = __DIR__ . '/config/database_sqlite_backup.php';
        copy($current_db_file, $backup_file);
        echo "<p>✅ تم إنشاء نسخة احتياطية من ملف قاعدة البيانات</p>";
    }
    
    // 2. تحديث ملف الاتصال الرئيسي
    $new_db_content = '<?php
// تحديد نوع قاعدة البيانات المستخدمة
$database_type = "mysql"; // mysql أو sqlite

if ($database_type === "mysql") {
    require_once __DIR__ . "/database_mysql.php";
} else {
    require_once __DIR__ . "/database_sqlite.php";
}
?>';
    
    file_put_contents(__DIR__ . '/config/database.php', $new_db_content);
    echo "<p>✅ تم إنشاء ملف الاتصال الموحد</p>";
    
    // 3. تحديث الملفات التي تستخدم قاعدة البيانات
    $files_to_update = [
        'auth.php',
        'login.php',
        'daily_report_ajax.php',
        'process_csv_import.php',
        'save_collections.php',
        'check_import_results.php',
        'get_available_dates.php'
    ];
    
    $updated_files = 0;
    foreach ($files_to_update as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            // استبدال require_once لقاعدة البيانات
            $content = str_replace(
                "require_once 'config/database_sqlite.php';",
                "require_once 'config/database.php';",
                $content
            );
            
            // استبدال استعلامات SQLite المحددة
            $content = str_replace('CURRENT_TIMESTAMP', 'NOW()', $content);
            
            file_put_contents($file, $content);
            $updated_files++;
        }
    }
    
    echo "<p>✅ تم تحديث $updated_files ملف</p>";
    
    // 4. اختبار الاتصال بقاعدة البيانات الجديدة
    require_once 'config/database_mysql.php';
    
    $test_query = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch();
    echo "<p>✅ اختبار الاتصال ناجح - عدد المستخدمين: {$test_query['count']}</p>";
    
    $test_agents = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch();
    echo "<p>✅ عدد الوكلاء: {$test_agents['count']}</p>";
    
    $test_collections = $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch();
    echo "<p>✅ عدد التحصيلات: {$test_collections['count']}</p>";
    
    echo "<div class='alert alert-success'>";
    echo "<h6>🎉 تم تحديث النظام بنجاح!</h6>";
    echo "<p>النظام يستخدم الآن قاعدة بيانات MySQL.</p>";
    echo "<ul>";
    echo "<li><strong>قاعدة البيانات:</strong> collections_system</li>";
    echo "<li><strong>الخادم:</strong> localhost</li>";
    echo "<li><strong>phpMyAdmin:</strong> <a href='https://localhost/phpmyadmin/' target='_blank'>https://localhost/phpmyadmin/</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<a href='main.php' class='btn btn-success'>اختبار النظام</a> ";
    echo "<a href='https://localhost/phpmyadmin/' target='_blank' class='btn btn-info'>فتح phpMyAdmin</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>❌ خطأ في تحديث النظام</h6>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
