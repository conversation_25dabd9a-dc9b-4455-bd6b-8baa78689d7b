<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>محتويات الجداول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-container { max-height: 400px; overflow-y: auto; }
        .table th { position: sticky; top: 0; background: #f8f9fa; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-primary">
                <h2><i class="fas fa-database"></i> محتويات الجداول في قاعدة البيانات</h2>
                <p>عرض محتويات جداول collections و sqlite_sequence</p>
            </div>
        </div>
    </div>

    <?php
    try {
        // محاولة الاتصال بقاعدة البيانات الحالية
        $database_file = __DIR__ . '/database/collections_system.db';
        
        if (file_exists($database_file)) {
            $pdo = new PDO("sqlite:$database_file");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<div class='alert alert-success'>";
            echo "<h5>✅ تم الاتصال بقاعدة البيانات SQLite</h5>";
            echo "<p><strong>مسار الملف:</strong> $database_file</p>";
            echo "</div>";
            
            // عرض جميع الجداول الموجودة
            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
            
            echo "<div class='row'>";
            echo "<div class='col-md-6'>";
            echo "<div class='card'>";
            echo "<div class='card-header bg-info text-white'>";
            echo "<h5>📋 الجداول الموجودة</h5>";
            echo "</div>";
            echo "<div class='card-body'>";
            echo "<ul class='list-group'>";
            foreach ($tables as $table) {
                $table_name = $table['name'];
                $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
                echo "<li class='list-group-item d-flex justify-content-between'>";
                echo "<span><strong>$table_name</strong></span>";
                echo "<span class='badge bg-primary'>$count سجل</span>";
                echo "</li>";
            }
            echo "</ul>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
        } else {
            echo "<div class='alert alert-warning'>";
            echo "<h5>⚠️ ملف قاعدة البيانات غير موجود</h5>";
            echo "<p>المسار المتوقع: $database_file</p>";
            echo "</div>";
            
            // البحث عن ملفات قاعدة البيانات
            $db_files = glob(__DIR__ . '/*.db');
            $db_files = array_merge($db_files, glob(__DIR__ . '/database/*.db'));
            
            if (!empty($db_files)) {
                echo "<div class='alert alert-info'>";
                echo "<h6>ملفات قاعدة البيانات الموجودة:</h6>";
                echo "<ul>";
                foreach ($db_files as $file) {
                    echo "<li>" . basename($file) . " - " . realpath($file) . "</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
            
            // محاولة إنشاء قاعدة البيانات
            try {
                require_once 'config/database_sqlite.php';
                echo "<div class='alert alert-success'>";
                echo "<h5>✅ تم إنشاء قاعدة البيانات تلقائياً</h5>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>";
                echo "<h5>❌ خطأ في إنشاء قاعدة البيانات</h5>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
        }
        
        // إذا تم الاتصال بنجاح، عرض محتويات الجداول
        if (isset($pdo)) {
            
            // جدول sqlite_sequence
            echo "<div class='row mt-4'>";
            echo "<div class='col-12'>";
            echo "<div class='card'>";
            echo "<div class='card-header bg-warning text-dark'>";
            echo "<h5>🔢 جدول sqlite_sequence</h5>";
            echo "<small>يحتوي على معرفات التسلسل التلقائي للجداول</small>";
            echo "</div>";
            echo "<div class='card-body'>";
            
            try {
                $sequences = $pdo->query("SELECT * FROM sqlite_sequence")->fetchAll();
                if (!empty($sequences)) {
                    echo "<div class='table-container'>";
                    echo "<table class='table table-striped table-sm'>";
                    echo "<thead><tr><th>اسم الجدول</th><th>آخر معرف</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($sequences as $seq) {
                        echo "<tr>";
                        echo "<td><strong>" . $seq['name'] . "</strong></td>";
                        echo "<td>" . $seq['seq'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-info'>لا توجد بيانات في جدول sqlite_sequence</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'>جدول sqlite_sequence غير موجود أو فارغ</div>";
            }
            
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // جدول collections
            echo "<div class='row mt-4'>";
            echo "<div class='col-12'>";
            echo "<div class='card'>";
            echo "<div class='card-header bg-success text-white'>";
            echo "<h5>💰 جدول collections (التحصيلات)</h5>";
            echo "</div>";
            echo "<div class='card-body'>";
            
            try {
                $collections = $pdo->query("SELECT * FROM collections ORDER BY collection_date DESC, id DESC LIMIT 50")->fetchAll();
                if (!empty($collections)) {
                    echo "<div class='alert alert-info'>";
                    echo "<strong>إجمالي السجلات:</strong> " . $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch()['count'];
                    echo " (عرض آخر 50 سجل)";
                    echo "</div>";
                    
                    echo "<div class='table-container'>";
                    echo "<table class='table table-striped table-sm'>";
                    echo "<thead>";
                    echo "<tr>";
                    echo "<th>المعرف</th>";
                    echo "<th>معرف الوكيل</th>";
                    echo "<th>مبلغ Gateway</th>";
                    echo "<th>مبلغ Riyal Mobile</th>";
                    echo "<th>تاريخ التحصيل</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($collections as $collection) {
                        echo "<tr>";
                        echo "<td>" . $collection['id'] . "</td>";
                        echo "<td>" . $collection['agent_id'] . "</td>";
                        echo "<td class='text-end'>" . number_format($collection['gateway_amount'], 2) . "</td>";
                        echo "<td class='text-end'>" . number_format($collection['riyal_mobile_amount'], 2) . "</td>";
                        echo "<td>" . $collection['collection_date'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    echo "</div>";
                    
                    // إحصائيات سريعة
                    $stats = $pdo->query("
                        SELECT 
                            COUNT(*) as total_records,
                            SUM(gateway_amount) as total_gateway,
                            SUM(riyal_mobile_amount) as total_riyal,
                            MIN(collection_date) as earliest_date,
                            MAX(collection_date) as latest_date
                        FROM collections
                    ")->fetch();
                    
                    echo "<div class='row mt-3'>";
                    echo "<div class='col-md-3'>";
                    echo "<div class='alert alert-primary text-center'>";
                    echo "<h6>إجمالي السجلات</h6>";
                    echo "<h4>" . number_format($stats['total_records']) . "</h4>";
                    echo "</div>";
                    echo "</div>";
                    echo "<div class='col-md-3'>";
                    echo "<div class='alert alert-success text-center'>";
                    echo "<h6>إجمالي Gateway</h6>";
                    echo "<h4>" . number_format($stats['total_gateway'], 2) . "</h4>";
                    echo "</div>";
                    echo "</div>";
                    echo "<div class='col-md-3'>";
                    echo "<div class='alert alert-info text-center'>";
                    echo "<h6>إجمالي Riyal Mobile</h6>";
                    echo "<h4>" . number_format($stats['total_riyal'], 2) . "</h4>";
                    echo "</div>";
                    echo "</div>";
                    echo "<div class='col-md-3'>";
                    echo "<div class='alert alert-warning text-center'>";
                    echo "<h6>الفترة الزمنية</h6>";
                    echo "<small>" . $stats['earliest_date'] . "<br>إلى<br>" . $stats['latest_date'] . "</small>";
                    echo "</div>";
                    echo "</div>";
                    echo "</div>";
                    
                } else {
                    echo "<div class='alert alert-info'>لا توجد بيانات في جدول collections</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>خطأ في قراءة جدول collections: " . $e->getMessage() . "</div>";
            }
            
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // عرض بنية الجداول
            echo "<div class='row mt-4'>";
            echo "<div class='col-md-6'>";
            echo "<div class='card'>";
            echo "<div class='card-header bg-dark text-white'>";
            echo "<h5>🏗️ بنية جدول collections</h5>";
            echo "</div>";
            echo "<div class='card-body'>";
            
            try {
                $schema = $pdo->query("PRAGMA table_info(collections)")->fetchAll();
                echo "<div class='table-container'>";
                echo "<table class='table table-sm'>";
                echo "<thead><tr><th>العمود</th><th>النوع</th><th>مطلوب</th><th>افتراضي</th></tr></thead>";
                echo "<tbody>";
                foreach ($schema as $column) {
                    echo "<tr>";
                    echo "<td><strong>" . $column['name'] . "</strong></td>";
                    echo "<td>" . $column['type'] . "</td>";
                    echo "<td>" . ($column['notnull'] ? 'نعم' : 'لا') . "</td>";
                    echo "<td>" . ($column['dflt_value'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</tbody></table>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'>لا يمكن عرض بنية الجدول</div>";
            }
            
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // عرض الوكلاء إن وجدوا
            echo "<div class='col-md-6'>";
            echo "<div class='card'>";
            echo "<div class='card-header bg-primary text-white'>";
            echo "<h5>🏢 جدول agents (الوكلاء)</h5>";
            echo "</div>";
            echo "<div class='card-body'>";
            
            try {
                $agents = $pdo->query("SELECT * FROM agents ORDER BY agent_id")->fetchAll();
                if (!empty($agents)) {
                    echo "<div class='table-container'>";
                    echo "<table class='table table-sm'>";
                    echo "<thead><tr><th>المعرف</th><th>اسم الوكيل</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($agents as $agent) {
                        echo "<tr>";
                        echo "<td>" . $agent['agent_id'] . "</td>";
                        echo "<td>" . $agent['agent_name'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-info'>لا توجد بيانات وكلاء</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'>جدول الوكلاء غير موجود</div>";
            }
            
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ خطأ في الاتصال بقاعدة البيانات</h5>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5>🔗 روابط مفيدة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>إدارة النظام:</h6>
                            <ul>
                                <li><a href="main.php">الصفحة الرئيسية</a></li>
                                <li><a href="db_viewer_simple.php">مستعرض قاعدة البيانات</a></li>
                                <li><a href="instant_mysql_setup.php">إعداد MySQL</a></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>إدارة البيانات:</h6>
                            <ul>
                                <li><a href="add_collection_integrated.php">إضافة تحصيلات</a></li>
                                <li><a href="daily_report.php">التقارير اليومية</a></li>
                                <li><a href="manage_agents.php">إدارة الوكلاء</a></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>أدوات أخرى:</h6>
                            <ul>
                                <li><a href="backup_database.php">نسخ احتياطي</a></li>
                                <li><a href="import_csv.php">استيراد CSV</a></li>
                                <li><a href="export_excel.php">تصدير Excel</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
