<?php
// قسم إدارة الوكلاء
$message = '';
$error = '';

// معالجة إضافة وكيل جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_agent'])) {
    $agent_name = trim($_POST['agent_name']);
    
    if (empty($agent_name)) {
        $error = 'اسم الوكيل مطلوب';
    } else {
        try {
            // التحقق من عدم وجود الوكيل
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM agents WHERE agent_name = ?");
            $check_stmt->execute([$agent_name]);
            
            if ($check_stmt->fetchColumn() > 0) {
                $error = 'اسم الوكيل موجود بالفعل';
            } else {
                // إضافة الوكيل الجديد
                $stmt = $pdo->prepare("INSERT INTO agents (agent_name) VALUES (?)");
                $stmt->execute([$agent_name]);
                $message = 'تم إضافة الوكيل بنجاح';
                
                // إعادة تحميل قائمة الوكلاء
                $agents_stmt = $pdo->query("SELECT * FROM agents ORDER BY agent_name");
                $agents = $agents_stmt->fetchAll();
            }
        } catch (PDOException $e) {
            $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// معالجة حذف وكيل
if (isset($_GET['delete_agent']) && is_numeric($_GET['delete_agent'])) {
    $agent_id = $_GET['delete_agent'];
    
    try {
        // التحقق من وجود تحصيلات للوكيل
        $check_collections = $pdo->prepare("SELECT COUNT(*) FROM collections WHERE agent_id = ?");
        $check_collections->execute([$agent_id]);
        
        if ($check_collections->fetchColumn() > 0) {
            $error = 'لا يمكن حذف الوكيل لوجود تحصيلات مسجلة له';
        } else {
            $stmt = $pdo->prepare("DELETE FROM agents WHERE agent_id = ?");
            $stmt->execute([$agent_id]);
            $message = 'تم حذف الوكيل بنجاح';
            
            // إعادة تحميل قائمة الوكلاء
            $agents_stmt = $pdo->query("SELECT * FROM agents ORDER BY agent_name");
            $agents = $agents_stmt->fetchAll();
        }
    } catch (PDOException $e) {
        $error = 'خطأ في حذف الوكيل';
    }
}

// معالجة تعديل اسم وكيل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_agent'])) {
    $agent_id = $_POST['agent_id'];
    $new_name = trim($_POST['new_agent_name']);
    
    if (empty($new_name)) {
        $error = 'اسم الوكيل مطلوب';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE agents SET agent_name = ? WHERE agent_id = ?");
            $stmt->execute([$new_name, $agent_id]);
            $message = 'تم تعديل اسم الوكيل بنجاح';
            
            // إعادة تحميل قائمة الوكلاء
            $agents_stmt = $pdo->query("SELECT * FROM agents ORDER BY agent_name");
            $agents = $agents_stmt->fetchAll();
        } catch (PDOException $e) {
            $error = 'خطأ في تعديل الوكيل';
        }
    }
}

// جلب إحصائيات الوكلاء
$stats_query = "
    SELECT 
        a.agent_id,
        a.agent_name,
        COUNT(c.id) as total_records,
        SUM(c.gateway_amount + c.riyal_mobile_amount) as total_amount,
        MAX(c.collection_date) as last_collection
    FROM agents a
    LEFT JOIN collections c ON a.agent_id = c.agent_id
    GROUP BY a.agent_id, a.agent_name
    ORDER BY a.agent_name
";
$agents_stats = $pdo->query($stats_query)->fetchAll();
?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user-plus"></i> إضافة وكيل جديد</h5>
            </div>
            <div class="card-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>
                
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">اسم الوكيل:</label>
                        <input type="text" name="agent_name" class="form-control" required placeholder="أدخل اسم الوكيل">
                    </div>
                    
                    <button type="submit" name="add_agent" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة الوكيل
                    </button>
                </form>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary"><?= count($agents) ?></h4>
                        <small>إجمالي الوكلاء</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">
                            <?php
                            $active_agents = 0;
                            foreach ($agents_stats as $stat) {
                                if ($stat['total_records'] > 0) $active_agents++;
                            }
                            echo $active_agents;
                            ?>
                        </h4>
                        <small>وكلاء نشطون</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> قائمة الوكلاء والإحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>اسم الوكيل</th>
                                <th>عدد التحصيلات</th>
                                <th>إجمالي المبلغ</th>
                                <th>آخر تحصيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($agents_stats as $agent): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($agent['agent_name']) ?></strong>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $agent['total_records'] ?></span>
                                </td>
                                <td>
                                    <span class="text-success">
                                        <?= number_format($agent['total_amount'] ?? 0, 2) ?> ريال
                                    </span>
                                </td>
                                <td>
                                    <?= $agent['last_collection'] ? date('d/m/Y', strtotime($agent['last_collection'])) : 'لا يوجد' ?>
                                </td>
                                <td>
                                    <?php if ($agent['total_records'] > 0): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-warning" onclick="editAgent(<?= $agent['agent_id'] ?>, '<?= htmlspecialchars($agent['agent_name']) ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="?delete_agent=<?= $agent['agent_id'] ?>" 
                                           class="btn btn-danger"
                                           onclick="return confirm('هل تريد حذف هذا الوكيل؟\nملاحظة: لا يمكن حذف الوكيل إذا كان له تحصيلات مسجلة')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الوكيل -->
<div class="modal fade" id="editAgentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل اسم الوكيل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="agent_id" id="edit_agent_id">
                    <div class="mb-3">
                        <label class="form-label">اسم الوكيل الجديد:</label>
                        <input type="text" name="new_agent_name" id="edit_agent_name" class="form-control" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_agent" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editAgent(agentId, agentName) {
    document.getElementById('edit_agent_id').value = agentId;
    document.getElementById('edit_agent_name').value = agentName;
    
    const modal = new bootstrap.Modal(document.getElementById('editAgentModal'));
    modal.show();
}
</script>
