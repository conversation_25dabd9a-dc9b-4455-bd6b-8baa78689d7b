<?php
require_once 'config/database.php';

// إحصائيات اليوم
$today = date('Y-m-d');
$today_stats = $pdo->prepare("
    SELECT
        COUNT(DISTINCT agent_id) as active_agents,
        SUM(gateway_amount) as today_gateway,
        SUM(riyal_mobile_amount) as today_riyal,
        SUM(gateway_amount + riyal_mobile_amount) as today_total
    FROM collections WHERE collection_date = ?
");
$today_stats->execute([$today]);
$today_data = $today_stats->fetch(PDO::FETCH_ASSOC);

// إحصائيات الشهر الحالي
$current_month = date('Y-m');
$month_stats = $pdo->prepare("
    SELECT
        SUM(gateway_amount) as month_gateway,
        SUM(riyal_mobile_amount) as month_riyal,
        SUM(gateway_amount + riyal_mobile_amount) as month_total
    FROM collections WHERE strftime('%Y-%m', collection_date) = ?
");
$month_stats->execute([$current_month]);
$month_data = $month_stats->fetch(PDO::FETCH_ASSOC);

// أفضل 5 وكلاء هذا الشهر
$top_agents = $pdo->prepare("
    SELECT
        a.agent_name,
        SUM(c.gateway_amount + c.riyal_mobile_amount) as total_amount
    FROM collections c
    JOIN agents a ON c.agent_id = a.agent_id
    WHERE strftime('%Y-%m', c.collection_date) = ?
    GROUP BY a.agent_id, a.agent_name
    ORDER BY total_amount DESC
    LIMIT 5
");
$top_agents->execute([$current_month]);
$top_agents_data = $top_agents->fetchAll(PDO::FETCH_ASSOC);

// إجمالي عدد الوكلاء
$total_agents = $pdo->query("SELECT COUNT(*) FROM agents")->fetchColumn();
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لوحة المعلومات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">لوحة المعلومات والإحصائيات</h2>

    <!-- إحصائيات اليوم -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h5>تحصيلات اليوم</h5>
                    <h3><?= number_format($today_data['today_total'] ?: 0, 2) ?></h3>
                    <small>وكلاء نشطين: <?= $today_data['active_agents'] ?: 0 ?></small>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5>البوابة اليوم</h5>
                    <h3><?= number_format($today_data['today_gateway'] ?: 0, 2) ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h5>الريال موبايل اليوم</h5>
                    <h3><?= number_format($today_data['today_riyal'] ?: 0, 2) ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5>إجمالي الوكلاء</h5>
                    <h3><?= $total_agents ?></h3>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الشهر -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">إحصائيات الشهر الحالي</div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td>البوابة:</td>
                            <td class="text-end"><?= number_format($month_data['month_gateway'] ?: 0, 2) ?></td>
                        </tr>
                        <tr>
                            <td>الريال موبايل:</td>
                            <td class="text-end"><?= number_format($month_data['month_riyal'] ?: 0, 2) ?></td>
                        </tr>
                        <tr class="table-success">
                            <td><strong>الإجمالي:</strong></td>
                            <td class="text-end"><strong><?= number_format($month_data['month_total'] ?: 0, 2) ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">أفضل 5 وكلاء هذا الشهر</div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم الوكيل</th>
                                <th>إجمالي التحصيلات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_agents_data as $index => $agent): ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td><?= $agent['agent_name'] ?></td>
                                <td class="text-end"><?= number_format($agent['total_amount'], 2) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center">
        <a href="reports.php" class="btn btn-primary">عرض جميع التقارير</a>
        <a href="add_collection.php" class="btn btn-success">إضافة تحصيلات</a>
    </div>
</div>
</body>
</html>
