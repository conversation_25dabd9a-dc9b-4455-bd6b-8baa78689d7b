<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تحويل النظام إلى MySQL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-exchange-alt"></i> تحويل النظام إلى MySQL</h2>
        <p>تحديث جميع ملفات النظام لاستخدام قاعدة بيانات MySQL</p>
    </div>

    <?php
    try {
        echo "<div class='card'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h5>🔄 جاري تحديث النظام...</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        // 1. التحقق من وجود ملف إعدادات MySQL
        $mysql_config_file = __DIR__ . '/config/database_mysql_working.php';
        if (!file_exists($mysql_config_file)) {
            throw new Exception("ملف إعدادات MySQL غير موجود. يرجى إنشاء قاعدة البيانات أولاً.");
        }
        
        echo "<p>✅ تم العثور على ملف إعدادات MySQL</p>";
        
        // 2. إنشاء نسخة احتياطية من ملف قاعدة البيانات الحالي
        $current_db_file = __DIR__ . '/config/database_sqlite.php';
        if (file_exists($current_db_file)) {
            $backup_file = __DIR__ . '/config/database_sqlite_backup_' . date('Y-m-d_H-i-s') . '.php';
            copy($current_db_file, $backup_file);
            echo "<p>✅ تم إنشاء نسخة احتياطية: " . basename($backup_file) . "</p>";
        }
        
        // 3. إنشاء ملف الاتصال الموحد
        $unified_db_content = '<?php
// ملف الاتصال الموحد بقاعدة البيانات
// يمكن التبديل بين MySQL و SQLite

$use_mysql = true; // تغيير إلى false لاستخدام SQLite

if ($use_mysql) {
    // استخدام MySQL
    require_once __DIR__ . "/database_mysql_working.php";
} else {
    // استخدام SQLite
    require_once __DIR__ . "/database_sqlite.php";
}
?>';
        
        file_put_contents(__DIR__ . '/config/database.php', $unified_db_content);
        echo "<p>✅ تم إنشاء ملف الاتصال الموحد</p>";
        
        // 4. تحديث الملفات التي تستخدم قاعدة البيانات
        $files_to_update = [
            'auth.php',
            'login.php', 
            'daily_report_ajax.php',
            'process_csv_import.php',
            'save_collections.php',
            'check_import_results.php',
            'get_available_dates.php',
            'db_viewer_simple.php'
        ];
        
        $updated_files = 0;
        $failed_files = [];
        
        foreach ($files_to_update as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                $original_content = $content;
                
                // استبدال require_once لقاعدة البيانات
                $content = str_replace(
                    "require_once 'config/database_sqlite.php';",
                    "require_once 'config/database.php';",
                    $content
                );
                
                // استبدال استعلامات SQLite المحددة بـ MySQL
                $sqlite_to_mysql = [
                    'CURRENT_TIMESTAMP' => 'NOW()',
                    'AUTOINCREMENT' => 'AUTO_INCREMENT',
                    'INTEGER PRIMARY KEY AUTOINCREMENT' => 'INT AUTO_INCREMENT PRIMARY KEY'
                ];
                
                foreach ($sqlite_to_mysql as $sqlite => $mysql) {
                    $content = str_replace($sqlite, $mysql, $content);
                }
                
                if ($content !== $original_content) {
                    if (file_put_contents($file, $content)) {
                        $updated_files++;
                        echo "<p>✅ تم تحديث: $file</p>";
                    } else {
                        $failed_files[] = $file;
                        echo "<p class='text-warning'>⚠️ فشل تحديث: $file</p>";
                    }
                } else {
                    echo "<p class='text-muted'>ℹ️ لا يحتاج تحديث: $file</p>";
                }
            } else {
                echo "<p class='text-muted'>ℹ️ غير موجود: $file</p>";
            }
        }
        
        echo "<p><strong>تم تحديث $updated_files ملف</strong></p>";
        
        if (!empty($failed_files)) {
            echo "<div class='alert alert-warning'>";
            echo "<h6>⚠️ ملفات فشل تحديثها:</h6>";
            echo "<ul>";
            foreach ($failed_files as $file) {
                echo "<li>$file</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
        // 5. اختبار الاتصال بقاعدة البيانات الجديدة
        echo "<h6>اختبار الاتصال:</h6>";
        require_once 'config/database.php';
        
        $test_users = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch();
        echo "<p>✅ المستخدمون: {$test_users['count']}</p>";
        
        $test_agents = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch();
        echo "<p>✅ الوكلاء: {$test_agents['count']}</p>";
        
        $test_collections = $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch();
        echo "<p>✅ التحصيلات: {$test_collections['count']}</p>";
        
        echo "</div>";
        echo "</div>";
        
        // نتيجة النجاح
        echo "<div class='alert alert-success'>";
        echo "<h4>🎉 تم تحويل النظام بنجاح!</h4>";
        echo "<p>النظام يستخدم الآن قاعدة بيانات MySQL.</p>";
        echo "<ul>";
        echo "<li><strong>قاعدة البيانات:</strong> collections_system</li>";
        echo "<li><strong>نوع قاعدة البيانات:</strong> MySQL</li>";
        echo "<li><strong>الخادم:</strong> localhost</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='alert alert-info'>";
        echo "<h6>📋 ملاحظات مهمة:</h6>";
        echo "<ul>";
        echo "<li>تم إنشاء نسخة احتياطية من إعدادات SQLite</li>";
        echo "<li>يمكن العودة لـ SQLite بتغيير \$use_mysql = false في config/database.php</li>";
        echo "<li>جميع البيانات محفوظة وآمنة</li>";
        echo "<li>النظام جاهز للاستخدام مع MySQL</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='mt-4'>";
        echo "<a href='main.php' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-rocket'></i> اختبار النظام مع MySQL";
        echo "</a> ";
        echo "<a href='db_viewer_simple.php' class='btn btn-info'>";
        echo "<i class='fas fa-database'></i> مستعرض قاعدة البيانات";
        echo "</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h6>❌ خطأ في تحويل النظام:</h6>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
        
        echo "<div class='alert alert-info'>";
        echo "<h6>💡 الحلول المقترحة:</h6>";
        echo "<ul>";
        echo "<li><a href='mysql_alternative_setup.php'>إعادة إنشاء قاعدة البيانات</a></li>";
        echo "<li><a href='main.php'>العودة للنظام الحالي (SQLite)</a></li>";
        echo "<li>التحقق من تشغيل MySQL</li>";
        echo "</ul>";
        echo "</div>";
    }
    ?>
    
    <div class="mt-4">
        <a href="mysql_alternative_setup.php" class="btn btn-secondary">العودة لإعداد MySQL</a>
        <a href="main.php" class="btn btn-outline-primary">النظام الرئيسي</a>
    </div>
</div>

</body>
</html>
