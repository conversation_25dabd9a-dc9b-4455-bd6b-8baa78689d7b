<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>فحص شامل للنظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container-fluid mt-4">
    <div class="alert alert-primary text-center">
        <h1><i class="fas fa-stethoscope"></i> فحص شامل للنظام</h1>
        <p>فحص جميع مكونات النظام والتأكد من عملها بشكل صحيح</p>
    </div>

    <?php
    $checks = [];
    $overall_status = true;

    // 1. فحص ملف قاعدة البيانات
    echo "<div class='row mb-4'>";
    echo "<div class='col-12'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5>🔍 فحص ملفات النظام</h5>";
    echo "</div>";
    echo "<div class='card-body'>";

    $files_to_check = [
        'config/database.php' => 'ملف إعدادات قاعدة البيانات',
        'add_collection_fixed.php' => 'صفحة إضافة التحصيلات المحدثة',
        'manage_agents_fixed.php' => 'صفحة إدارة الوكلاء المحدثة',
        'daily_report_new.php' => 'صفحة التقرير اليومي',
        'main_fixed.php' => 'الصفحة الرئيسية المحدثة',
        'database_status.php' => 'صفحة حالة قاعدة البيانات'
    ];

    foreach ($files_to_check as $file => $description) {
        $exists = file_exists($file);
        $icon = $exists ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';
        $status = $exists ? 'موجود' : 'مفقود';
        
        echo "<div class='row mb-2'>";
        echo "<div class='col-md-6'>$description</div>";
        echo "<div class='col-md-3'>$file</div>";
        echo "<div class='col-md-3'><i class='$icon'></i> $status</div>";
        echo "</div>";
        
        if (!$exists) $overall_status = false;
    }

    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    // 2. فحص قاعدة البيانات
    echo "<div class='row mb-4'>";
    echo "<div class='col-12'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h5>🗄️ فحص قاعدة البيانات</h5>";
    echo "</div>";
    echo "<div class='card-body'>";

    try {
        require_once 'config/database.php';
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle'></i> تم الاتصال بقاعدة البيانات بنجاح";
        echo "</div>";

        // فحص الجداول المطلوبة
        $required_tables = ['users', 'agents', 'days', 'gateway', 'riyal_mobile'];
        $tables = $pdo->query("SHOW TABLES")->fetchAll();
        $existing_tables = array_map(function($table) { return array_values($table)[0]; }, $tables);

        echo "<h6>الجداول المطلوبة:</h6>";
        echo "<div class='row'>";
        foreach ($required_tables as $table) {
            $exists = in_array($table, $existing_tables);
            $icon = $exists ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';
            $status = $exists ? 'موجود' : 'مفقود';
            
            if ($exists) {
                $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                $status .= " ($count سجل)";
            }
            
            echo "<div class='col-md-2'>";
            echo "<div class='text-center'>";
            echo "<i class='$icon fa-2x'></i>";
            echo "<h6>$table</h6>";
            echo "<small>$status</small>";
            echo "</div>";
            echo "</div>";
            
            if (!$exists) $overall_status = false;
        }
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-times-circle'></i> خطأ في قاعدة البيانات: " . $e->getMessage();
        echo "</div>";
        $overall_status = false;
    }

    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    // 3. فحص الصفحات
    echo "<div class='row mb-4'>";
    echo "<div class='col-12'>";
    echo "<div class='card'>";
    echo "<div class='card-header bg-warning text-dark'>";
    echo "<h5>🌐 فحص الصفحات</h5>";
    echo "</div>";
    echo "<div class='card-body'>";

    $pages_to_test = [
        '/main_fixed.php' => 'الصفحة الرئيسية',
        '/add_collection_fixed.php' => 'إضافة التحصيلات',
        '/manage_agents_fixed.php' => 'إدارة الوكلاء',
        '/daily_report_new.php' => 'التقرير اليومي',
        '/database_status.php' => 'حالة قاعدة البيانات'
    ];

    echo "<div class='row'>";
    foreach ($pages_to_test as $url => $name) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-body text-center'>";
        echo "<h6>$name</h6>";
        echo "<a href='$url' class='btn btn-primary btn-sm' target='_blank'>";
        echo "<i class='fas fa-external-link-alt'></i> اختبار";
        echo "</a>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";

    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    // 4. اختبار العمليات
    if (isset($pdo)) {
        echo "<div class='row mb-4'>";
        echo "<div class='col-12'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h5>⚙️ اختبار العمليات</h5>";
        echo "</div>";
        echo "<div class='card-body'>";

        // اختبار إضافة يوم جديد
        try {
            $test_date = date('Y-m-d', strtotime('+1 day'));
            $check_day = $pdo->prepare("SELECT COUNT(*) FROM days WHERE date = ?");
            $check_day->execute([$test_date]);
            
            if ($check_day->fetchColumn() == 0) {
                $day_name = date('l', strtotime($test_date));
                $month_name = date('F', strtotime($test_date));
                $year = date('Y', strtotime($test_date));
                
                $day_names_ar = [
                    'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء', 'Wednesday' => 'الأربعاء',
                    'Thursday' => 'الخميس', 'Friday' => 'الجمعة', 'Saturday' => 'السبت', 'Sunday' => 'الأحد'
                ];
                $month_names_ar = [
                    'January' => 'يناير', 'February' => 'فبراير', 'March' => 'مارس',
                    'April' => 'أبريل', 'May' => 'مايو', 'June' => 'يونيو',
                    'July' => 'يوليو', 'August' => 'أغسطس', 'September' => 'سبتمبر',
                    'October' => 'أكتوبر', 'November' => 'نوفمبر', 'December' => 'ديسمبر'
                ];
                
                $insert_test = $pdo->prepare("INSERT INTO days (date, day_name, month_name, year) VALUES (?, ?, ?, ?)");
                $insert_test->execute([$test_date, $day_names_ar[$day_name], $month_names_ar[$month_name], $year]);
                
                echo "<div class='alert alert-success'>";
                echo "<i class='fas fa-check'></i> اختبار إضافة يوم جديد: نجح";
                echo "</div>";
                
                // حذف اليوم التجريبي
                $pdo->prepare("DELETE FROM days WHERE date = ?")->execute([$test_date]);
            } else {
                echo "<div class='alert alert-info'>";
                echo "<i class='fas fa-info'></i> اختبار إضافة يوم: اليوم موجود مسبقاً";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times'></i> اختبار إضافة يوم: فشل - " . $e->getMessage();
            echo "</div>";
            $overall_status = false;
        }

        // اختبار الاستعلامات
        try {
            $test_queries = [
                "SELECT COUNT(*) FROM agents" => "عدد الوكلاء",
                "SELECT COUNT(*) FROM users" => "عدد المستخدمين",
                "SELECT COUNT(*) FROM days" => "عدد الأيام",
                "SELECT COUNT(*) FROM gateway" => "سجلات البوابة",
                "SELECT COUNT(*) FROM riyal_mobile" => "سجلات ريال موبايل"
            ];

            echo "<h6>اختبار الاستعلامات:</h6>";
            echo "<div class='row'>";
            foreach ($test_queries as $query => $description) {
                $result = $pdo->query($query)->fetchColumn();
                echo "<div class='col-md-2'>";
                echo "<div class='text-center'>";
                echo "<i class='fas fa-database text-primary'></i>";
                echo "<h6>$result</h6>";
                echo "<small>$description</small>";
                echo "</div>";
                echo "</div>";
            }
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times'></i> خطأ في الاستعلامات: " . $e->getMessage();
            echo "</div>";
            $overall_status = false;
        }

        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }

    // 5. النتيجة النهائية
    echo "<div class='row'>";
    echo "<div class='col-12'>";
    echo "<div class='card'>";
    echo "<div class='card-header " . ($overall_status ? 'bg-success' : 'bg-danger') . " text-white'>";
    echo "<h5>📊 النتيجة النهائية</h5>";
    echo "</div>";
    echo "<div class='card-body text-center'>";

    if ($overall_status) {
        echo "<div class='alert alert-success'>";
        echo "<h3><i class='fas fa-check-circle'></i> النظام يعمل بشكل ممتاز!</h3>";
        echo "<p>جميع المكونات تعمل بشكل صحيح والنظام جاهز للاستخدام</p>";
        echo "<div class='mt-3'>";
        echo "<a href='/main_fixed.php' class='btn btn-success me-2'>";
        echo "<i class='fas fa-home'></i> الصفحة الرئيسية";
        echo "</a>";
        echo "<a href='/add_collection_fixed.php' class='btn btn-primary me-2'>";
        echo "<i class='fas fa-plus'></i> إضافة تحصيلات";
        echo "</a>";
        echo "<a href='/daily_report_new.php' class='btn btn-info'>";
        echo "<i class='fas fa-chart-bar'></i> التقرير اليومي";
        echo "</a>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h3><i class='fas fa-exclamation-triangle'></i> يوجد مشاكل في النظام</h3>";
        echo "<p>بعض المكونات لا تعمل بشكل صحيح. يرجى مراجعة التفاصيل أعلاه</p>";
        echo "<div class='mt-3'>";
        echo "<a href='/create_new_database_structure.php' class='btn btn-warning me-2'>";
        echo "<i class='fas fa-wrench'></i> إصلاح قاعدة البيانات";
        echo "</a>";
        echo "<a href='/database_status.php' class='btn btn-info'>";
        echo "<i class='fas fa-database'></i> حالة قاعدة البيانات";
        echo "</a>";
        echo "</div>";
        echo "</div>";
    }

    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    ?>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5>ℹ️ معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>معلومات الخادم:</h6>
                            <ul class="list-unstyled">
                                <li><strong>PHP:</strong> <?= phpversion() ?></li>
                                <li><strong>الخادم:</strong> Node.js + PHP</li>
                                <li><strong>المنفذ:</strong> 7445</li>
                                <li><strong>الوقت:</strong> <?= date('Y-m-d H:i:s') ?></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>إحصائيات الملفات:</h6>
                            <ul class="list-unstyled">
                                <li><strong>ملفات PHP:</strong> <?= count(glob('*.php')) ?></li>
                                <li><strong>حجم المجلد:</strong> <?= round(array_sum(array_map('filesize', glob('*'))) / 1024 / 1024, 2) ?> MB</li>
                                <li><strong>آخر تعديل:</strong> <?= date('Y-m-d H:i:s', filemtime('.')) ?></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>روابط مفيدة:</h6>
                            <ul class="list-unstyled">
                                <li><a href="/test_server.php">اختبار الخادم</a></li>
                                <li><a href="/view_new_structure.php">هيكل قاعدة البيانات</a></li>
                                <li><a href="/">الصفحة الرئيسية</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
