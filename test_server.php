<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار خادم Node.js + PHP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-5">
    <div class="alert alert-success text-center">
        <h1><i class="fas fa-check-circle"></i> خادم Node.js + PHP يعمل بنجاح!</h1>
        <p class="lead">تم تشغيل PHP بنجاح عبر خادم Node.js</p>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-server"></i> معلومات الخادم</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>المنفذ:</strong> 7445</li>
                        <li><strong>الوقت:</strong> <?= date('Y-m-d H:i:s') ?></li>
                        <li><strong>إصدار PHP:</strong> <?= phpversion() ?></li>
                        <li><strong>النظام:</strong> <?= php_uname() ?></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-database"></i> اختبار قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        echo "<div class='alert alert-success'>";
                        echo "<i class='fas fa-check'></i> الاتصال بقاعدة البيانات ناجح";
                        echo "</div>";
                        
                        // عرض الجداول
                        $tables = $pdo->query("SHOW TABLES")->fetchAll();
                        echo "<p><strong>الجداول الموجودة:</strong></p>";
                        echo "<ul>";
                        foreach ($tables as $table) {
                            $table_name = array_values($table)[0];
                            echo "<li>$table_name</li>";
                        }
                        echo "</ul>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-warning'>";
                        echo "<i class='fas fa-exclamation-triangle'></i> خطأ في قاعدة البيانات: " . $e->getMessage();
                        echo "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-link"></i> روابط النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="/create_new_database_structure.php" class="btn btn-success">
                                    <i class="fas fa-database"></i><br>إنشاء قاعدة البيانات
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="/view_new_structure.php" class="btn btn-info">
                                    <i class="fas fa-sitemap"></i><br>عرض الهيكل
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="/add_gateway_data.php" class="btn btn-warning">
                                    <i class="fas fa-door-open"></i><br>بيانات البوابة
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="/add_riyal_data.php" class="btn btn-danger">
                                    <i class="fas fa-mobile-alt"></i><br>بيانات ريال موبايل
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="/manage_users.php" class="btn btn-primary">
                                    <i class="fas fa-users"></i><br>إدارة المستخدمين
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="/daily_report_new.php" class="btn btn-secondary">
                                    <i class="fas fa-chart-bar"></i><br>التقرير اليومي
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-grid">
                                <a href="/api/status" class="btn btn-dark" target="_blank">
                                    <i class="fas fa-code"></i><br>API Status
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info text-center">
                <h5><i class="fas fa-rocket"></i> النظام جاهز للاستخدام!</h5>
                <p>خادم Node.js يعمل على المنفذ 7445 مع دعم كامل لـ PHP</p>
                <small>تم إنشاؤه بواسطة Augment Agent</small>
            </div>
        </div>
    </div>
</div>

</body>
</html>
