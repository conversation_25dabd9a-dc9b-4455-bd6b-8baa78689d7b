<?php
require_once 'config/database.php';

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);

    $date = $input['date'] ?? '';
    $collections = $input['collections'] ?? [];

    if (empty($date)) {
        throw new Exception('التاريخ مطلوب');
    }

    if (empty($collections)) {
        throw new Exception('لا توجد بيانات للحفظ');
    }

    // التحقق من صحة التاريخ
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        throw new Exception('تنسيق التاريخ غير صحيح');
    }

    // بدء المعاملة
    $pdo->beginTransaction();

    $saved = 0;
    $updated = 0;

    foreach ($collections as $collection) {
        $agentId = $collection['agent_id'] ?? 0;
        $gatewayAmount = (float)($collection['gateway_amount'] ?? 0);
        $riyalAmount = (float)($collection['riyal_mobile_amount'] ?? 0);

        if ($agentId <= 0) {
            continue; // تخطي السجلات غير الصحيحة
        }

        // التحقق من وجود السجل
        $check_stmt = $pdo->prepare("
            SELECT id
            FROM collections
            WHERE agent_id = ? AND collection_date = ?
        ");
        $check_stmt->execute([$agentId, $date]);
        $existing = $check_stmt->fetch();

        if ($existing) {
            // تحديث السجل الموجود
            $update_stmt = $pdo->prepare("
                UPDATE collections
                SET gateway_amount = ?, riyal_mobile_amount = ?
                WHERE id = ?
            ");
            $update_stmt->execute([$gatewayAmount, $riyalAmount, $existing['id']]);
            $updated++;
        } else {
            // إنشاء سجل جديد
            $insert_stmt = $pdo->prepare("
                INSERT INTO collections (agent_id, collection_date, gateway_amount, riyal_mobile_amount)
                VALUES (?, ?, ?, ?)
            ");
            $insert_stmt->execute([$agentId, $date, $gatewayAmount, $riyalAmount]);
            $saved++;
        }
    }

    // تأكيد المعاملة
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'saved' => $saved,
        'updated' => $updated,
        'total' => $saved + $updated,
        'date' => $date
    ]);

} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
