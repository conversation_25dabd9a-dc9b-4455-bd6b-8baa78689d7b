<?php
// سكريبت التثبيت التلقائي - الرابط المحلي
$config = [
    'db_host' => 'localhost',
    'db_name' => 'collections_system',
    'db_user' => 'root',
    'db_pass' => '',
    'server_ip' => 'localhost',
    'server_port' => '7445'
];

echo "<h2>تثبيت نظام إدارة تحصيلات الوكلاء</h2>";

try {
    // إنشاء قاعدة البيانات
    $pdo = new PDO("mysql:host={$config['db_host']}", $config['db_user'], $config['db_pass']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $pdo->exec("CREATE DATABASE IF NOT EXISTS {$config['db_name']} CHARACTER SET utf8 COLLATE utf8_general_ci");
    echo "<p style='color: green;'>✓ تم إنشاء قاعدة البيانات بنجاح</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8", 
                   $config['db_user'], $config['db_pass']);
    
    // تنفيذ سكريبت إنشاء الجداول
    $schema = file_get_contents('database/schema.sql');
    $pdo->exec($schema);
    echo "<p style='color: green;'>✓ تم إنشاء الجداول بنجاح</p>";
    
    // إدراج البيانات التجريبية
    $agents = ['الوكيل الأول', 'الشرق', 'جوث', 'الشمال', 'سقف', 'أبو أسامة', 'الوسط', 'الأثير', 'المريخ', 'الزعيم'];
    $stmt = $pdo->prepare("INSERT INTO agents (agent_name) VALUES (?)");
    
    foreach ($agents as $agent) {
        $stmt->execute([$agent]);
    }
    echo "<p style='color: green;'>✓ تم إدراج البيانات التجريبية بنجاح</p>";
    
    echo "<hr>";
    echo "<h3>معلومات الوصول:</h3>";
    echo "<p><strong>الرابط المحلي:</strong> <a href='http://localhost:7445' target='_blank'>http://localhost:7445</a></p>";
    echo "<p style='color: blue;'>النظام جاهز للاستخدام محلي3</p>";
    
} catch(Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>

