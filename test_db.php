<?php
echo "<h2>اختبار قاعدة البيانات</h2>";

try {
    require_once 'config/database_sqlite.php';
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار جدول المستخدمين
    $users = $pdo->query("SELECT * FROM users")->fetchAll();
    echo "<p>عدد المستخدمين: " . count($users) . "</p>";
    
    // اختبار جدول الوكلاء
    $agents = $pdo->query("SELECT * FROM agents")->fetchAll();
    echo "<p>عدد الوكلاء: " . count($agents) . "</p>";
    
    // اختبار جدول التحصيلات
    $collections = $pdo->query("SELECT * FROM collections")->fetchAll();
    echo "<p>عدد التحصيلات: " . count($collections) . "</p>";
    
    echo "<hr>";
    echo "<h3>المستخدمون:</h3>";
    foreach ($users as $user) {
        echo "<p>- " . $user['employee_name'] . " (" . $user['username'] . ") - " . $user['permissions'] . "</p>";
    }
    
    echo "<h3>الوكلاء:</h3>";
    foreach ($agents as $agent) {
        echo "<p>- " . $agent['agent_name'] . "</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='login.php'>الذهاب لصفحة تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
