<?php
require_once 'config/database_sqlite.php';

header('Content-Type: application/json');

try {
    $type = $_POST['type'] ?? $_GET['type'] ?? '';

    if (empty($type) || !in_array($type, ['gateway', 'riyal'])) {
        throw new Exception('نوع الاستيراد غير صحيح');
    }

    // تحديد الملف
    $fileName = $type === 'gateway' ? 'import.csv' : 'ryal.csv';
    $filePath = __DIR__ . '/database/' . $fileName;

    if (!file_exists($filePath)) {
        throw new Exception("الملف غير موجود: $fileName");
    }

    // قراءة الملف
    $csvContent = file_get_contents($filePath);
    if ($csvContent === false) {
        throw new Exception("فشل في قراءة الملف: $fileName");
    }

    // تحليل CSV
    $lines = explode("\n", $csvContent);
    $lines = array_filter($lines, function($line) {
        return !empty(trim($line));
    });

    if (empty($lines)) {
        throw new Exception('الملف فارغ');
    }

    // جلب الوكلاء
    $agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents");
    $agents_map = [];
    while ($agent = $agents_stmt->fetch()) {
        $agents_map[trim($agent['agent_name'])] = $agent['agent_id'];
    }

    // تسجيل للتتبع
    error_log("عدد الوكلاء في قاعدة البيانات: " . count($agents_map));
    error_log("أسماء الوكلاء: " . implode(', ', array_keys($agents_map)));

    // إحصائيات
    $imported = 0;
    $updated = 0;
    $errors = 0;
    $total = 0;

    // بدء المعاملة
    $pdo->beginTransaction();

    $baseDate = date('Y-m-d');

    foreach ($lines as $lineIndex => $line) {
        $total++;

        try {
            $cells = explode(',', trim($line));

            if (count($cells) < 2) {
                $errors++;
                continue;
            }

            $agentName = trim($cells[0]);

            if (!isset($agents_map[$agentName])) {
                $errors++;
                error_log("وكيل غير موجود: '$agentName' في الصف $lineIndex");
                error_log("الوكلاء المتاحون: " . implode(', ', array_keys($agents_map)));
                continue;
            }

            $agentId = $agents_map[$agentName];

            // معالجة كل عمود (يوم)
            for ($dayIndex = 1; $dayIndex < count($cells); $dayIndex++) {
                $amount = (float)trim($cells[$dayIndex]);

                if ($amount <= 0) {
                    continue;
                }

                // حساب التاريخ
                $currentDate = date('Y-m-d', strtotime($baseDate . ' -' . ($dayIndex - 1) . ' days'));

                // التحقق من وجود السجل
                $check_stmt = $pdo->prepare("
                    SELECT id, gateway_amount, riyal_mobile_amount
                    FROM collections
                    WHERE agent_id = ? AND collection_date = ?
                ");
                $check_stmt->execute([$agentId, $currentDate]);
                $existing = $check_stmt->fetch();

                if ($existing) {
                    // تحديث السجل الموجود
                    if ($type === 'gateway') {
                        $update_stmt = $pdo->prepare("
                            UPDATE collections
                            SET gateway_amount = ?
                            WHERE id = ?
                        ");
                        $update_stmt->execute([$amount, $existing['id']]);
                    } else {
                        $update_stmt = $pdo->prepare("
                            UPDATE collections
                            SET riyal_mobile_amount = ?
                            WHERE id = ?
                        ");
                        $update_stmt->execute([$amount, $existing['id']]);
                    }
                    $updated++;
                } else {
                    // إنشاء سجل جديد
                    if ($type === 'gateway') {
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO collections (agent_id, collection_date, gateway_amount, riyal_mobile_amount)
                            VALUES (?, ?, ?, 0)
                        ");
                        $insert_stmt->execute([$agentId, $currentDate, $amount]);
                    } else {
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO collections (agent_id, collection_date, gateway_amount, riyal_mobile_amount)
                            VALUES (?, ?, 0, ?)
                        ");
                        $insert_stmt->execute([$agentId, $currentDate, $amount]);
                    }
                    $imported++;
                }
            }

        } catch (Exception $e) {
            $errors++;
            error_log("خطأ في الصف $lineIndex: " . $e->getMessage());
        }
    }

    // تأكيد المعاملة
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'imported' => $imported,
        'updated' => $updated,
        'errors' => $errors,
        'total' => $total,
        'type' => $type === 'gateway' ? 'البوابة' : 'ريال موبايل',
        'file' => $fileName
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
