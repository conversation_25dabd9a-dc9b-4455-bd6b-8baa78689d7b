<?php
require_once 'config/database_sqlite.php';

// تحديد نوع الاستيراد
$import_type = $_GET['type'] ?? 'gateway';
$page_title = $import_type == 'riyal' ? 'معالجة استيراد بيانات ريال موبايل' : 'معالجة استيراد بيانات البوابة';
$csv_file = $import_type == 'riyal' ? 'database/ryal.csv' : 'database/import.csv';
$column_name = $import_type == 'riyal' ? 'ريال موبايل' : 'البوابة';

echo "<h2>$page_title</h2>";

try {
    if (!file_exists($csv_file)) {
        echo "<p style='color: red;'>ملف CSV غير موجود: $csv_file</p>";
        echo "<p><a href='import_instructions.php'>العودة لصفحة التعليمات</a></p>";
        exit;
    }

    echo "<p>بدء معالجة ملف: $csv_file</p>";
    echo "<p>حجم الملف: " . number_format(filesize($csv_file)) . " بايت</p>";

    // جلب الوكلاء من قاعدة البيانات
    $agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents ORDER BY agent_name");
    $agents_map = [];
    while ($agent = $agents_stmt->fetch()) {
        $agents_map[trim($agent['agent_name'])] = $agent['agent_id'];
    }

    echo "<h3>الوكلاء المتاحون في قاعدة البيانات:</h3>";
    echo "<ul>";
    foreach ($agents_map as $name => $id) {
        echo "<li>$name (ID: $id)</li>";
    }
    echo "</ul>";

    // قراءة ملف CSV
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        echo "<p style='color: red;'>لا يمكن فتح ملف CSV</p>";
        exit;
    }

    $row_num = 0;
    $headers = [];
    $imported_count = 0;
    $errors = [];
    $skipped_count = 0;

    echo "<h3>بدء عملية الاستيراد:</h3>";
    echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;'>";

    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
        $row_num++;

        if ($row_num == 1) {
            // السطر الأول يحتوي على العناوين
            $headers = $data;
            echo "<p><strong>عناوين الأعمدة:</strong></p>";
            echo "<ul>";
            for ($i = 0; $i < count($headers); $i++) {
                echo "<li>العمود " . ($i + 1) . ": " . htmlspecialchars($headers[$i]) . "</li>";
            }
            echo "</ul>";
            continue;
        }

        if (count($data) < 2) {
            $skipped_count++;
            continue; // تخطي الصفوف الفارغة
        }

        $agent_name = trim($data[0]); // العمود الأول = اسم الوكيل

        if (empty($agent_name)) {
            $skipped_count++;
            continue;
        }

        // البحث عن الوكيل في قاعدة البيانات
        $agent_id = null;
        $matched_name = '';

        foreach ($agents_map as $db_name => $db_id) {
            // مطابقة دقيقة
            if ($db_name === $agent_name) {
                $agent_id = $db_id;
                $matched_name = $db_name;
                break;
            }
            // مطابقة جزئية
            if (stripos($db_name, $agent_name) !== false || stripos($agent_name, $db_name) !== false) {
                $agent_id = $db_id;
                $matched_name = $db_name;
                break;
            }
        }

        if (!$agent_id) {
            $errors[] = "السطر $row_num: الوكيل '$agent_name' غير موجود في قاعدة البيانات";
            echo "<p style='color: red;'>❌ السطر $row_num: الوكيل '$agent_name' غير موجود</p>";
            continue;
        }

        echo "<p><strong>معالجة الوكيل:</strong> $agent_name → $matched_name (ID: $agent_id)</p>";

        // قراءة البيانات من الأعمدة (بدءاً من العمود الثاني)
        $row_imported = 0;
        for ($col = 1; $col < count($data); $col++) {
            $amount = trim($data[$col]);

            if (empty($amount) || !is_numeric($amount) || floatval($amount) <= 0) {
                continue; // تخطي القيم الفارغة أو غير الرقمية
            }

            // تحديد التاريخ بناءً على العمود
            // يمكن تخصيص هذا حسب تنسيق ملف Excel
            // افتراض أن الأعمدة تمثل أياماً متتالية (من الأحدث إلى الأقدم)
            $days_back = $col - 1;
            $date = date('Y-m-d', strtotime("-$days_back days"));

            // إدراج البيانات الجديدة في العمود المناسب
            if ($import_type == 'riyal') {
                // للريال موبايل: التحقق من وجود سجل أولاً
                $check_stmt = $pdo->prepare("SELECT id, gateway_amount FROM collections WHERE agent_id = ? AND collection_date = ?");
                $check_stmt->execute([$agent_id, $date]);
                $existing = $check_stmt->fetch();

                if ($existing) {
                    // تحديث السجل الموجود
                    $update_stmt = $pdo->prepare("UPDATE collections SET riyal_mobile_amount = ? WHERE agent_id = ? AND collection_date = ?");
                    $success = $update_stmt->execute([floatval($amount), $agent_id, $date]);
                } else {
                    // إنشاء سجل جديد
                    $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, 0, ?, ?)");
                    $success = $insert_stmt->execute([$agent_id, floatval($amount), $date]);
                }
            } else {
                // للبوابة: حذف السجل الموجود وإنشاء جديد
                $delete_stmt = $pdo->prepare("DELETE FROM collections WHERE agent_id = ? AND collection_date = ?");
                $delete_stmt->execute([$agent_id, $date]);

                $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, ?, 0, ?)");
                $success = $insert_stmt->execute([$agent_id, floatval($amount), $date]);
            }

            if ($success) {
                $imported_count++;
                $row_imported++;
                echo "<span style='color: green;'>  ✓ العمود $col: " . number_format($amount, 2) . " ريال في $date</span><br>";
            }
        }

        if ($row_imported > 0) {
            echo "<p style='color: blue;'>📊 تم استيراد $row_imported سجل للوكيل $matched_name</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد بيانات صالحة للوكيل $matched_name</p>";
        }
        echo "<hr style='margin: 5px 0;'>";
    }

    echo "</div>";
    fclose($handle);

    echo "<h3>نتائج الاستيراد:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p style='color: #155724;'><strong>✅ تم استيراد $imported_count سجل بنجاح</strong></p>";
    echo "<p>عدد الصفوف المعالجة: " . ($row_num - 1) . "</p>";
    echo "<p>عدد الصفوف المتخطاة: $skipped_count</p>";
    echo "</div>";

    if (!empty($errors)) {
        echo "<h4>الأخطاء والتحذيرات:</h4>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        foreach ($errors as $error) {
            echo "<p style='color: #721c24;'>$error</p>";
        }
        echo "</div>";
    }

    // عرض ملخص قاعدة البيانات
    $summary = $pdo->query("
        SELECT
            COUNT(*) as total_records,
            SUM(gateway_amount) as total_gateway,
            SUM(riyal_mobile_amount) as total_riyal,
            COUNT(DISTINCT agent_id) as active_agents,
            COUNT(DISTINCT collection_date) as unique_dates
        FROM collections
    ")->fetch();

    echo "<h4>ملخص قاعدة البيانات بعد الاستيراد:</h4>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p>📊 إجمالي السجلات: " . $summary['total_records'] . "</p>";
    echo "<p>💰 إجمالي البوابة: " . number_format($summary['total_gateway'], 2) . " ريال</p>";
    echo "<p>📱 إجمالي ريال موبايل: " . number_format($summary['total_riyal'], 2) . " ريال</p>";
    echo "<p>👥 عدد الوكلاء النشطين: " . $summary['active_agents'] . "</p>";
    echo "<p>📅 عدد التواريخ المختلفة: " . $summary['unique_dates'] . "</p>";
    echo "</div>";

    // حذف ملف CSV بعد الاستيراد الناجح
    if ($imported_count > 0) {
        unlink($csv_file);
        echo "<p style='color: #856404;'>🗑️ تم حذف ملف CSV بعد الاستيراد الناجح</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='main.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 العودة للصفحة الرئيسية</a></p>";
echo "<p><a href='import_instructions.php'>🔄 استيراد ملف آخر</a></p>";
?>
