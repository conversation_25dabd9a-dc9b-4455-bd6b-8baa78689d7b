const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const app = express();

// إعدادات الخادم
const PORT = process.env.PORT || 3000;
const PHP_PORT = process.env.PHP_PORT || 8080;

// Middleware للملفات الثابتة
app.use(express.static('.'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// دالة لتشغيل PHP
function runPHP(filePath, req, res) {
    const phpPath = 'php'; // تأكد من أن PHP في PATH
    
    // إعداد متغيرات البيئة لـ PHP
    const env = {
        ...process.env,
        REQUEST_METHOD: req.method,
        REQUEST_URI: req.url,
        QUERY_STRING: req.url.split('?')[1] || '',
        CONTENT_TYPE: req.headers['content-type'] || '',
        CONTENT_LENGTH: req.headers['content-length'] || '0',
        HTTP_HOST: req.headers.host,
        HTTP_USER_AGENT: req.headers['user-agent'] || '',
        SCRIPT_FILENAME: path.resolve(filePath),
        SCRIPT_NAME: req.path,
        SERVER_NAME: 'localhost',
        SERVER_PORT: PORT.toString(),
        GATEWAY_INTERFACE: 'CGI/1.1',
        SERVER_PROTOCOL: 'HTTP/1.1',
        REDIRECT_STATUS: '200'
    };

    // إضافة POST data إذا وجد
    let postData = '';
    if (req.method === 'POST') {
        postData = JSON.stringify(req.body);
    }

    const php = spawn(phpPath, ['-f', filePath], {
        env: env,
        stdio: ['pipe', 'pipe', 'pipe']
    });

    // إرسال POST data
    if (postData) {
        php.stdin.write(postData);
    }
    php.stdin.end();

    let output = '';
    let error = '';

    php.stdout.on('data', (data) => {
        output += data.toString();
    });

    php.stderr.on('data', (data) => {
        error += data.toString();
    });

    php.on('close', (code) => {
        if (code !== 0) {
            console.error('PHP Error:', error);
            res.status(500).send(`خطأ في تشغيل PHP: ${error}`);
            return;
        }

        // فصل headers عن body
        const parts = output.split('\r\n\r\n');
        if (parts.length > 1) {
            const headers = parts[0];
            const body = parts.slice(1).join('\r\n\r\n');
            
            // تطبيق headers
            headers.split('\r\n').forEach(header => {
                const [key, value] = header.split(': ');
                if (key && value) {
                    res.setHeader(key, value);
                }
            });
            
            res.send(body);
        } else {
            res.send(output);
        }
    });
}

// معالج ملفات PHP
app.all('*.php', (req, res) => {
    const filePath = path.join(__dirname, req.path);
    
    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
        res.status(404).send('الملف غير موجود');
        return;
    }
    
    runPHP(filePath, req, res);
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    // إعادة توجيه إلى main.php أو index.php
    if (fs.existsSync(path.join(__dirname, 'main.php'))) {
        res.redirect('/main.php');
    } else if (fs.existsSync(path.join(__dirname, 'index.php'))) {
        res.redirect('/index.php');
    } else {
        res.send(`
            <div style="text-align: center; font-family: Arial; margin-top: 50px;">
                <h1>🚀 خادم Node.js + PHP</h1>
                <p>الخادم يعمل على المنفذ ${PORT}</p>
                <h3>الملفات المتاحة:</h3>
                <ul style="list-style: none;">
                    <li><a href="/main.php">الصفحة الرئيسية</a></li>
                    <li><a href="/login.php">تسجيل الدخول</a></li>
                    <li><a href="/view_table_contents.php">محتويات الجداول</a></li>
                    <li><a href="/instant_mysql_setup.php">إعداد MySQL</a></li>
                    <li><a href="/db_viewer_simple.php">مستعرض قاعدة البيانات</a></li>
                </ul>
            </div>
        `);
    }
});

// API endpoints إضافية بـ Node.js
app.get('/api/status', (req, res) => {
    res.json({
        status: 'running',
        server: 'Node.js + PHP',
        port: PORT,
        timestamp: new Date().toISOString()
    });
});

app.get('/api/files', (req, res) => {
    const phpFiles = fs.readdirSync(__dirname)
        .filter(file => file.endsWith('.php'))
        .map(file => ({
            name: file,
            path: `/${file}`,
            size: fs.statSync(path.join(__dirname, file)).size
        }));
    
    res.json(phpFiles);
});

// معالج الأخطاء
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('خطأ في الخادم');
});

// بدء الخادم
app.listen(PORT, () => {
    console.log(`🚀 خادم Node.js + PHP يعمل على:`);
    console.log(`   http://localhost:${PORT}`);
    console.log(`📁 مجلد العمل: ${__dirname}`);
    console.log(`🐘 دعم PHP: متاح`);
    
    // فحص وجود PHP
    const testPhp = spawn('php', ['--version']);
    testPhp.on('close', (code) => {
        if (code === 0) {
            console.log('✅ PHP متاح ويعمل');
        } else {
            console.log('❌ PHP غير متاح - تأكد من تثبيته');
        }
    });
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});

module.exports = app;
