<?php
require_once 'config/database.php';

$message = '';
$error = '';

// معالجة التعديل المتعدد
if ($_POST && isset($_POST['bulk_edit'])) {
    try {
        $pdo->beginTransaction();
        $updated_count = 0;
        
        foreach ($_POST['agents'] as $agent_id => $new_name) {
            $new_name = trim($new_name);
            if (!empty($new_name)) {
                $stmt = $pdo->prepare("UPDATE agents SET agent_name = ? WHERE agent_id = ?");
                $stmt->execute([$new_name, $agent_id]);
                $updated_count++;
            }
        }
        
        $pdo->commit();
        $message = "تم تحديث $updated_count وكيل بنجاح";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "خطأ في التحديث: " . $e->getMessage();
    }
}

// جلب قائمة الوكلاء
$agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل متعدد للوكلاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">تعديل متعدد لأسماء الوكلاء</h2>
    
    <?php if ($message): ?>
        <div class="alert alert-success"><?= $message ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <span>تعديل أسماء الوكلاء</span>
                <div>
                    <button type="button" class="btn btn-sm btn-info" onclick="selectAll()">تحديد الكل</button>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="clearAll()">إلغاء التحديد</button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="select_all" onchange="toggleAll()">
                                </th>
                                <th>رقم الوكيل</th>
                                <th>الاسم الحالي</th>
                                <th>الاسم الجديد</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($agents as $agent): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="agent-checkbox" 
                                           onchange="toggleRow(<?= $agent['agent_id'] ?>)">
                                </td>
                                <td><?= $agent['agent_id'] ?></td>
                                <td><?= $agent['agent_name'] ?></td>
                                <td>
                                    <input type="text" name="agents[<?= $agent['agent_id'] ?>]" 
                                           class="form-control" value="<?= $agent['agent_name'] ?>"
                                           id="input_<?= $agent['agent_id'] ?>" disabled>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="text-center">
                    <button type="submit" name="bulk_edit" class="btn btn-primary">
                        حفظ التعديلات
                    </button>
                    <a href="manage_agents.php" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAll() {
    const selectAll = document.getElementById('select_all');
    const checkboxes = document.querySelectorAll('.agent-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        toggleRow(checkbox.closest('tr').querySelector('input[type="text"]').name.match(/\d+/)[0]);
    });
}

function toggleRow(agentId) {
    const checkbox = event.target;
    const input = document.getElementById('input_' + agentId);
    
    if (checkbox.checked) {
        input.disabled = false;
        input.focus();
        input.select();
    } else {
        input.disabled = true;
    }
}

function selectAll() {
    document.getElementById('select_all').checked = true;
    toggleAll();
}

function clearAll() {
    document.getElementById('select_all').checked = false;
    toggleAll();
}
</script>
</body>
</html>
