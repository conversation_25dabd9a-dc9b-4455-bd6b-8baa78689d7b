<?php
// إعدادات قاعدة بيانات MySQL
$mysql_config = [
    'host' => 'localhost',
    'dbname' => 'collections_system',
    'username' => 'root',
    'password' => '', // كلمة مرور MySQL (عادة فارغة في XAMPP)
    'charset' => 'utf8mb4',
    'port' => 3306
];

try {
    // الاتصال بـ MySQL لإنشاء قاعدة البيانات
    $pdo_temp = new PDO(
        "mysql:host={$mysql_config['host']};port={$mysql_config['port']};charset={$mysql_config['charset']}", 
        $mysql_config['username'], 
        $mysql_config['password']
    );
    $pdo_temp->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS `{$mysql_config['dbname']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO(
        "mysql:host={$mysql_config['host']};dbname={$mysql_config['dbname']};port={$mysql_config['port']};charset={$mysql_config['charset']}", 
        $mysql_config['username'], 
        $mysql_config['password']
    );
    
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // إنشاء الجداول
    
    // جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INT AUTO_INCREMENT PRIMARY KEY,
            employee_name VARCHAR(255) NOT NULL,
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            permissions ENUM('admin', 'user') DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // جدول الوكلاء
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS agents (
            agent_id INT AUTO_INCREMENT PRIMARY KEY,
            agent_name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // جدول التحصيلات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS collections (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agent_id INT,
            gateway_amount DECIMAL(15,2) DEFAULT 0.00,
            riyal_mobile_amount DECIMAL(15,2) DEFAULT 0.00,
            collection_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_agent_date (agent_id, collection_date),
            INDEX idx_collection_date (collection_date),
            FOREIGN KEY (agent_id) REFERENCES agents(agent_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // التحقق من وجود البيانات الأساسية وإضافتها
    
    // إضافة المستخدمين الافتراضيين
    $user_count = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    if ($user_count == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $user_password = password_hash('user123', PASSWORD_DEFAULT);
        
        $pdo->exec("
            INSERT INTO users (employee_name, username, password, permissions) VALUES
            ('المدير العام', 'admin', '$admin_password', 'admin'),
            ('مستخدم تجريبي', 'user1', '$user_password', 'user')
        ");
    }
    
    // إضافة الوكلاء الافتراضيين
    $agents_count = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
    if ($agents_count == 0) {
        $pdo->exec("
            INSERT INTO agents (agent_name) VALUES
            ('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
            ('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
            ('المترب'), ('باتكو'), ('بران')
        ");
    }
    
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}
?>
