<?php
require_once 'auth.php';
require_once 'config/database_sqlite.php';

$month = $_GET['month'] ?? date('Y-m');

// التحقق من صحة الشهر
if (!preg_match('/^\d{4}-\d{2}$/', $month)) {
    echo '<div class="alert alert-danger">شهر غير صحيح</div>';
    exit;
}

try {
    // جلب بيانات التحصيلات للشهر المحدد
    $stmt = $pdo->prepare("
        SELECT
            a.agent_name,
            SUM(c.gateway_amount) as total_gateway,
            SUM(c.riyal_mobile_amount) as total_riyal,
            SUM(c.gateway_amount + c.riyal_mobile_amount) as total_amount,
            COUNT(c.id) as days_count,
            MIN(c.collection_date) as first_collection,
            MAX(c.collection_date) as last_collection
        FROM agents a
        LEFT JOIN collections c ON a.agent_id = c.agent_id
            AND strftime('%Y-%m', c.collection_date) = ?
        GROUP BY a.agent_id, a.agent_name
        ORDER BY total_amount DESC, a.agent_name
    ");
    $stmt->execute([$month]);
    $collections = $stmt->fetchAll();

    // حساب الإجماليات
    $total_gateway = 0;
    $total_riyal = 0;
    $total_amount = 0;
    $active_agents = 0;

    foreach ($collections as $collection) {
        if ($collection['total_amount'] > 0) {
            $active_agents++;
        }
        $total_gateway += $collection['total_gateway'] ?? 0;
        $total_riyal += $collection['total_riyal'] ?? 0;
        $total_amount += $collection['total_amount'] ?? 0;
    }

    // تنسيق الشهر للعرض
    $month_names = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس', '04' => 'أبريل',
        '05' => 'مايو', '06' => 'يونيو', '07' => 'يوليو', '08' => 'أغسطس',
        '09' => 'سبتمبر', '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];

    $year = substr($month, 0, 4);
    $month_num = substr($month, 5, 2);
    $month_name = $month_names[$month_num] ?? $month_num;

    // حساب عدد أيام الشهر
    $days_in_month = date('t', strtotime($month . '-01'));

    ?>

    <div class="card">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-calendar-alt"></i>
                التقرير الشهري لشهر <?= $month_name ?> <?= $year ?>
            </h5>
        </div>
        <div class="card-body">
            <!-- ملخص سريع -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي البوابة</h6>
                            <h4 class="text-primary"><?= number_format($total_gateway, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي ريال موبايل</h6>
                            <h4 class="text-info"><?= number_format($total_riyal, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الإجمالي العام</h6>
                            <h4 class="text-success"><?= number_format($total_amount, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الوكلاء النشطون</h6>
                            <h4 class="text-warning"><?= $active_agents ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">المتوسط اليومي</h6>
                            <h4 class="text-secondary"><?= number_format($total_amount / $days_in_month, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">أيام الشهر</h6>
                            <h4 class="text-dark"><?= $days_in_month ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التفاصيل -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم الوكيل</th>
                            <th>البوابة</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                            <th>عدد الأيام</th>
                            <th>المتوسط اليومي</th>
                            <th>أول تحصيل</th>
                            <th>آخر تحصيل</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $rank = 1;
                        foreach ($collections as $collection):
                        ?>
                        <?php
                        $daily_avg = $collection['days_count'] > 0 ? $collection['total_amount'] / $collection['days_count'] : 0;
                        $row_class = '';
                        if ($rank <= 3 && $collection['total_amount'] > 0) {
                            $row_class = $rank == 1 ? 'table-warning' : ($rank == 2 ? 'table-info' : 'table-light');
                        } elseif ($collection['total_amount'] > 0) {
                            $row_class = 'table-success';
                        }
                        ?>
                        <tr class="<?= $row_class ?>">
                            <td>
                                <?php if ($collection['total_amount'] > 0): ?>
                                    <?php if ($rank == 1): ?>
                                        <i class="fas fa-trophy text-warning"></i> <?= $rank ?>
                                    <?php elseif ($rank == 2): ?>
                                        <i class="fas fa-medal text-secondary"></i> <?= $rank ?>
                                    <?php elseif ($rank == 3): ?>
                                        <i class="fas fa-award text-warning"></i> <?= $rank ?>
                                    <?php else: ?>
                                        <?= $rank ?>
                                    <?php endif; ?>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td><?= htmlspecialchars($collection['agent_name']) ?></td>
                            <td><?= number_format($collection['total_gateway'] ?? 0, 2) ?></td>
                            <td><?= number_format($collection['total_riyal'] ?? 0, 2) ?></td>
                            <td><strong><?= number_format($collection['total_amount'] ?? 0, 2) ?></strong></td>
                            <td><?= $collection['days_count'] ?></td>
                            <td><?= number_format($daily_avg, 2) ?></td>
                            <td><?= $collection['first_collection'] ? date('d/m', strtotime($collection['first_collection'])) : '-' ?></td>
                            <td><?= $collection['last_collection'] ? date('d/m', strtotime($collection['last_collection'])) : '-' ?></td>
                            <td>
                                <?php if ($collection['total_amount'] > 0): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php
                        if ($collection['total_amount'] > 0) $rank++;
                        endforeach;
                        ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th>-</th>
                            <th>الإجمالي العام</th>
                            <th><?= number_format($total_gateway, 2) ?></th>
                            <th><?= number_format($total_riyal, 2) ?></th>
                            <th><?= number_format($total_amount, 2) ?></th>
                            <th>-</th>
                            <th><?= number_format($total_amount / $days_in_month, 2) ?></th>
                            <th>-</th>
                            <th>-</th>
                            <th><?= $active_agents ?> وكيل</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-3">
                <div class="col-md-6">
                    <?php if ($total_amount == 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            لا توجد تحصيلات لهذا الشهر
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-chart-line"></i>
                            <strong>إحصائيات الشهر:</strong><br>
                            - إجمالي التحصيلات: <?= number_format($total_amount, 2) ?> ريال<br>
                            - متوسط التحصيل للوكيل: <?= number_format($active_agents > 0 ? $total_amount / $active_agents : 0, 2) ?> ريال<br>
                            - متوسط التحصيل اليومي: <?= number_format($total_amount / $days_in_month, 2) ?> ريال<br>
                            - نسبة الوكلاء النشطين: <?= number_format(($active_agents / count($collections)) * 100, 1) ?>%
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <i class="fas fa-calendar"></i>
                        <strong>معلومات الشهر:</strong><br>
                        - الشهر: <?= $month_name ?> <?= $year ?><br>
                        - عدد الأيام: <?= $days_in_month ?> يوم<br>
                        - عدد الوكلاء الإجمالي: <?= count($collections) ?><br>
                        - عدد الوكلاء النشطين: <?= $active_agents ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
