<?php
$action = $_POST['action'] ?? 'create_only';

// كلمات مرور محتملة
$passwords = ['', 'root', 'password', 'admin', '123456', 'mysql'];
$successful_password = null;
$pdo = null;

// البحث عن كلمة المرور الصحيحة
foreach ($passwords as $password) {
    try {
        $pdo = new PDO("mysql:host=localhost;port=3306", 'root', $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $successful_password = $password;
        break;
    } catch (PDOException $e) {
        continue;
    }
}

if (!$pdo) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>❌ لا يمكن الاتصال بـ MySQL</h6>";
    echo "<p>تأكد من تشغيل MySQL وجرب كلمات مرور مختلفة.</p>";
    echo "</div>";
    exit;
}

try {
    echo "<div class='alert alert-success'>";
    echo "<h6>✅ تم الاتصال بـ MySQL بنجاح!</h6>";
    echo "<p>كلمة المرور المستخدمة: " . ($successful_password === '' ? '(فارغة)' : $successful_password) . "</p>";
    echo "</div>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `collections_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: collections_system</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', $successful_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء الجداول
    echo "<h6>إنشاء الجداول:</h6>";
    
    // جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `user_id` INT AUTO_INCREMENT PRIMARY KEY,
            `employee_name` VARCHAR(255) NOT NULL,
            `username` VARCHAR(100) UNIQUE NOT NULL,
            `password` VARCHAR(255) NOT NULL,
            `permissions` ENUM('admin', 'user') DEFAULT 'user',
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `last_login` TIMESTAMP NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ جدول المستخدمين</p>";
    
    // جدول الوكلاء
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `agents` (
            `agent_id` INT AUTO_INCREMENT PRIMARY KEY,
            `agent_name` VARCHAR(255) NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ جدول الوكلاء</p>";
    
    // جدول التحصيلات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `collections` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `agent_id` INT,
            `gateway_amount` DECIMAL(15,2) DEFAULT 0.00,
            `riyal_mobile_amount` DECIMAL(15,2) DEFAULT 0.00,
            `collection_date` DATE NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX `idx_agent_date` (`agent_id`, `collection_date`),
            INDEX `idx_collection_date` (`collection_date`),
            FOREIGN KEY (`agent_id`) REFERENCES `agents`(`agent_id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ جدول التحصيلات</p>";
    
    if ($action === 'create_with_data' || $action === 'import_sqlite') {
        echo "<h6>إضافة البيانات:</h6>";
        
        // إضافة المستخدمين
        $user_count = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
        if ($user_count == 0) {
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $user_password = password_hash('user123', PASSWORD_DEFAULT);
            
            $pdo->exec("
                INSERT INTO users (employee_name, username, password, permissions) VALUES
                ('المدير العام', 'admin', '$admin_password', 'admin'),
                ('مستخدم تجريبي', 'user1', '$user_password', 'user')
            ");
            echo "<p>✅ تم إضافة المستخدمين الافتراضيين</p>";
        }
        
        // إضافة الوكلاء
        $agents_count = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
        if ($agents_count == 0) {
            $pdo->exec("
                INSERT INTO agents (agent_name) VALUES
                ('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
                ('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
                ('المترب'), ('باتكو'), ('بران')
            ");
            echo "<p>✅ تم إضافة الوكلاء الافتراضيين</p>";
        }
        
        if ($action === 'import_sqlite') {
            // نقل البيانات من SQLite
            $sqlite_file = __DIR__ . '/database/collections_system.db';
            if (file_exists($sqlite_file)) {
                try {
                    $sqlite_pdo = new PDO("sqlite:$sqlite_file");
                    $sqlite_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // نقل التحصيلات
                    $collections = $sqlite_pdo->query("SELECT * FROM collections")->fetchAll();
                    if (!empty($collections)) {
                        $pdo->exec("DELETE FROM collections"); // مسح البيانات الموجودة
                        
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) 
                            VALUES (?, ?, ?, ?)
                        ");
                        
                        $transferred = 0;
                        foreach ($collections as $collection) {
                            $insert_stmt->execute([
                                $collection['agent_id'],
                                $collection['gateway_amount'],
                                $collection['riyal_mobile_amount'],
                                $collection['collection_date']
                            ]);
                            $transferred++;
                        }
                        
                        echo "<p>✅ تم نقل $transferred سجل تحصيل من SQLite</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='text-warning'>⚠️ لا يمكن نقل البيانات من SQLite: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p class='text-info'>ℹ️ ملف SQLite غير موجود</p>";
            }
        } elseif ($action === 'create_with_data') {
            // إضافة بيانات تجريبية
            $collections_count = $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch()['count'];
            if ($collections_count == 0) {
                $pdo->exec("
                    INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES
                    (1, 25000.50, 15000.25, CURDATE()),
                    (2, 22000.75, 18000.50, CURDATE()),
                    (3, 18000.25, 12000.75, CURDATE()),
                    (4, 30000.00, 20000.00, CURDATE()),
                    (5, 16000.50, 14000.25, CURDATE()),
                    (6, 28000.75, 22000.50, CURDATE()),
                    (7, 19000.25, 16000.75, CURDATE()),
                    (8, 24000.50, 19000.25, CURDATE()),
                    (9, 21000.75, 17000.50, CURDATE()),
                    (10, 26000.25, 21000.75, CURDATE()),
                    (11, 23000.50, 18500.25, CURDATE())
                ");
                echo "<p>✅ تم إضافة بيانات تحصيلات تجريبية</p>";
            }
        }
    }
    
    // عرض الإحصائيات النهائية
    $users = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    $agents = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
    $collections = $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch()['count'];
    
    echo "<div class='alert alert-success mt-3'>";
    echo "<h6>🎉 تم إنشاء قاعدة البيانات بنجاح!</h6>";
    echo "<ul>";
    echo "<li><strong>قاعدة البيانات:</strong> collections_system</li>";
    echo "<li><strong>المستخدمون:</strong> $users</li>";
    echo "<li><strong>الوكلاء:</strong> $agents</li>";
    echo "<li><strong>التحصيلات:</strong> $collections</li>";
    echo "</ul>";
    echo "</div>";
    
    // حفظ إعدادات الاتصال
    $config_content = "<?php
// إعدادات قاعدة بيانات MySQL
\$mysql_config = [
    'host' => 'localhost',
    'dbname' => 'collections_system',
    'username' => 'root',
    'password' => '$successful_password',
    'charset' => 'utf8mb4',
    'port' => 3306
];

try {
    \$pdo = new PDO(
        \"mysql:host={\$mysql_config['host']};dbname={\$mysql_config['dbname']};port={\$mysql_config['port']};charset={\$mysql_config['charset']}\", 
        \$mysql_config['username'], 
        \$mysql_config['password']
    );
    
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
} catch (PDOException \$e) {
    die(\"خطأ في الاتصال بقاعدة البيانات: \" . \$e->getMessage());
}
?>";
    
    file_put_contents(__DIR__ . '/config/database_mysql_working.php', $config_content);
    echo "<p>✅ تم حفظ إعدادات الاتصال</p>";
    
    echo "<div class='mt-3'>";
    echo "<a href='switch_to_mysql.php' class='btn btn-primary'>تحويل النظام لاستخدام MySQL</a> ";
    echo "<a href='main.php' class='btn btn-success'>اختبار النظام</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>❌ خطأ في إنشاء قاعدة البيانات:</h6>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
