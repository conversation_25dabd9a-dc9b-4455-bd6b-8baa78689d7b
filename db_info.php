<?php
try {
    // معلومات قاعدة البيانات
    $db_file = __DIR__ . '/database/collections_system.db';
    $db_size = file_exists($db_file) ? filesize($db_file) : 0;
    $db_size_mb = round($db_size / 1024 / 1024, 2);
    
    echo "<div class='card'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h5><i class='fas fa-info-circle'></i> معلومات قاعدة البيانات</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<h6>معلومات عامة:</h6>";
    echo "<ul class='list-unstyled'>";
    echo "<li><strong>نوع قاعدة البيانات:</strong> SQLite</li>";
    echo "<li><strong>مسار الملف:</strong> <code>$db_file</code></li>";
    echo "<li><strong>حجم الملف:</strong> $db_size_mb MB</li>";
    echo "<li><strong>آخر تعديل:</strong> " . date('Y-m-d H:i:s', filemtime($db_file)) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='col-md-6'>";
    echo "<h6>إحصائيات الجداول:</h6>";
    
    // إحصائيات الجداول
    $tables_info = [];
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
    
    foreach ($tables as $table) {
        $table_name = $table['name'];
        $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
        $tables_info[] = ['name' => $table_name, 'count' => $count];
    }
    
    echo "<table class='table table-sm'>";
    echo "<thead><tr><th>الجدول</th><th>عدد السجلات</th></tr></thead>";
    echo "<tbody>";
    foreach ($tables_info as $info) {
        echo "<tr>";
        echo "<td><a href='?action=table&table={$info['name']}'>{$info['name']}</a></td>";
        echo "<td><span class='badge bg-primary'>{$info['count']}</span></td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    echo "</div>";
    
    // معلومات SQLite
    echo "<hr>";
    echo "<h6>معلومات SQLite:</h6>";
    $sqlite_version = $pdo->query("SELECT sqlite_version() as version")->fetch()['version'];
    echo "<p><strong>إصدار SQLite:</strong> $sqlite_version</p>";
    
    // إعدادات قاعدة البيانات
    echo "<h6>إعدادات قاعدة البيانات:</h6>";
    $pragmas = [
        'journal_mode' => 'وضع المجلة',
        'synchronous' => 'المزامنة',
        'cache_size' => 'حجم التخزين المؤقت',
        'page_size' => 'حجم الصفحة',
        'encoding' => 'الترميز'
    ];
    
    echo "<table class='table table-sm'>";
    foreach ($pragmas as $pragma => $description) {
        try {
            $value = $pdo->query("PRAGMA $pragma")->fetch();
            $value = is_array($value) ? array_values($value)[0] : $value;
            echo "<tr><td>$description</td><td><code>$value</code></td></tr>";
        } catch (Exception $e) {
            echo "<tr><td>$description</td><td class='text-muted'>غير متاح</td></tr>";
        }
    }
    echo "</table>";
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ في قراءة معلومات قاعدة البيانات</h5>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
