<?php
require_once 'config/database_sqlite.php';

echo "<h2>معالجة استيراد بيانات ريال موبايل</h2>";

$csv_file = 'E:\agent\database\ryal.csv';

try {
    if (!file_exists($csv_file)) {
        echo "<p style='color: red;'>ملف CSV غير موجود: $csv_file</p>";
        exit;
    }
    
    // جلب الوكلاء من قاعدة البيانات
    $agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents ORDER BY agent_name");
    $agents_map = [];
    while ($agent = $agents_stmt->fetch()) {
        $agents_map[trim($agent['agent_name'])] = $agent['agent_id'];
    }
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>👥 الوكلاء المتاحون في قاعدة البيانات:</h4>";
    echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
    foreach ($agents_map as $name => $id) {
        echo "<span style='background: #007bff; color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;'>$name</span>";
    }
    echo "</div>";
    echo "</div>";
    
    // قراءة ملف CSV
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        echo "<p style='color: red;'>لا يمكن فتح ملف CSV</p>";
        exit;
    }
    
    $row_num = 0;
    $headers = [];
    $imported_count = 0;
    $updated_count = 0;
    $errors = [];
    $skipped_count = 0;
    
    echo "<h3>🔄 بدء عملية الاستيراد:</h3>";
    echo "<div style='max-height: 500px; overflow-y: auto; border: 2px solid #007bff; padding: 15px; background: #f8f9fa; border-radius: 10px;'>";
    
    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
        $row_num++;
        
        if ($row_num == 1) {
            // السطر الأول يحتوي على العناوين
            $headers = $data;
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin-bottom: 15px;'>";
            echo "<h5>📊 عناوين الأعمدة:</h5>";
            echo "<div style='display: flex; flex-wrap: wrap; gap: 5px;'>";
            for ($i = 0; $i < count($headers); $i++) {
                $bg_color = $i == 0 ? '#28a745' : '#17a2b8';
                echo "<span style='background: $bg_color; color: white; padding: 3px 8px; border-radius: 10px; font-size: 11px;'>العمود " . ($i + 1) . ": " . htmlspecialchars($headers[$i]) . "</span>";
            }
            echo "</div>";
            echo "</div>";
            continue;
        }
        
        if (count($data) < 2) {
            $skipped_count++;
            continue;
        }
        
        $agent_name = trim($data[0]);
        
        if (empty($agent_name)) {
            $skipped_count++;
            continue;
        }
        
        // البحث عن الوكيل
        $agent_id = null;
        $matched_name = '';
        
        foreach ($agents_map as $db_name => $db_id) {
            if ($db_name === $agent_name || 
                stripos($db_name, $agent_name) !== false || 
                stripos($agent_name, $db_name) !== false) {
                $agent_id = $db_id;
                $matched_name = $db_name;
                break;
            }
        }
        
        if (!$agent_id) {
            $errors[] = "السطر $row_num: الوكيل '$agent_name' غير موجود";
            echo "<div style='background: #f8d7da; padding: 8px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ <strong>السطر $row_num:</strong> الوكيل '$agent_name' غير موجود في قاعدة البيانات";
            echo "</div>";
            continue;
        }
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h6 style='margin: 0 0 10px 0;'>👤 <strong>معالجة الوكيل:</strong> $agent_name → $matched_name (ID: $agent_id)</h6>";
        
        $row_imported = 0;
        $row_updated = 0;
        
        for ($col = 1; $col < count($data); $col++) {
            $amount = trim($data[$col]);
            
            if (empty($amount) || !is_numeric($amount) || floatval($amount) <= 0) {
                continue;
            }
            
            // تحديد التاريخ بناءً على العمود
            $days_back = $col - 1;
            $date = date('Y-m-d', strtotime("-$days_back days"));
            $formatted_date = date('d/m/Y', strtotime($date));
            
            // التحقق من وجود سجل
            $check_stmt = $pdo->prepare("SELECT id, gateway_amount FROM collections WHERE agent_id = ? AND collection_date = ?");
            $check_stmt->execute([$agent_id, $date]);
            $existing = $check_stmt->fetch();
            
            if ($existing) {
                // تحديث السجل الموجود
                $update_stmt = $pdo->prepare("
                    UPDATE collections 
                    SET riyal_mobile_amount = ? 
                    WHERE agent_id = ? AND collection_date = ?
                ");
                
                if ($update_stmt->execute([floatval($amount), $agent_id, $date])) {
                    $updated_count++;
                    $row_updated++;
                    echo "<div style='background: #cce5ff; padding: 5px; border-radius: 3px; margin: 2px 0; font-size: 13px;'>";
                    echo "🔄 <strong>العمود $col:</strong> تحديث " . number_format($amount, 2) . " ريال موبايل في $formatted_date";
                    echo " (البوابة الموجودة: " . number_format($existing['gateway_amount'], 2) . ")";
                    echo "</div>";
                }
            } else {
                // إنشاء سجل جديد
                $insert_stmt = $pdo->prepare("
                    INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) 
                    VALUES (?, 0, ?, ?)
                ");
                
                if ($insert_stmt->execute([$agent_id, floatval($amount), $date])) {
                    $imported_count++;
                    $row_imported++;
                    echo "<div style='background: #d4edda; padding: 5px; border-radius: 3px; margin: 2px 0; font-size: 13px;'>";
                    echo "✅ <strong>العمود $col:</strong> سجل جديد " . number_format($amount, 2) . " ريال موبايل في $formatted_date";
                    echo "</div>";
                }
            }
        }
        
        echo "<div style='background: #e7f3ff; padding: 5px; border-radius: 3px; margin-top: 5px; font-weight: bold; font-size: 14px;'>";
        echo "📊 النتيجة: $row_imported سجل جديد، $row_updated سجل محدث";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    fclose($handle);
    
    // نتائج الاستيراد
    echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h3 style='margin: 0 0 15px 0;'>🎉 نتائج استيراد ريال موبايل</h3>";
    echo "<div style='display: flex; justify-content: space-around; flex-wrap: wrap;'>";
    echo "<div><h4 style='margin: 5px;'>$imported_count</h4><p style='margin: 0;'>سجل جديد</p></div>";
    echo "<div><h4 style='margin: 5px;'>$updated_count</h4><p style='margin: 0;'>سجل محدث</p></div>";
    echo "<div><h4 style='margin: 5px;'>" . ($imported_count + $updated_count) . "</h4><p style='margin: 0;'>إجمالي العمليات</p></div>";
    echo "</div>";
    echo "</div>";
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24;'>⚠️ الأخطاء والتحذيرات:</h4>";
        foreach ($errors as $error) {
            echo "<p style='color: #721c24; margin: 5px 0;'>• $error</p>";
        }
        echo "</div>";
    }
    
    // ملخص قاعدة البيانات
    $summary = $pdo->query("
        SELECT 
            COUNT(*) as total_records,
            SUM(gateway_amount) as total_gateway,
            SUM(riyal_mobile_amount) as total_riyal,
            SUM(gateway_amount + riyal_mobile_amount) as grand_total,
            COUNT(DISTINCT agent_id) as active_agents,
            COUNT(CASE WHEN gateway_amount > 0 THEN 1 END) as records_with_gateway,
            COUNT(CASE WHEN riyal_mobile_amount > 0 THEN 1 END) as records_with_riyal
        FROM collections
    ")->fetch();
    
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>📈 ملخص قاعدة البيانات النهائي:</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;'>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h5 style='color: #007bff; margin: 0;'>" . $summary['total_records'] . "</h5>";
    echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>إجمالي السجلات</p>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h5 style='color: #28a745; margin: 0;'>" . number_format($summary['total_gateway'], 0) . "</h5>";
    echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>إجمالي البوابة</p>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h5 style='color: #17a2b8; margin: 0;'>" . number_format($summary['total_riyal'], 0) . "</h5>";
    echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>إجمالي ريال موبايل</p>";
    echo "</div>";
    
    echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h5 style='color: #dc3545; margin: 0;'>" . number_format($summary['grand_total'], 0) . "</h5>";
    echo "<p style='margin: 5px 0 0 0; color: #6c757d;'>الإجمالي العام</p>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h4>❌ خطأ في العملية:</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='main.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>🏠 العودة للصفحة الرئيسية</a>";
echo "<a href='import_riyal_mobile.php' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 10px; display: inline-block;'>🔄 استيراد ملف آخر</a>";
echo "</div>";
?>
