<?php
require_once 'config/database.php';

$message = '';
$error = '';

// معالجة استيراد البيانات
if ($_POST) {
    $import_date = $_POST['import_date'] ?? '';
    $import_data = $_POST['import_data'] ?? '';
    
    if ($import_date && $import_data) {
        try {
            // تنظيف البيانات المدخلة
            $lines = explode("\n", trim($import_data));
            $imported_count = 0;
            $errors = [];
            
            foreach ($lines as $line_num => $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                // تقسيم البيانات بالمسافات المتعددة أو التاب
                $data = preg_split('/\s+/', $line, 3);
                
                if (count($data) >= 1) {
                    $agent_name = trim($data[0]);
                    
                    // العمود الأول = البوابة (إذا كان فارغ = 0)
                    $gateway_amount = 0;
                    if (isset($data[1]) && !empty(trim($data[1]))) {
                        $gateway_amount = floatval(str_replace(',', '', trim($data[1])));
                    }
                    
                    // العمود الثاني = الريال موبايل (إذا كان فارغ = 0)
                    $riyal_amount = 0;
                    if (isset($data[2]) && !empty(trim($data[2]))) {
                        $riyal_amount = floatval(str_replace(',', '', trim($data[2])));
                    }
                    
                    // البحث عن الوكيل
                    $agent_stmt = $pdo->prepare("SELECT agent_id FROM agents WHERE agent_name LIKE ?");
                    $agent_stmt->execute(["%$agent_name%"]);
                    $agent_id = $agent_stmt->fetchColumn();
                    
                    if ($agent_id) {
                        // التحقق من وجود البيانات لنفس التاريخ
                        $check_stmt = $pdo->prepare("SELECT id FROM collections WHERE agent_id = ? AND collection_date = ?");
                        $check_stmt->execute([$agent_id, $import_date]);
                        
                        if ($check_stmt->fetchColumn()) {
                            // تحديث البيانات الموجودة
                            $update_stmt = $pdo->prepare("UPDATE collections SET gateway_amount = ?, riyal_mobile_amount = ? WHERE agent_id = ? AND collection_date = ?");
                            $update_stmt->execute([$gateway_amount, $riyal_amount, $agent_id, $import_date]);
                        } else {
                            // إدراج بيانات جديدة
                            $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, ?, ?, ?)");
                            $insert_stmt->execute([$agent_id, $gateway_amount, $riyal_amount, $import_date]);
                        }
                        $imported_count++;
                    } else {
                        $errors[] = "السطر " . ($line_num + 1) . ": الوكيل '$agent_name' غير موجود";
                    }
                } else {
                    $errors[] = "السطر " . ($line_num + 1) . ": اسم الوكيل مطلوب";
                }
            }
            
            $message = "تم استيراد $imported_count سجل بنجاح";
            if (!empty($errors)) {
                $error = implode("<br>", $errors);
            }
            
        } catch (Exception $e) {
            $error = "خطأ في الاستيراد: " . $e->getMessage();
        }
    } else {
        $error = "يرجى تحديد التاريخ وإدخال البيانات";
    }
}

// جلب قائمة الوكلاء للمرجع
$agents = $pdo->query("SELECT agent_name FROM agents ORDER BY agent_name")->fetchAll(PDO::FETCH_COLUMN);
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>استيراد البيانات النصي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .example-box { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
        .agents-list { max-height: 200px; overflow-y: auto; }
        .format-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">استيراد بيانات التحصيلات (نصي)</h2>
    
    <?php if ($message): ?>
        <div class="alert alert-success"><?= $message ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">استيراد البيانات</div>
                <div class="card-body">
                    <div class="format-info mb-3">
                        <h6><strong>تنسيق البيانات:</strong></h6>
                        <p class="mb-1">اسم_الوكيل [مسافة] مبلغ_البوابة [مسافة] مبلغ_الريال_موبايل</p>
                        <small class="text-muted">
                            • يمكن ترك أي عمود فارغ (سيعتبر صفر)<br>
                            • استخدم المسافات للفصل بين الأعمدة<br>
                            • العمود الأول: البوابة، العمود الثاني: الريال موبايل
                        </small>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">تاريخ التحصيل:</label>
                            <input type="date" name="import_date" class="form-control" required 
                                   value="<?= $_POST['import_date'] ?? date('Y-m-d') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">البيانات:</label>
                            <textarea name="import_data" class="form-control" rows="12" 
                                      placeholder="الوكيل الأول 1500.50 2000.75
الشرق 1200.00 1800.25
جوث  900.75
الشمال  1100.50
سقف 800.00 1200.75" 
                                      style="font-family: monospace; direction: ltr; text-align: left;"
                                      required><?= $_POST['import_data'] ?? '' ?></textarea>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary">استيراد البيانات</button>
                            <button type="button" class="btn btn-secondary" onclick="clearForm()">مسح</button>
                            <button type="button" class="btn btn-info" onclick="showExample()">مثال</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">قائمة الوكلاء المتاحين</div>
                <div class="card-body agents-list">
                    <?php foreach ($agents as $agent): ?>
                        <div class="badge bg-secondary me-1 mb-1"><?= $agent ?></div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">أمثلة على التنسيق</div>
                <div class="card-body">
                    <div class="example-box">
                        <strong>مثال 1 - كلا العمودين:</strong><br>
                        الوكيل الأول 1500.50 2000.75<br>
                        الشرق 1200.00 1800.25
                    </div>
                    
                    <div class="example-box">
                        <strong>مثال 2 - البوابة فقط:</strong><br>
                        جوث 900.75<br>
                        الشمال 1100.50
                    </div>
                    
                    <div class="example-box">
                        <strong>مثال 3 - الريال موبايل فقط:</strong><br>
                        سقف  1200.75<br>
                        الأثير  800.50
                    </div>
                    
                    <div class="example-box">
                        <strong>مثال 4 - مختلط:</strong><br>
                        الوكيل الأول 1500.50 2000.75<br>
                        الشرق 1200.00<br>
                        جوث  900.75<br>
                        الشمال 800.00 1100.50
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">ملاحظات مهمة</div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li>✅ العمود الأول = البوابة</li>
                        <li>✅ العمود الثاني = الريال موبايل</li>
                        <li>✅ يمكن ترك أي عمود فارغ</li>
                        <li>✅ استخدم المسافات للفصل</li>
                        <li>⚠️ اسم الوكيل مطلوب دائماً</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="text-center mt-3">
        <a href="quick_import.php" class="btn btn-info">استيراد سريع</a>
        <a href="import_excel.php" class="btn btn-success">استيراد Excel</a>
        <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
    </div>
</div>

<script>
function clearForm() {
    document.querySelector('textarea[name="import_data"]').value = '';
}

function showExample() {
    const example = `الوكيل الأول 1500.50 2000.75
الشرق 1200.00 1800.25
جوث 900.75
الشمال  1100.50
سقف 800.00 1200.75
الأثير  
المريخ 500.00 750.25`;
    
    document.querySelector('textarea[name="import_data"]').value = example;
}

// تحسين تجربة الإدخال
document.querySelector('textarea[name="import_data"]').addEventListener('input', function(e) {
    // تنظيف المسافات الزائدة
    let lines = this.value.split('\n');
    let cleanedLines = lines.map(line => {
        return line.replace(/\s+/g, ' ').trim();
    });
    
    // تحديث القيمة بدون تغيير موضع المؤشر
    if (this.value !== cleanedLines.join('\n')) {
        let cursorPos = this.selectionStart;
        this.value = cleanedLines.join('\n');
        this.setSelectionRange(cursorPos, cursorPos);
    }
});
</script>
</body>
</html>


