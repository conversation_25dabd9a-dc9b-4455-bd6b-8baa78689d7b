body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    border-radius: 8px;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.table th {
    background-color: #495057;
    color: white;
    font-weight: 600;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.alert {
    border-radius: 6px;
    border: none;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.text-end {
    font-weight: 600;
}

.table-success th,
.table-success td {
    font-weight: bold;
    font-size: 1.1em;
}

@media print {
    .btn, form, .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
