/* تحميل الخط العربي */
@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('../fonts/<PERSON>-Art-bold.ttf') format('truetype');
    font-weight: bold;
    font-display: swap;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* تصميم الصفحة الواحدة */
.main-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin: 20px;
    min-height: calc(100vh - 40px);
}

/* شريط الأيقونات العلوي */
.top-navigation {
    background: linear-gradient(45deg, #2c3e50, #3498db);
    border-radius: 15px 15px 0 0;
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-icons {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.nav-icon {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    text-align: center;
    min-width: 120px;
    backdrop-filter: blur(10px);
}

.nav-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.nav-icon.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: #fff;
    color: white;
}

/* منطقة المحتوى */
.content-area {
    padding: 30px;
    min-height: 500px;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* أيقونات التقارير الفرعية */
.sub-navigation {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.sub-nav-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.sub-nav-icon {
    background: rgba(52, 152, 219, 0.1);
    border: 2px solid rgba(52, 152, 219, 0.3);
    border-radius: 10px;
    padding: 10px 15px;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.3s ease;
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    text-align: center;
    min-width: 100px;
    font-size: 0.9rem;
}

.sub-nav-icon:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.6);
    transform: translateY(-2px);
    color: #2c3e50;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.sub-nav-icon.active {
    background: linear-gradient(45deg, #3498db, #2980b9);
    border-color: #2980b9;
    color: white;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.sub-nav-icon.active:hover {
    color: white;
}

/* إعدادات التقارير */
.report-settings {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.report-settings.active {
    display: block;
}

.report-settings-area {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

/* معلومات المستخدم */
.user-info {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    cursor: default !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.8rem;
}

.user-info:hover {
    transform: none !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

/* تصميم صفحة تسجيل الدخول */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 400px;
    width: 100%;
}

.login-title {
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    font-family: 'Khalid-Art', sans-serif;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    padding: 12px 15px;
    width: 100%;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.btn-login {
    background: linear-gradient(45deg, #3498db, #2c3e50);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    color: white;
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    width: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.card {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.table th {
    background: linear-gradient(45deg, #2c3e50, #3498db);
    color: white;
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    border: none;
}

.table td {
    font-family: 'Khalid-Art', sans-serif;
    vertical-align: middle;
}

.btn {
    border-radius: 8px;
    font-family: 'Khalid-Art', sans-serif;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    padding: 10px 20px;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

.btn-success {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.btn-warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.btn-danger {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.alert {
    border-radius: 6px;
    border: none;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.text-end {
    font-weight: 600;
}

.table-success th,
.table-success td {
    font-weight: bold;
    font-size: 1.1em;
}

@media print {
    .btn, form, .no-print {
        display: none !important;
    }

    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
