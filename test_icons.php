<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار الأيقونات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/icons.css" rel="stylesheet">
    <style>
        .icon-demo {
            padding: 15px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
            background: #f8f9fa;
        }
        .icon-demo:hover {
            background: #e9ecef;
            transform: scale(1.05);
            transition: all 0.3s;
        }
    </style>
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary text-center">
        <h1><span class="icon icon-star icon-lg"></span> اختبار الأيقونات الجديدة</h1>
        <p>عرض جميع الأيقونات المتاحة في النظام</p>
    </div>

    <!-- أيقونات أساسية -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><span class="icon icon-home"></span> الأيقونات الأساسية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-home icon-2x"></span>
                        <br><small>الرئيسية</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-plus icon-2x"></span>
                        <br><small>إضافة</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-chart icon-2x"></span>
                        <br><small>تقارير</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-users icon-2x"></span>
                        <br><small>المستخدمين</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-building icon-2x"></span>
                        <br><small>الوكلاء</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-calendar icon-2x"></span>
                        <br><small>التاريخ</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أيقونات النظام -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><span class="icon icon-settings"></span> أيقونات النظام</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-door icon-2x"></span>
                        <br><small>البوابة</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-mobile icon-2x"></span>
                        <br><small>ريال موبايل</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-money icon-2x"></span>
                        <br><small>التحصيلات</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-database icon-2x"></span>
                        <br><small>قاعدة البيانات</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-save icon-2x"></span>
                        <br><small>حفظ</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-settings icon-2x"></span>
                        <br><small>الإعدادات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أيقونات الحالة -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5><span class="icon icon-info"></span> أيقونات الحالة</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-check icon-2x icon-success"></span>
                        <br><small>نجح</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-error icon-2x icon-danger"></span>
                        <br><small>خطأ</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-warning icon-2x icon-warning"></span>
                        <br><small>تحذير</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-info icon-2x icon-info"></span>
                        <br><small>معلومات</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-online icon-2x"></span>
                        <br><small>متصل</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-offline icon-2x"></span>
                        <br><small>غير متصل</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أيقونات العمليات -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5><span class="icon icon-edit"></span> أيقونات العمليات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-edit icon-2x"></span>
                        <br><small>تعديل</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-delete icon-2x"></span>
                        <br><small>حذف</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-search icon-2x"></span>
                        <br><small>بحث</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-print icon-2x"></span>
                        <br><small>طباعة</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-download icon-2x"></span>
                        <br><small>تحميل</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="icon-demo">
                        <span class="icon icon-refresh icon-2x"></span>
                        <br><small>تحديث</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الأزرار -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5><span class="icon icon-star"></span> اختبار الأزرار مع الأيقونات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-2">
                    <button class="btn btn-primary w-100">
                        <span class="icon icon-plus"></span> إضافة جديد
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-success w-100">
                        <span class="icon icon-save"></span> حفظ البيانات
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-warning w-100">
                        <span class="icon icon-edit"></span> تعديل
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-danger w-100">
                        <span class="icon icon-delete"></span> حذف
                    </button>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-3 mb-2">
                    <button class="btn btn-info w-100">
                        <span class="icon icon-chart"></span> التقارير
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-secondary w-100">
                        <span class="icon icon-users"></span> المستخدمين
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-dark w-100">
                        <span class="icon icon-settings"></span> الإعدادات
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-outline-primary w-100">
                        <span class="icon icon-home"></span> الرئيسية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار التأثيرات -->
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5><span class="icon icon-star"></span> اختبار التأثيرات</h5>
        </div>
        <div class="card-body text-center">
            <div class="row">
                <div class="col-md-4">
                    <h6>أيقونة دوارة</h6>
                    <span class="icon icon-refresh icon-3x icon-spin"></span>
                </div>
                <div class="col-md-4">
                    <h6>أيقونة نابضة</h6>
                    <span class="icon icon-heart icon-3x icon-pulse icon-danger"></span>
                </div>
                <div class="col-md-4">
                    <h6>أيقونة كبيرة</h6>
                    <span class="icon icon-star icon-3x icon-warning"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الأحجام -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><span class="icon icon-star"></span> اختبار الأحجام</h5>
        </div>
        <div class="card-body text-center">
            <div class="row">
                <div class="col-md-2">
                    <span class="icon icon-home icon-sm"></span>
                    <br><small>صغير</small>
                </div>
                <div class="col-md-2">
                    <span class="icon icon-home"></span>
                    <br><small>عادي</small>
                </div>
                <div class="col-md-2">
                    <span class="icon icon-home icon-lg"></span>
                    <br><small>كبير</small>
                </div>
                <div class="col-md-2">
                    <span class="icon icon-home icon-xl"></span>
                    <br><small>كبير جداً</small>
                </div>
                <div class="col-md-2">
                    <span class="icon icon-home icon-2x"></span>
                    <br><small>2x</small>
                </div>
                <div class="col-md-2">
                    <span class="icon icon-home icon-3x"></span>
                    <br><small>3x</small>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط التنقل -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5><span class="icon icon-link"></span> روابط التنقل</h5>
        </div>
        <div class="card-body text-center">
            <div class="btn-group" role="group">
                <a href="/main_fixed.php" class="btn btn-primary">
                    <span class="icon icon-home"></span> الصفحة الرئيسية
                </a>
                <a href="/add_collection_fixed.php" class="btn btn-success">
                    <span class="icon icon-plus"></span> إضافة تحصيلات
                </a>
                <a href="/manage_agents_fixed.php" class="btn btn-warning">
                    <span class="icon icon-users"></span> إدارة الوكلاء
                </a>
                <a href="/daily_report_new.php" class="btn btn-info">
                    <span class="icon icon-chart"></span> التقرير اليومي
                </a>
                <a href="/system_check.php" class="btn btn-secondary">
                    <span class="icon icon-check"></span> فحص النظام
                </a>
            </div>
        </div>
    </div>
</div>

<script src="/assets/icons.js"></script>
<script>
// اختبار إضافة أيقونات ديناميكياً
setTimeout(() => {
    console.log('تم تحميل صفحة اختبار الأيقونات');
    
    // إضافة تأثير hover للأيقونات
    document.querySelectorAll('.icon-demo').forEach(demo => {
        demo.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.icon');
            if (icon) {
                icon.style.transform = 'scale(1.2)';
                icon.style.transition = 'transform 0.3s';
            }
        });
        
        demo.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.icon');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}, 1000);
</script>

</body>
</html>
