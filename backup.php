<?php
require_once 'config/database.php';

if ($_POST['action'] ?? '' === 'backup') {
    $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    // إعداد headers للتحميل
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $backup_file . '"');
    
    // بداية ملف النسخة الاحتياطية
    echo "-- نسخة احتياطية لقاعدة بيانات التحصيلات\n";
    echo "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
    
    // نسخ احتياطي لجدول الوكلاء
    echo "-- جدول الوكلاء\n";
    echo "DROP TABLE IF EXISTS agents;\n";
    echo "CREATE TABLE agents (agent_id INT PRIMARY KEY AUTO_INCREMENT, agent_name VARCHAR(100) NOT NULL);\n";
    
    $agents = $pdo->query("SELECT * FROM agents")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($agents as $agent) {
        echo "INSERT INTO agents VALUES ({$agent['agent_id']}, '{$agent['agent_name']}');\n";
    }
    
    echo "\n-- جدول التحصيلات\n";
    echo "DROP TABLE IF EXISTS collections;\n";
    echo "CREATE TABLE collections (id INT PRIMARY KEY AUTO_INCREMENT, agent_id INT, gateway_amount DECIMAL(15,2) DEFAULT 0, riyal_mobile_amount DECIMAL(15,2) DEFAULT 0, collection_date DATE NOT NULL, FOREIGN KEY (agent_id) REFERENCES agents(agent_id));\n";
    
    $collections = $pdo->query("SELECT * FROM collections")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($collections as $collection) {
        echo "INSERT INTO collections VALUES ({$collection['id']}, {$collection['agent_id']}, {$collection['gateway_amount']}, {$collection['riyal_mobile_amount']}, '{$collection['collection_date']}');\n";
    }
    
    exit;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>النسخ الاحتياطي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">النسخ الاحتياطي واستعادة البيانات</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">إنشاء نسخة احتياطية</div>
                <div class="card-body">
                    <p>قم بإنشاء نسخة احتياطية من جميع البيانات</p>
                    <form method="POST">
                        <input type="hidden" name="action" value="backup">
                        <button type="submit" class="btn btn-primary">تحميل النسخة الاحتياطية</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">معلومات النظام</div>
                <div class="card-body">
                    <?php
                    $agents_count = $pdo->query("SELECT COUNT(*) FROM agents")->fetchColumn();
                    $collections_count = $pdo->query("SELECT COUNT(*) FROM collections")->fetchColumn();
                    ?>
                    <p>عدد الوكلاء: <?php echo $agents_count; ?></p>
                    <p>عدد التحصيلات: <?php echo $collections_count; ?></p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
