@echo off
title نظام إدارة تحصيلات الوكلاء

echo ========================================
echo    نظام إدارة تحصيلات الوكلاء
echo ========================================
echo.
echo اختر نوع التشغيل:
echo 1. تشغيل محلي (localhost:7445)
echo 2. تشغيل على الشبكة (***********:7445)
echo 3. خادم PHP المدمج (محلي سريع)
echo 4. خروج
echo.

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto :local
if "%choice%"=="2" goto :network
if "%choice%"=="3" goto :php_server
if "%choice%"=="4" goto :exit

:local
echo بدء التشغيل المحلي...
call run_local.bat
goto :end

:network
echo بدء التشغيل على الشبكة...
call run_xampp_system.bat
goto :end

:php_server
echo بدء خادم PHP المدمج...
call local_server.bat
goto :end

:exit
exit

:end
echo تم بدء النظام بنجاح!
pause

