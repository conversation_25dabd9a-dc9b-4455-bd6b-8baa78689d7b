<?php
require_once 'config/database.php';

// جلب قائمة الوكلاء
$agents_query = $pdo->query("SELECT * FROM agents ORDER BY agent_name");
$agents = $agents_query->fetchAll(PDO::FETCH_ASSOC);

// إضافة في بداية الملف بعد جلب الوكلاء
$selected_agent = $_GET['agent_id'] ?? null;
$selected_date = $_GET['date'] ?? $_POST['collection_date'] ?? date('Y-m-d');

// جلب البيانات الموجودة للتاريخ المحدد
$existing_data = [];
if ($selected_date) {
    $existing_query = $pdo->prepare("
        SELECT agent_id, gateway_amount, riyal_mobile_amount
        FROM collections
        WHERE collection_date = ?
    ");
    $existing_query->execute([$selected_date]);
    $existing_results = $existing_query->fetchAll(PDO::FETCH_ASSOC);

    foreach ($existing_results as $row) {
        $existing_data[$row['agent_id']] = [
            'gateway' => $row['gateway_amount'],
            'riyal' => $row['riyal_mobile_amount']
        ];
    }
}

// معالجة حذف البيانات المحددة
if ($_POST && isset($_POST['delete_selected'])) {
    $selected_agents = $_POST['selected_agents'] ?? [];
    $date = $_POST['collection_date'];
    $deleted_count = 0;

    if (!empty($selected_agents)) {
        foreach ($selected_agents as $agent_id) {
            $delete_stmt = $pdo->prepare("DELETE FROM collections WHERE agent_id = ? AND collection_date = ?");
            $delete_stmt->execute([$agent_id, $date]);
            if ($delete_stmt->rowCount() > 0) {
                $deleted_count++;
            }
        }

        echo "<div class='alert alert-warning'>تم حذف $deleted_count سجل بنجاح</div>";

        // إعادة تحميل البيانات بعد الحذف
        $existing_query->execute([$date]);
        $existing_results = $existing_query->fetchAll(PDO::FETCH_ASSOC);
        $existing_data = [];
        foreach ($existing_results as $row) {
            $existing_data[$row['agent_id']] = [
                'gateway' => $row['gateway_amount'],
                'riyal' => $row['riyal_mobile_amount']
            ];
        }
    } else {
        echo "<div class='alert alert-danger'>لم يتم تحديد أي وكيل للحذف</div>";
    }
}

// معالجة إضافة/تحديث البيانات
if ($_POST && !isset($_POST['delete_selected'])) {
    $date = $_POST['collection_date'];
    $updated_count = 0;
    $inserted_count = 0;

    foreach ($_POST['agents'] as $agent_id => $data) {
        $gateway_amount = $data['gateway'] ?: 0;
        $riyal_amount = $data['riyal'] ?: 0;

        // التحقق من وجود بيانات للوكيل في نفس التاريخ
        $check_stmt = $pdo->prepare("SELECT id FROM collections WHERE agent_id = ? AND collection_date = ?");
        $check_stmt->execute([$agent_id, $date]);
        $existing_id = $check_stmt->fetchColumn();

        if ($existing_id) {
            // تحديث البيانات الموجودة
            if ($gateway_amount > 0 || $riyal_amount > 0) {
                $update_stmt = $pdo->prepare("UPDATE collections SET gateway_amount = ?, riyal_mobile_amount = ? WHERE id = ?");
                $update_stmt->execute([$gateway_amount, $riyal_amount, $existing_id]);
                $updated_count++;
            } else {
                // حذف السجل إذا كانت القيم صفر
                $delete_stmt = $pdo->prepare("DELETE FROM collections WHERE id = ?");
                $delete_stmt->execute([$existing_id]);
            }
        } else {
            // إدراج بيانات جديدة فقط إذا كانت القيم أكبر من صفر
            if ($gateway_amount > 0 || $riyal_amount > 0) {
                $insert_stmt = $pdo->prepare("INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) VALUES (?, ?, ?, ?)");
                $insert_stmt->execute([$agent_id, $gateway_amount, $riyal_amount, $date]);
                $inserted_count++;
            }
        }
    }

    $message = "";
    if ($inserted_count > 0) $message .= "تم إضافة $inserted_count سجل جديد. ";
    if ($updated_count > 0) $message .= "تم تحديث $updated_count سجل موجود. ";

    echo "<div class='alert alert-success'>$message</div>";

    // إعادة تحميل البيانات بعد الحفظ
    $existing_query->execute([$date]);
    $existing_results = $existing_query->fetchAll(PDO::FETCH_ASSOC);
    $existing_data = [];
    foreach ($existing_results as $row) {
        $existing_data[$row['agent_id']] = [
            'gateway' => $row['gateway_amount'],
            'riyal' => $row['riyal_mobile_amount']
        ];
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إضافة تحصيلات الوكلاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .existing-data {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .date-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .selection-controls {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #ffeaa7;
        }
        .selected-row {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }
    </style>
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">إضافة/تعديل تحصيلات الوكلاء</h2>

    <!-- معلومات التاريخ -->
    <div class="date-info">
        <div class="row align-items-center">
            <div class="col-md-6">
                <strong>التاريخ المحدد:</strong> <?= date('Y-m-d', strtotime($selected_date)) ?>
                <?php if (count($existing_data) > 0): ?>
                    <span class="badge bg-success ms-2"><?= count($existing_data) ?> سجل موجود</span>
                <?php else: ?>
                    <span class="badge bg-info ms-2">لا توجد بيانات</span>
                <?php endif; ?>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-sm btn-outline-primary" onclick="changeDate()">تغيير التاريخ</button>
            </div>
        </div>
    </div>

    <!-- أدوات التحديد والحذف -->
    <?php if (count($existing_data) > 0): ?>
    <div class="selection-controls">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" id="select_all" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="select_all">تحديد الكل</label>
                </div>
                <span id="selected_count" class="badge bg-warning ms-2">0 محدد</span>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteSelected()" id="delete_btn" disabled>
                    حذف المحدد
                </button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                    إلغاء التحديد
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <form method="POST" id="collectionsForm">
        <div class="row mb-3">
            <div class="col-md-4">
                <label class="form-label">التاريخ:</label>
                <input type="date" name="collection_date" class="form-control" required
                       value="<?= $selected_date ?>" onchange="loadDataForDate(this.value)">
            </div>
        </div>

        <table class="table table-bordered">
            <thead class="table-dark">
                <tr>
                    <?php if (count($existing_data) > 0): ?>
                    <th width="50">
                        <input type="checkbox" id="header_select_all" onchange="toggleSelectAll()">
                    </th>
                    <?php endif; ?>
                    <th>الوكيل</th>
                    <th>البوابة</th>
                    <th>الريال موبايل</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($agents as $agent): ?>
                <?php
                $gateway_value = $existing_data[$agent['agent_id']]['gateway'] ?? '';
                $riyal_value = $existing_data[$agent['agent_id']]['riyal'] ?? '';
                $has_data = isset($existing_data[$agent['agent_id']]);
                ?>
                <tr id="row_<?= $agent['agent_id'] ?>" <?= $has_data ? 'class="existing-data"' : '' ?>>
                    <?php if (count($existing_data) > 0): ?>
                    <td>
                        <?php if ($has_data): ?>
                        <input type="checkbox" class="agent-checkbox" value="<?= $agent['agent_id'] ?>"
                               onchange="updateSelection()">
                        <?php endif; ?>
                    </td>
                    <?php endif; ?>
                    <td>
                        <?= $agent['agent_name'] ?>
                        <?php if ($has_data): ?>
                            <span class="badge bg-success ms-1">محفوظ</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <input type="number" step="0.01"
                               name="agents[<?= $agent['agent_id'] ?>][gateway]"
                               class="form-control gateway-input data-input"
                               placeholder="0.00"
                               value="<?= $gateway_value ?>"
                               onchange="calculateTotal(<?= $agent['agent_id'] ?>)"
                               onkeydown="handleEnterKey(event, this)"
                               data-agent-id="<?= $agent['agent_id'] ?>"
                               data-field-type="gateway"
                               <?= $selected_agent == $agent['agent_id'] ? 'autofocus' : '' ?>>
                    </td>
                    <td>
                        <input type="number" step="0.01"
                               name="agents[<?= $agent['agent_id'] ?>][riyal]"
                               class="form-control riyal-input data-input"
                               placeholder="0.00"
                               value="<?= $riyal_value ?>"
                               onchange="calculateTotal(<?= $agent['agent_id'] ?>)"
                               onkeydown="handleEnterKey(event, this)"
                               data-agent-id="<?= $agent['agent_id'] ?>"
                               data-field-type="riyal">
                    </td>
                    <td>
                        <span class="badge bg-secondary total-amount" id="total_<?= $agent['agent_id'] ?>">
                            <?= $has_data ? number_format(($gateway_value + $riyal_value), 2) : '0.00' ?>
                        </span>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot class="table-light">
                <tr>
                    <?php if (count($existing_data) > 0): ?>
                    <th></th>
                    <?php endif; ?>
                    <th>الإجمالي العام</th>
                    <th><span id="total_gateway" class="badge bg-primary">0.00</span></th>
                    <th><span id="total_riyal" class="badge bg-info">0.00</span></th>
                    <th><span id="grand_total" class="badge bg-success">0.00</span></th>
                </tr>
            </tfoot>
        </table>

        <div class="d-flex justify-content-between">
            <div>
                <button type="submit" class="btn btn-primary">حفظ البيانات</button>
                <button type="button" class="btn btn-warning" onclick="clearAll()">مسح الكل</button>
            </div>
            <div>
                <a href="import_data.php" class="btn btn-info">استيراد نصي</a>
                <a href="import_excel.php" class="btn btn-success">استيراد Excel</a>
                <a href="quick_import.php" class="btn btn-outline-info">استيراد سريع</a>
                <a href="reports.php" class="btn btn-secondary">التقارير</a>
            </div>
        </div>
    </form>

    <!-- نموذج حذف مخفي -->
    <form method="POST" id="deleteForm" style="display: none;">
        <input type="hidden" name="collection_date" value="<?= $selected_date ?>">
        <input type="hidden" name="delete_selected" value="1">
        <div id="selected_agents_inputs"></div>
    </form>
</div>

<script>
// تحميل البيانات عند تغيير التاريخ
function loadDataForDate(date) {
    if (date) {
        window.location.href = `add_collection.php?date=${date}`;
    }
}

// تغيير التاريخ
function changeDate() {
    const newDate = prompt('أدخل التاريخ الجديد (YYYY-MM-DD):', '<?= $selected_date ?>');
    if (newDate) {
        loadDataForDate(newDate);
    }
}

// تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('select_all') || document.getElementById('header_select_all');
    const checkboxes = document.querySelectorAll('.agent-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        toggleRowHighlight(checkbox);
    });

    updateSelection();
}

// تحديث حالة التحديد
function updateSelection() {
    const checkboxes = document.querySelectorAll('.agent-checkbox');
    const selectedCheckboxes = document.querySelectorAll('.agent-checkbox:checked');
    const selectAllCheckbox = document.getElementById('select_all');
    const headerSelectAllCheckbox = document.getElementById('header_select_all');
    const selectedCount = document.getElementById('selected_count');
    const deleteBtn = document.getElementById('delete_btn');

    // تحديث عداد المحدد
    selectedCount.textContent = `${selectedCheckboxes.length} محدد`;

    // تحديث حالة زر الحذف
    deleteBtn.disabled = selectedCheckboxes.length === 0;

    // تحديث حالة تحديد الكل
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = selectedCheckboxes.length === checkboxes.length && checkboxes.length > 0;
    }
    if (headerSelectAllCheckbox) {
        headerSelectAllCheckbox.checked = selectedCheckboxes.length === checkboxes.length && checkboxes.length > 0;
    }

    // تحديث تمييز الصفوف
    checkboxes.forEach(checkbox => {
        toggleRowHighlight(checkbox);
    });
}

// تمييز الصف المحدد
function toggleRowHighlight(checkbox) {
    const row = checkbox.closest('tr');
    if (checkbox.checked) {
        row.classList.add('selected-row');
    } else {
        row.classList.remove('selected-row');
    }
}

// إلغاء التحديد
function clearSelection() {
    const checkboxes = document.querySelectorAll('.agent-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        toggleRowHighlight(checkbox);
    });
    updateSelection();
}

// حذف المحدد
function deleteSelected() {
    const selectedCheckboxes = document.querySelectorAll('.agent-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        alert('يرجى تحديد وكيل واحد على الأقل للحذف');
        return;
    }

    const agentNames = [];
    selectedCheckboxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const agentName = row.querySelector('td:nth-child(2)').textContent.trim().replace('محفوظ', '').trim();
        agentNames.push(agentName);
    });

    const confirmMessage = `هل أنت متأكد من حذف بيانات الوكلاء التالية؟\n\n${agentNames.join('\n')}\n\nلتاريخ: <?= $selected_date ?>`;

    if (confirm(confirmMessage)) {
        // إضافة الوكلاء المحددين للنموذج المخفي
        const selectedAgentsInputs = document.getElementById('selected_agents_inputs');
        selectedAgentsInputs.innerHTML = '';

        selectedCheckboxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_agents[]';
            input.value = checkbox.value;
            selectedAgentsInputs.appendChild(input);
        });

        // إرسال النموذج
        document.getElementById('deleteForm').submit();
    }
}

// حساب الإجمالي لكل وكيل
function calculateTotal(agentId) {
    const gatewayInput = document.querySelector(`input[name="agents[${agentId}][gateway]"]`);
    const riyalInput = document.querySelector(`input[name="agents[${agentId}][riyal]"]`);
    const totalSpan = document.getElementById(`total_${agentId}`);

    const gateway = parseFloat(gatewayInput.value) || 0;
    const riyal = parseFloat(riyalInput.value) || 0;
    const total = gateway + riyal;

    totalSpan.textContent = total.toFixed(2);

    // تحديث الإجماليات العامة
    updateGrandTotals();
}

// تحديث الإجماليات العامة
function updateGrandTotals() {
    let totalGateway = 0;
    let totalRiyal = 0;

    document.querySelectorAll('.gateway-input').forEach(input => {
        totalGateway += parseFloat(input.value) || 0;
    });

    document.querySelectorAll('.riyal-input').forEach(input => {
        totalRiyal += parseFloat(input.value) || 0;
    });

    document.getElementById('total_gateway').textContent = totalGateway.toFixed(2);
    document.getElementById('total_riyal').textContent = totalRiyal.toFixed(2);
    document.getElementById('grand_total').textContent = (totalGateway + totalRiyal).toFixed(2);
}

// مسح جميع الحقول
function clearAll() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.value = '';
        });
        updateGrandTotals();
    }
}

// التعامل مع مفتاح Enter للانتقال للسجل التالي
function handleEnterKey(event, currentInput) {
    if (event.key === 'Enter') {
        event.preventDefault(); // منع إرسال النموذج

        const currentAgentId = currentInput.getAttribute('data-agent-id');
        const currentFieldType = currentInput.getAttribute('data-field-type');

        // إذا كان في حقل البوابة، انتقل لحقل ريال موبايل لنفس الوكيل
        if (currentFieldType === 'gateway') {
            const riyalInput = document.querySelector(`input[data-agent-id="${currentAgentId}"][data-field-type="riyal"]`);
            if (riyalInput) {
                riyalInput.focus();
                riyalInput.select();
                return;
            }
        }

        // إذا كان في حقل ريال موبايل أو لم يجد حقل ريال موبايل، انتقل للوكيل التالي
        const allInputs = Array.from(document.querySelectorAll('.data-input'));
        const currentIndex = allInputs.indexOf(currentInput);

        // البحث عن أول حقل بوابة للوكيل التالي
        for (let i = currentIndex + 1; i < allInputs.length; i++) {
            const nextInput = allInputs[i];
            if (nextInput.getAttribute('data-field-type') === 'gateway') {
                nextInput.focus();
                nextInput.select();
                return;
            }
        }

        // إذا وصل للنهاية، ارجع للبداية
        const firstInput = document.querySelector('.data-input[data-field-type="gateway"]');
        if (firstInput) {
            firstInput.focus();
            firstInput.select();
        }
    }
}

// منع إرسال النموذج عند الضغط على Enter
function preventFormSubmitOnEnter(event) {
    if (event.key === 'Enter' && event.target.classList.contains('data-input')) {
        event.preventDefault();
        return false;
    }
}

// تحديث الإجماليات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrandTotals();

    // إضافة مستمع للأحداث لجميع الحقول
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            const agentId = this.name.match(/\[(\d+)\]/)[1];
            calculateTotal(agentId);
        });
    });

    // إضافة مستمع للأحداث لصناديق التحديد
    document.querySelectorAll('.agent-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelection);
    });

    // منع إرسال النموذج عند الضغط على Enter في حقول البيانات
    document.addEventListener('keydown', preventFormSubmitOnEnter);
});
</script>
</body>
</html>


