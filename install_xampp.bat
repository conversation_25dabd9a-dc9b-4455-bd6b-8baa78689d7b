@echo off
echo تثبيت XAMPP لنظام التحصيلات...

REM التحقق من وجود XAMPP
if exist "C:\xampp\xampp-control.exe" (
    echo XAMPP موجود بالفعل
    goto :start_services
)

echo يرجى تحميل وتثبيت XAMPP من:
echo https://www.apachefriends.org/download.html
echo.
echo بعد التثبيت، قم بتشغيل هذا السكريبت مرة أخرى
pause
exit /b 1

:start_services
echo بدء خدمات XAMPP...

REM بدء Apache
"C:\xampp\apache\bin\httpd.exe" -k start
echo ✓ تم بدء Apache

REM بدء MySQL
"C:\xampp\mysql\bin\mysqld.exe" --defaults-file="C:\xampp\mysql\bin\my.ini" --standalone --console
echo ✓ تم بدء MySQL

echo.
echo XAMPP يعمل الآن
echo لوحة التحكم: C:\xampp\xampp-control.exe
pause
