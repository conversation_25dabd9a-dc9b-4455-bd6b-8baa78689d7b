<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>نظام إدارة التحصيلات - الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-card { transition: all 0.3s; cursor: pointer; }
        .dashboard-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .stats-card { background: linear-gradient(45deg, #007bff, #0056b3); }
        .quick-action { margin-bottom: 10px; }
    </style>
</head>
<body>

<?php
require_once 'config/database.php';

try {
    // إحصائيات عامة
    $total_agents = $pdo->query("SELECT COUNT(*) FROM agents")->fetchColumn();
    $total_users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $total_days = $pdo->query("SELECT COUNT(*) FROM days")->fetchColumn();
    
    // إحصائيات التحصيلات
    $gateway_stats = $pdo->query("
        SELECT 
            COUNT(*) as records,
            COALESCE(SUM(amount), 0) as total,
            COUNT(DISTINCT agent_id) as agents
        FROM gateway
    ")->fetch();
    
    $riyal_stats = $pdo->query("
        SELECT 
            COUNT(*) as records,
            COALESCE(SUM(amount), 0) as total,
            COUNT(DISTINCT agent_id) as agents
        FROM riyal_mobile
    ")->fetch();
    
    // إحصائيات اليوم الحالي
    $today = date('Y-m-d');
    $today_gateway = $pdo->prepare("
        SELECT COALESCE(SUM(g.amount), 0) as total, COUNT(*) as records
        FROM gateway g
        JOIN days d ON g.day_id = d.day_id
        WHERE d.date = ?
    ");
    $today_gateway->execute([$today]);
    $today_gateway_data = $today_gateway->fetch();
    
    $today_riyal = $pdo->prepare("
        SELECT COALESCE(SUM(r.amount), 0) as total, COUNT(*) as records
        FROM riyal_mobile r
        JOIN days d ON r.day_id = d.day_id
        WHERE d.date = ?
    ");
    $today_riyal->execute([$today]);
    $today_riyal_data = $today_riyal->fetch();
    
    // آخر التحديثات
    $recent_updates = $pdo->query("
        SELECT 'gateway' as type, a.agent_name, g.amount, g.updated_at
        FROM gateway g
        JOIN agents a ON g.agent_id = a.agent_id
        WHERE g.updated_at IS NOT NULL
        UNION ALL
        SELECT 'riyal_mobile' as type, a.agent_name, r.amount, r.updated_at
        FROM riyal_mobile r
        JOIN agents a ON r.agent_id = a.agent_id
        WHERE r.updated_at IS NOT NULL
        ORDER BY updated_at DESC
        LIMIT 10
    ")->fetchAll();
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>

<div class="container-fluid mt-4">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-primary text-center">
                <h1><i class="fas fa-tachometer-alt"></i> نظام إدارة التحصيلات</h1>
                <p class="lead">لوحة التحكم الرئيسية - النظام الجديد</p>
                <small>آخر تحديث: <?= date('Y-m-d H:i:s') ?></small>
            </div>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <h5>خطأ في الاتصال بقاعدة البيانات</h5>
            <p><?= htmlspecialchars($error_message) ?></p>
            <a href="/create_new_database_structure.php" class="btn btn-warning">إنشاء قاعدة البيانات</a>
        </div>
    <?php else: ?>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-white">
                <div class="card-body text-center">
                    <i class="fas fa-building fa-2x mb-2"></i>
                    <h3><?= number_format($total_agents) ?></h3>
                    <p>إجمالي الوكلاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-door-open fa-2x mb-2"></i>
                    <h3><?= number_format($gateway_stats['total'], 0) ?></h3>
                    <p>إجمالي البوابة</p>
                    <small><?= $gateway_stats['records'] ?> سجل</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                    <h3><?= number_format($riyal_stats['total'], 0) ?></h3>
                    <p>إجمالي ريال موبايل</p>
                    <small><?= $riyal_stats['records'] ?> سجل</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h3><?= number_format($gateway_stats['total'] + $riyal_stats['total'], 0) ?></h3>
                    <p>الإجمالي العام</p>
                    <small><?= $gateway_stats['records'] + $riyal_stats['records'] ?> سجل</small>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات اليوم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-calendar-day"></i> إحصائيات اليوم (<?= $today ?>)</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning"><?= number_format($today_gateway_data['total'], 0) ?></h4>
                                <p>البوابة اليوم</p>
                                <small><?= $today_gateway_data['records'] ?> سجل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger"><?= number_format($today_riyal_data['total'], 0) ?></h4>
                                <p>ريال موبايل اليوم</p>
                                <small><?= $today_riyal_data['records'] ?> سجل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success"><?= number_format($today_gateway_data['total'] + $today_riyal_data['total'], 0) ?></h4>
                                <p>إجمالي اليوم</p>
                                <small><?= $today_gateway_data['records'] + $today_riyal_data['records'] ?> سجل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <a href="/daily_report_new.php?date=<?= $today ?>" class="btn btn-primary">
                                    <i class="fas fa-chart-bar"></i> تقرير اليوم
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الإجراءات السريعة -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-bolt"></i> الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/add_collection_fixed.php" class="btn btn-success quick-action">
                            <i class="fas fa-plus-circle"></i> إضافة تحصيلات جديدة
                        </a>
                        
                        <a href="/add_gateway_data.php" class="btn btn-warning quick-action">
                            <i class="fas fa-door-open"></i> إضافة بيانات البوابة
                        </a>
                        
                        <a href="/add_riyal_data.php" class="btn btn-danger quick-action">
                            <i class="fas fa-mobile-alt"></i> إضافة بيانات ريال موبايل
                        </a>
                        
                        <a href="/daily_report_new.php" class="btn btn-info quick-action">
                            <i class="fas fa-chart-bar"></i> التقرير اليومي
                        </a>
                        
                        <a href="/manage_agents_fixed.php" class="btn btn-secondary quick-action">
                            <i class="fas fa-users"></i> إدارة الوكلاء
                        </a>
                        
                        <a href="/manage_users.php" class="btn btn-dark quick-action">
                            <i class="fas fa-users-cog"></i> إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- آخر التحديثات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-clock"></i> آخر التحديثات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_updates)): ?>
                        <div class="alert alert-info">
                            <small>لا توجد تحديثات حديثة</small>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($recent_updates, 0, 8) as $update): ?>
                                <div class="list-group-item p-2">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong><?= htmlspecialchars($update['agent_name']) ?></strong>
                                            <br>
                                            <span class="badge bg-<?= $update['type'] == 'gateway' ? 'warning' : 'danger' ?>">
                                                <?= $update['type'] == 'gateway' ? 'بوابة' : 'ريال موبايل' ?>
                                            </span>
                                            <span class="text-success"><?= number_format($update['amount'], 0) ?></span>
                                        </div>
                                        <small class="text-muted">
                                            <?= date('H:i', strtotime($update['updated_at'])) ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- أدوات النظام -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-tools"></i> أدوات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/database_status.php" class="btn btn-info quick-action">
                            <i class="fas fa-database"></i> حالة قاعدة البيانات
                        </a>
                        
                        <a href="/view_new_structure.php" class="btn btn-secondary quick-action">
                            <i class="fas fa-sitemap"></i> هيكل قاعدة البيانات
                        </a>
                        
                        <a href="/create_new_database_structure.php" class="btn btn-warning quick-action">
                            <i class="fas fa-wrench"></i> إعداد قاعدة البيانات
                        </a>
                        
                        <a href="/test_server.php" class="btn btn-success quick-action">
                            <i class="fas fa-server"></i> اختبار الخادم
                        </a>
                        
                        <button onclick="location.reload()" class="btn btn-primary quick-action">
                            <i class="fas fa-sync-alt"></i> تحديث الصفحة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="card mt-3">
                <div class="card-header bg-secondary text-white">
                    <h6><i class="fas fa-info-circle"></i> معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li><strong>الإصدار:</strong> 2.0 - MySQL الجديد</li>
                        <li><strong>الخادم:</strong> Node.js + PHP</li>
                        <li><strong>المنفذ:</strong> 7445</li>
                        <li><strong>قاعدة البيانات:</strong> MySQL</li>
                        <li><strong>إجمالي الأيام:</strong> <?= $total_days ?></li>
                        <li><strong>إجمالي المستخدمين:</strong> <?= $total_users ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>
</div>

<script>
// تحديث تلقائي للإحصائيات كل 30 ثانية
setInterval(() => {
    fetch('/api/status')
    .then(response => response.json())
    .then(data => {
        console.log('تم تحديث حالة النظام:', data);
    })
    .catch(error => {
        console.log('خطأ في تحديث الحالة:', error);
    });
}, 30000);

// إضافة تأثيرات للبطاقات
document.querySelectorAll('.dashboard-card').forEach(card => {
    card.addEventListener('click', function() {
        const link = this.querySelector('a');
        if (link) {
            window.location.href = link.href;
        }
    });
});
</script>

</body>
</html>
