<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إعداد PostgreSQL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-elephant"></i> إعداد نظام التحصيلات مع PostgreSQL</h2>
        <p>تكوين النظام للعمل مع قاعدة بيانات PostgreSQL الموجودة</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>📋 معلومات قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr><td><strong>نوع قاعدة البيانات:</strong></td><td>PostgreSQL</td></tr>
                        <tr><td><strong>المضيف:</strong></td><td>localhost</td></tr>
                        <tr><td><strong>المنفذ:</strong></td><td>5432</td></tr>
                        <tr><td><strong>اسم قاعدة البيانات:</strong></td><td>agent</td></tr>
                        <tr><td><strong>اسم المستخدم:</strong></td><td>postgres</td></tr>
                        <tr><td><strong>كلمة المرور:</strong></td><td>yemen123</td></tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>🔧 حالة الإعداد</h5>
                </div>
                <div class="card-body">
                    <?php
                    echo "<h6>1. فحص امتداد PostgreSQL:</h6>";
                    if (extension_loaded('pdo_pgsql')) {
                        echo "<p class='text-success'><i class='fas fa-check'></i> امتداد PDO PostgreSQL متاح</p>";
                        $pgsql_available = true;
                    } else {
                        echo "<p class='text-danger'><i class='fas fa-times'></i> امتداد PDO PostgreSQL غير متاح</p>";
                        echo "<div class='alert alert-warning'>";
                        echo "<small>يرجى تفعيل امتداد pdo_pgsql في PHP</small>";
                        echo "</div>";
                        $pgsql_available = false;
                    }
                    
                    if ($pgsql_available) {
                        echo "<h6>2. اختبار الاتصال:</h6>";
                        try {
                            require_once 'config/database_postgresql.php';
                            echo "<p class='text-success'><i class='fas fa-check'></i> تم الاتصال بنجاح</p>";
                            
                            // عرض معلومات PostgreSQL
                            $version = $pdo->query("SELECT version()")->fetch()['version'];
                            echo "<p><small><strong>إصدار PostgreSQL:</strong> " . substr($version, 0, 50) . "...</small></p>";
                            
                            $connection_success = true;
                        } catch (Exception $e) {
                            echo "<p class='text-danger'><i class='fas fa-times'></i> فشل الاتصال</p>";
                            echo "<div class='alert alert-danger'>";
                            echo "<small>" . $e->getMessage() . "</small>";
                            echo "</div>";
                            $connection_success = false;
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isset($connection_success) && $connection_success): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>🚀 إعداد النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>1. إنشاء الجداول:</h6>
                            <button onclick="createTables()" class="btn btn-primary">
                                <i class="fas fa-table"></i> إنشاء الجداول
                            </button>
                            <div id="tables-result" class="mt-2"></div>
                        </div>
                        
                        <div class="col-md-4">
                            <h6>2. استيراد البيانات:</h6>
                            <button onclick="importData()" class="btn btn-warning">
                                <i class="fas fa-upload"></i> استيراد من SQLite
                            </button>
                            <div id="import-result" class="mt-2"></div>
                        </div>
                        
                        <div class="col-md-4">
                            <h6>3. تحويل النظام:</h6>
                            <button onclick="switchSystem()" class="btn btn-success">
                                <i class="fas fa-exchange-alt"></i> تحويل النظام
                            </button>
                            <div id="switch-result" class="mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>📊 حالة الجداول</h5>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // فحص الجداول الموجودة
                        $tables = $pdo->query("
                            SELECT table_name 
                            FROM information_schema.tables 
                            WHERE table_schema = 'public' 
                            ORDER BY table_name
                        ")->fetchAll();
                        
                        if (empty($tables)) {
                            echo "<div class='alert alert-warning'>لا توجد جداول. يرجى إنشاء الجداول أولاً.</div>";
                        } else {
                            echo "<h6>الجداول الموجودة:</h6>";
                            echo "<div class='row'>";
                            foreach ($tables as $table) {
                                $table_name = $table['table_name'];
                                try {
                                    $count = $pdo->query("SELECT COUNT(*) as count FROM \"$table_name\"")->fetch()['count'];
                                    echo "<div class='col-md-4 mb-2'>";
                                    echo "<div class='card'>";
                                    echo "<div class='card-body text-center'>";
                                    echo "<h6><i class='fas fa-table'></i> $table_name</h6>";
                                    echo "<p class='text-muted'>$count سجل</p>";
                                    echo "</div>";
                                    echo "</div>";
                                    echo "</div>";
                                } catch (Exception $e) {
                                    echo "<div class='col-md-4 mb-2'>";
                                    echo "<div class='card'>";
                                    echo "<div class='card-body text-center'>";
                                    echo "<h6><i class='fas fa-table'></i> $table_name</h6>";
                                    echo "<p class='text-danger'>خطأ في القراءة</p>";
                                    echo "</div>";
                                    echo "</div>";
                                    echo "</div>";
                                }
                            }
                            echo "</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>خطأ في قراءة الجداول: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5>📚 معلومات PostgreSQL</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>مميزات PostgreSQL:</h6>
                            <ul>
                                <li>قاعدة بيانات قوية ومتقدمة</li>
                                <li>دعم ممتاز للبيانات الكبيرة</li>
                                <li>أداء عالي ومستقر</li>
                                <li>دعم JSON والبيانات المعقدة</li>
                                <li>أمان متقدم</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>أدوات إدارة PostgreSQL:</h6>
                            <ul>
                                <li><strong>pgAdmin:</strong> أداة إدارة رسمية</li>
                                <li><strong>DBeaver:</strong> أداة مجانية متعددة المنصات</li>
                                <li><strong>DataGrip:</strong> من JetBrains</li>
                                <li><strong>psql:</strong> سطر الأوامر</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <a href="main.php" class="btn btn-secondary">العودة للنظام الحالي</a>
        <a href="db_viewer_simple.php" class="btn btn-info">مستعرض قاعدة البيانات</a>
    </div>
</div>

<script>
function createTables() {
    document.getElementById('tables-result').innerHTML = '<div class="alert alert-info">🔄 جاري إنشاء الجداول...</div>';
    
    fetch('postgresql_operations.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=create_tables'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('tables-result').innerHTML = data;
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        document.getElementById('tables-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}

function importData() {
    document.getElementById('import-result').innerHTML = '<div class="alert alert-info">🔄 جاري استيراد البيانات...</div>';
    
    fetch('postgresql_operations.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=import_data'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('import-result').innerHTML = data;
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        document.getElementById('import-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}

function switchSystem() {
    document.getElementById('switch-result').innerHTML = '<div class="alert alert-info">🔄 جاري تحويل النظام...</div>';
    
    fetch('postgresql_operations.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=switch_system'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('switch-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('switch-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}
</script>

</body>
</html>
