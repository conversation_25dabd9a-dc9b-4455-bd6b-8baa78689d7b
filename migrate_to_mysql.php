<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تحويل النظام إلى MySQL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-info">
        <h2><i class="fas fa-database"></i> تحويل النظام إلى MySQL</h2>
        <p>سيتم تحويل النظام من SQLite إلى MySQL وإنشاء قاعدة البيانات الجديدة.</p>
    </div>

    <?php
    $step = $_GET['step'] ?? 1;
    
    if ($step == 1) {
        // الخطوة الأولى: التحقق من متطلبات MySQL
        echo "<div class='card'>";
        echo "<div class='card-header bg-primary text-white'>";
        echo "<h5>الخطوة 1: التحقق من متطلبات MySQL</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $requirements = [];
        
        // فحص امتداد MySQL
        if (extension_loaded('pdo_mysql')) {
            $requirements['pdo_mysql'] = ['status' => true, 'message' => 'امتداد PDO MySQL متاح'];
        } else {
            $requirements['pdo_mysql'] = ['status' => false, 'message' => 'امتداد PDO MySQL غير متاح'];
        }
        
        // فحص الاتصال بـ MySQL
        try {
            $test_pdo = new PDO("mysql:host=localhost;port=3306", 'root', '');
            $requirements['connection'] = ['status' => true, 'message' => 'الاتصال بـ MySQL ناجح'];
        } catch (Exception $e) {
            $requirements['connection'] = ['status' => false, 'message' => 'فشل الاتصال بـ MySQL: ' . $e->getMessage()];
        }
        
        // فحص XAMPP/WAMP
        $xampp_running = false;
        if (file_exists('C:/xampp/mysql/bin/mysqld.exe') || file_exists('/opt/lampp/bin/mysql')) {
            $xampp_running = true;
            $requirements['xampp'] = ['status' => true, 'message' => 'XAMPP/LAMPP مكتشف'];
        } else {
            $requirements['xampp'] = ['status' => false, 'message' => 'XAMPP/LAMPP غير مكتشف'];
        }
        
        echo "<h6>نتائج الفحص:</h6>";
        echo "<ul class='list-group'>";
        foreach ($requirements as $req) {
            $icon = $req['status'] ? 'fa-check text-success' : 'fa-times text-danger';
            $class = $req['status'] ? 'list-group-item-success' : 'list-group-item-danger';
            echo "<li class='list-group-item $class'>";
            echo "<i class='fas $icon'></i> {$req['message']}";
            echo "</li>";
        }
        echo "</ul>";
        
        $all_ok = array_reduce($requirements, function($carry, $req) {
            return $carry && $req['status'];
        }, true);
        
        if ($all_ok) {
            echo "<div class='alert alert-success mt-3'>";
            echo "<h6>✅ جميع المتطلبات متوفرة!</h6>";
            echo "<p>يمكن المتابعة لإنشاء قاعدة بيانات MySQL.</p>";
            echo "</div>";
            echo "<a href='?step=2' class='btn btn-primary'>المتابعة للخطوة التالية</a>";
        } else {
            echo "<div class='alert alert-danger mt-3'>";
            echo "<h6>❌ بعض المتطلبات غير متوفرة</h6>";
            echo "<p>يرجى التأكد من تشغيل XAMPP أو WAMP وتفعيل MySQL.</p>";
            echo "</div>";
            echo "<button onclick='location.reload()' class='btn btn-warning'>إعادة الفحص</button>";
        }
        
        echo "</div>";
        echo "</div>";
        
    } elseif ($step == 2) {
        // الخطوة الثانية: إنشاء قاعدة البيانات
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h5>الخطوة 2: إنشاء قاعدة بيانات MySQL</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        try {
            require_once 'config/database_mysql.php';
            
            echo "<div class='alert alert-success'>";
            echo "<h6>✅ تم إنشاء قاعدة البيانات بنجاح!</h6>";
            echo "<ul>";
            echo "<li>اسم قاعدة البيانات: <strong>collections_system</strong></li>";
            echo "<li>الخادم: <strong>localhost</strong></li>";
            echo "<li>المنفذ: <strong>3306</strong></li>";
            echo "</ul>";
            echo "</div>";
            
            // فحص الجداول
            $tables = $pdo->query("SHOW TABLES")->fetchAll();
            echo "<h6>الجداول المنشأة:</h6>";
            echo "<ul class='list-group'>";
            foreach ($tables as $table) {
                $table_name = array_values($table)[0];
                $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
                echo "<li class='list-group-item d-flex justify-content-between'>";
                echo "<span><i class='fas fa-table'></i> $table_name</span>";
                echo "<span class='badge bg-primary'>$count سجل</span>";
                echo "</li>";
            }
            echo "</ul>";
            
            echo "<div class='mt-3'>";
            echo "<a href='?step=3' class='btn btn-primary'>المتابعة لنقل البيانات</a>";
            echo "<a href='https://localhost/phpmyadmin/' target='_blank' class='btn btn-info'>فتح phpMyAdmin</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h6>❌ خطأ في إنشاء قاعدة البيانات</h6>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
            echo "<a href='?step=1' class='btn btn-secondary'>العودة للخطوة السابقة</a>";
        }
        
        echo "</div>";
        echo "</div>";
        
    } elseif ($step == 3) {
        // الخطوة الثالثة: نقل البيانات من SQLite
        echo "<div class='card'>";
        echo "<div class='card-header bg-warning text-dark'>";
        echo "<h5>الخطوة 3: نقل البيانات من SQLite إلى MySQL</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        try {
            // الاتصال بـ SQLite
            $sqlite_file = __DIR__ . '/database/collections_system.db';
            if (file_exists($sqlite_file)) {
                $sqlite_pdo = new PDO("sqlite:$sqlite_file");
                $sqlite_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // الاتصال بـ MySQL
                require_once 'config/database_mysql.php';
                
                echo "<h6>نقل البيانات:</h6>";
                
                // نقل بيانات التحصيلات
                $collections = $sqlite_pdo->query("SELECT * FROM collections")->fetchAll();
                if (!empty($collections)) {
                    $pdo->exec("DELETE FROM collections"); // مسح البيانات الموجودة
                    
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) 
                        VALUES (?, ?, ?, ?)
                    ");
                    
                    $transferred = 0;
                    foreach ($collections as $collection) {
                        $insert_stmt->execute([
                            $collection['agent_id'],
                            $collection['gateway_amount'],
                            $collection['riyal_mobile_amount'],
                            $collection['collection_date']
                        ]);
                        $transferred++;
                    }
                    
                    echo "<div class='alert alert-success'>";
                    echo "<i class='fas fa-check'></i> تم نقل $transferred سجل تحصيل";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-info'>";
                    echo "<i class='fas fa-info'></i> لا توجد بيانات تحصيلات في SQLite";
                    echo "</div>";
                }
                
                echo "<div class='alert alert-success'>";
                echo "<h6>🎉 تم نقل البيانات بنجاح!</h6>";
                echo "<p>يمكنك الآن استخدام النظام مع قاعدة بيانات MySQL.</p>";
                echo "</div>";
                
                echo "<a href='?step=4' class='btn btn-primary'>المتابعة لتحديث النظام</a>";
                
            } else {
                echo "<div class='alert alert-warning'>";
                echo "<h6>⚠️ ملف SQLite غير موجود</h6>";
                echo "<p>سيتم استخدام قاعدة بيانات MySQL فارغة.</p>";
                echo "</div>";
                echo "<a href='?step=4' class='btn btn-primary'>المتابعة</a>";
            }
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h6>❌ خطأ في نقل البيانات</h6>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        
    } elseif ($step == 4) {
        // الخطوة الرابعة: تحديث ملفات النظام
        echo "<div class='card'>";
        echo "<div class='card-header bg-dark text-white'>";
        echo "<h5>الخطوة 4: تحديث النظام لاستخدام MySQL</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        echo "<div class='alert alert-info'>";
        echo "<h6>📝 الخطوات المطلوبة:</h6>";
        echo "<ol>";
        echo "<li>تحديث ملف الاتصال بقاعدة البيانات</li>";
        echo "<li>تحديث ملفات النظام لاستخدام MySQL</li>";
        echo "<li>اختبار النظام</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<button onclick='updateSystem()' class='btn btn-success btn-lg'>تحديث النظام</button>";
        echo "<div id='update-result' class='mt-3'></div>";
        
        echo "</div>";
        echo "</div>";
    }
    ?>
    
    <div class="mt-4">
        <a href="main.php" class="btn btn-secondary">العودة للصفحة الرئيسية</a>
    </div>
</div>

<script>
function updateSystem() {
    document.getElementById('update-result').innerHTML = '<div class="alert alert-info">🔄 جاري تحديث النظام...</div>';
    
    fetch('update_to_mysql.php')
    .then(response => response.text())
    .then(data => {
        document.getElementById('update-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('update-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}
</script>

</body>
</html>
