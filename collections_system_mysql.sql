-- نسخة احتياطية من قاعدة بيانات نظام التحصيلات
-- تاريخ الإنشاء: 2025-07-15
-- نوع قاعدة البيانات: MySQL

-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `collections_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `collections_system`;

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` INT AUTO_INCREMENT PRIMARY KEY,
    `employee_name` VARCHAR(255) NOT NULL,
    `username` VARCHAR(100) UNIQUE NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `permissions` ENUM('admin', 'user') DEFAULT 'user',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `last_login` TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الوكلاء
CREATE TABLE IF NOT EXISTS `agents` (
    `agent_id` INT AUTO_INCREMENT PRIMARY KEY,
    `agent_name` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول التحصيلات
CREATE TABLE IF NOT EXISTS `collections` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `agent_id` INT,
    `gateway_amount` DECIMAL(15,2) DEFAULT 0.00,
    `riyal_mobile_amount` DECIMAL(15,2) DEFAULT 0.00,
    `collection_date` DATE NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_agent_date` (`agent_id`, `collection_date`),
    INDEX `idx_collection_date` (`collection_date`),
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`agent_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المستخدمين الافتراضيين
INSERT INTO `users` (`employee_name`, `username`, `password`, `permissions`) VALUES
('المدير العام', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('مستخدم تجريبي', 'user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user');

-- إدراج الوكلاء الافتراضيين
INSERT INTO `agents` (`agent_name`) VALUES
('المامون'),
('الشرعبي'),
('عومان'),
('الكون'),
('سكافا'),
('أبو اسامه'),
('أسس'),
('الاثير'),
('المترب'),
('باتكو'),
('بران');

-- إدراج بيانات تجريبية للتحصيلات (اختيارية)
INSERT INTO `collections` (`agent_id`, `gateway_amount`, `riyal_mobile_amount`, `collection_date`) VALUES
(1, 25000.50, 15000.25, '2025-07-15'),
(2, 22000.75, 18000.50, '2025-07-15'),
(3, 18000.25, 12000.75, '2025-07-15'),
(4, 30000.00, 20000.00, '2025-07-15'),
(5, 16000.50, 14000.25, '2025-07-15'),
(6, 28000.75, 22000.50, '2025-07-15'),
(7, 19000.25, 16000.75, '2025-07-15'),
(8, 24000.50, 19000.25, '2025-07-15'),
(9, 21000.75, 17000.50, '2025-07-15'),
(10, 26000.25, 21000.75, '2025-07-15'),
(11, 23000.50, 18500.25, '2025-07-15');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX `idx_users_username` ON `users` (`username`);
CREATE INDEX `idx_agents_name` ON `agents` (`agent_name`);
CREATE INDEX `idx_collections_amounts` ON `collections` (`gateway_amount`, `riyal_mobile_amount`);

-- إنشاء views مفيدة
CREATE VIEW `daily_summary` AS
SELECT 
    `collection_date`,
    COUNT(*) as `total_records`,
    SUM(`gateway_amount`) as `total_gateway`,
    SUM(`riyal_mobile_amount`) as `total_riyal`,
    SUM(`gateway_amount` + `riyal_mobile_amount`) as `grand_total`
FROM `collections`
GROUP BY `collection_date`
ORDER BY `collection_date` DESC;

CREATE VIEW `agent_summary` AS
SELECT 
    a.`agent_id`,
    a.`agent_name`,
    COUNT(c.`id`) as `total_records`,
    COALESCE(SUM(c.`gateway_amount`), 0) as `total_gateway`,
    COALESCE(SUM(c.`riyal_mobile_amount`), 0) as `total_riyal`,
    COALESCE(SUM(c.`gateway_amount` + c.`riyal_mobile_amount`), 0) as `grand_total`,
    MAX(c.`collection_date`) as `last_collection_date`
FROM `agents` a
LEFT JOIN `collections` c ON a.`agent_id` = c.`agent_id`
GROUP BY a.`agent_id`, a.`agent_name`
ORDER BY `grand_total` DESC;

-- إنشاء stored procedures مفيدة
DELIMITER //

CREATE PROCEDURE GetDailyReport(IN report_date DATE)
BEGIN
    SELECT 
        a.agent_name,
        COALESCE(c.gateway_amount, 0) as gateway_amount,
        COALESCE(c.riyal_mobile_amount, 0) as riyal_mobile_amount,
        COALESCE(c.gateway_amount + c.riyal_mobile_amount, 0) as total_amount
    FROM agents a
    LEFT JOIN collections c ON a.agent_id = c.agent_id AND c.collection_date = report_date
    ORDER BY a.agent_name;
END //

CREATE PROCEDURE GetAgentReport(IN agent_id INT, IN start_date DATE, IN end_date DATE)
BEGIN
    SELECT 
        collection_date,
        gateway_amount,
        riyal_mobile_amount,
        (gateway_amount + riyal_mobile_amount) as total_amount
    FROM collections
    WHERE agent_id = agent_id 
    AND collection_date BETWEEN start_date AND end_date
    ORDER BY collection_date DESC;
END //

DELIMITER ;

-- إعطاء صلاحيات للمستخدم (إذا لزم الأمر)
-- GRANT ALL PRIVILEGES ON collections_system.* TO 'root'@'localhost';
-- FLUSH PRIVILEGES;

-- تعليقات مفيدة
-- كلمة مرور المستخدمين الافتراضية: admin123 للمدير، user123 للمستخدم العادي
-- يمكن الوصول لقاعدة البيانات عبر: https://localhost/phpmyadmin/
-- اسم قاعدة البيانات: collections_system
