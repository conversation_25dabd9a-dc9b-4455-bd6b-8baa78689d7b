<?php
require_once 'config/database_sqlite.php';

// تم نقل العنوان إلى HTML

// جلب الوكلاء من قاعدة البيانات
$agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents ORDER BY agent_name");
$agents_list = [];
while ($agent = $agents_stmt->fetch()) {
    $agents_list[] = $agent['agent_name'];
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الاستيراد المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .import-step {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .file-preview {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .data-type-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        .data-type-card:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .data-type-card.selected {
            border-color: #28a745;
            background: #d4edda;
        }
        .preview-table {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .agent-match {
            background: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .agent-no-match {
            background: #f8d7da;
            color: #721c24;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .success-animation {
            animation: pulse 0.6s ease-in-out;
        }
        .progress-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>

<div class="container-fluid p-2">
    <h4 class="mb-2">🚀 الاستيراد المتقدم</h4>

    <!-- اختيار الملف -->
    <div class="card mb-2">
        <div class="card-body p-2">
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">ملف Excel/CSV:</label>
                    <input type="file" id="fileInput" class="form-control form-control-sm" accept=".xlsx,.xls,.csv" onchange="handleFileSelect()">
                </div>
                <div class="col-md-6">
                    <label class="form-label">ملفات محددة مسبقاً:</label>
                    <select id="predefinedFiles" class="form-select form-select-sm" onchange="handlePredefinedFile()">
                        <option value="">اختر ملف...</option>
                        <option value="E:\agent\database\import.xlsx">import.xlsx (البوابة)</option>
                        <option value="E:\agent\database\ryal.xlsx">ryal.xlsx (ريال موبايل)</option>
                        <option value="E:\agent\database\combined.xlsx">combined.xlsx (مختلط)</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- الخطوة 2: معاينة الملف -->
    <div id="filePreviewSection" style="display: none;">
        <div class="import-step">
            <h3><i class="fas fa-eye"></i> الخطوة 2: معاينة محتوى الملف</h3>
            <div id="fileInfo" class="mb-3"></div>
            <div id="filePreview" class="preview-table"></div>
        </div>
    </div>

    <!-- الخطوة 3: تحديد نوع البيانات -->
    <div id="dataTypeSection" style="display: none;">
        <div class="import-step">
            <h3><i class="fas fa-cogs"></i> الخطوة 3: تحديد نوع البيانات</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="data-type-card" onclick="selectDataType('gateway')">
                        <div class="text-center">
                            <i class="fas fa-building fa-3x text-success mb-3"></i>
                            <h5>بيانات البوابة</h5>
                            <p>سيتم حفظ البيانات في عمود البوابة لكل وكيل حسب تاريخ العمود</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="data-type-card" onclick="selectDataType('riyal')">
                        <div class="text-center">
                            <i class="fas fa-mobile-alt fa-3x text-info mb-3"></i>
                            <h5>بيانات ريال موبايل</h5>
                            <p>سيتم حفظ البيانات في عمود ريال موبايل لكل وكيل حسب تاريخ العمود</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="data-type-card" onclick="selectDataType('combined')">
                        <div class="text-center">
                            <i class="fas fa-layer-group fa-3x text-warning mb-3"></i>
                            <h5>بيانات مختلطة</h5>
                            <p>البوابة وريال موبايل معاً (ليوم واحد فقط)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الخطوة 4: تحديد التاريخ للبيانات المختلطة -->
    <div id="dateSelectionSection" style="display: none;">
        <div class="import-step">
            <h3><i class="fas fa-calendar"></i> الخطوة 4: تحديد التاريخ</h3>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">تاريخ البيانات المختلطة:</label>
                    <input type="date" id="combinedDate" class="form-control" value="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-6">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> البيانات المختلطة يمكن استيرادها ليوم واحد فقط
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الخطوة 5: مطابقة الوكلاء -->
    <div id="agentMappingSection" style="display: none;">
        <div class="import-step">
            <h3><i class="fas fa-users"></i> الخطوة 5: مطابقة الوكلاء</h3>
            <div id="agentMapping"></div>
        </div>
    </div>

    <!-- الخطوة 6: بدء الاستيراد -->
    <div id="importSection" style="display: none;">
        <div class="import-step">
            <h3><i class="fas fa-play"></i> الخطوة 6: بدء الاستيراد</h3>
            <div class="text-center">
                <button class="btn btn-success btn-lg" onclick="startImport()">
                    <i class="fas fa-rocket"></i> بدء عملية الاستيراد
                </button>
            </div>
        </div>
    </div>

    <!-- نتائج الاستيراد -->
    <div id="resultsSection" style="display: none;">
        <div class="import-step">
            <h3><i class="fas fa-chart-bar"></i> نتائج الاستيراد</h3>
            <div id="importResults"></div>
        </div>
    </div>
</div>

<script>
let selectedFile = null;
let fileData = [];
let selectedDataType = '';
let agentsList = <?= json_encode($agents_list) ?>;

function handleFileSelect() {
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];

    if (file) {
        selectedFile = file;
        processFile(file);
    }
}

function handlePredefinedFile() {
    const select = document.getElementById('predefinedFiles');
    const filePath = select.value;

    if (filePath) {
        // محاكاة تحميل ملف من المسار المحدد
        fetch('load_predefined_file.php?file=' + encodeURIComponent(filePath))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    fileData = data.data;
                    showFilePreview(data.filename, data.data);
                } else {
                    alert('خطأ في تحميل الملف: ' + data.error);
                }
            })
            .catch(error => {
                alert('خطأ في الاتصال: ' + error);
            });
    }
}

function processFile(file) {
    const reader = new FileReader();

    reader.onload = function(e) {
        const data = new Uint8Array(e.target.result);

        if (file.name.endsWith('.csv')) {
            // معالجة ملف CSV
            const text = new TextDecoder().decode(data);
            const rows = text.split('\n').map(row => row.split(','));
            fileData = rows;
            showFilePreview(file.name, rows);
        } else {
            // معالجة ملف Excel (يحتاج مكتبة)
            alert('يرجى تحويل ملف Excel إلى CSV أولاً أو استخدام الملفات المحددة مسبقاً');
        }
    };

    reader.readAsArrayBuffer(file);
}

function showFilePreview(filename, data) {
    document.getElementById('filePreviewSection').style.display = 'block';

    // معلومات الملف
    const fileInfo = document.getElementById('fileInfo');
    fileInfo.innerHTML = `
        <div class="alert alert-info">
            <h5><i class="fas fa-file"></i> ${filename}</h5>
            <p>عدد الصفوف: ${data.length} | عدد الأعمدة: ${data[0] ? data[0].length : 0}</p>
        </div>
    `;

    // معاينة البيانات
    const preview = document.getElementById('filePreview');
    let table = '<table class="table table-striped table-sm">';

    for (let i = 0; i < Math.min(10, data.length); i++) {
        table += '<tr>';
        for (let j = 0; j < data[i].length; j++) {
            const cellClass = i === 0 ? 'table-dark' : '';
            table += `<td class="${cellClass}">${data[i][j] || ''}</td>`;
        }
        table += '</tr>';
    }
    table += '</table>';

    if (data.length > 10) {
        table += '<p class="text-muted">عرض أول 10 صفوف فقط...</p>';
    }

    preview.innerHTML = table;

    // إظهار قسم تحديد نوع البيانات
    document.getElementById('dataTypeSection').style.display = 'block';
}

function selectDataType(type) {
    selectedDataType = type;

    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.data-type-card').forEach(card => {
        card.classList.remove('selected');
    });

    // تحديد البطاقة المختارة
    event.target.closest('.data-type-card').classList.add('selected');

    if (type === 'combined') {
        document.getElementById('dateSelectionSection').style.display = 'block';
    } else {
        document.getElementById('dateSelectionSection').style.display = 'none';
    }

    // إظهار قسم مطابقة الوكلاء
    showAgentMapping();
}

function showAgentMapping() {
    document.getElementById('agentMappingSection').style.display = 'block';

    const mapping = document.getElementById('agentMapping');
    let html = '<div class="row">';

    // تحليل الوكلاء في الملف
    const fileAgents = [];
    for (let i = 1; i < fileData.length; i++) {
        if (fileData[i][0]) {
            fileAgents.push(fileData[i][0].trim());
        }
    }

    html += '<div class="col-md-6">';
    html += '<h5>الوكلاء في الملف:</h5>';
    html += '<ul class="list-group">';

    fileAgents.forEach(agent => {
        const match = findAgentMatch(agent);
        const badgeClass = match ? 'agent-match' : 'agent-no-match';
        const matchText = match ? `✓ ${match}` : '✗ غير موجود';

        html += `<li class="list-group-item d-flex justify-content-between">
            ${agent}
            <span class="${badgeClass}">${matchText}</span>
        </li>`;
    });

    html += '</ul></div>';

    html += '<div class="col-md-6">';
    html += '<h5>الوكلاء في قاعدة البيانات:</h5>';
    html += '<ul class="list-group">';

    agentsList.forEach(agent => {
        html += `<li class="list-group-item">${agent}</li>`;
    });

    html += '</ul></div>';
    html += '</div>';

    mapping.innerHTML = html;

    // إظهار قسم بدء الاستيراد
    document.getElementById('importSection').style.display = 'block';
}

function findAgentMatch(fileAgent) {
    for (let dbAgent of agentsList) {
        if (dbAgent === fileAgent ||
            dbAgent.includes(fileAgent) ||
            fileAgent.includes(dbAgent)) {
            return dbAgent;
        }
    }
    return null;
}

function startImport() {
    // التحقق من صحة البيانات
    if (!selectedDataType) {
        alert('يرجى تحديد نوع البيانات أولاً');
        return;
    }

    if (!fileData || fileData.length === 0) {
        alert('لا توجد بيانات للاستيراد');
        return;
    }

    if (selectedDataType === 'combined' && !document.getElementById('combinedDate').value) {
        alert('يرجى تحديد تاريخ للبيانات المختلطة');
        return;
    }

    // تأكيد بدء العملية
    if (!confirm('هل أنت متأكد من بدء عملية الاستيراد؟\nسيتم تحديث البيانات الموجودة.')) {
        return;
    }

    // إظهار قسم النتائج
    document.getElementById('resultsSection').style.display = 'block';

    const results = document.getElementById('importResults');

    // شريط التقدم
    results.innerHTML = `
        <div class="progress-container">
            <div class="text-center mb-4">
                <h4><i class="fas fa-rocket"></i> جاري الاستيراد...</h4>
                <div class="progress" style="height: 35px; border-radius: 20px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: 0%; border-radius: 20px; font-weight: bold; font-size: 16px;">0%</div>
                </div>
                <div id="progressText" class="mt-3 text-primary font-weight-bold">🚀 بدء العملية...</div>
            </div>
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-list"></i> تفاصيل العملية</h6>
                </div>
                <div class="card-body">
                    <div id="progressDetails" style="max-height: 250px; overflow-y: auto; font-family: monospace; font-size: 14px;">
                        <p><i class="fas fa-play text-success"></i> <strong>[${new Date().toLocaleTimeString('ar-SA')}]</strong> بدء عملية الاستيراد...</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تعطيل زر الاستيراد
    const importButton = document.querySelector('button[onclick="startImport()"]');
    importButton.disabled = true;
    importButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';

    // إعداد بيانات الاستيراد
    const importData = {
        dataType: selectedDataType,
        fileData: fileData,
        combinedDate: selectedDataType === 'combined' ? document.getElementById('combinedDate').value : null
    };

    // تحديث شريط التقدم
    updateProgress(10, '⚙️ إعداد البيانات...');
    addProgressDetail('<i class="fas fa-cog text-info"></i> إعداد بيانات الاستيراد...');

    setTimeout(() => {
        updateProgress(20, '📤 إرسال البيانات للخادم...');
        addProgressDetail('<i class="fas fa-upload text-primary"></i> إرسال البيانات للمعالجة...');

        // إرسال البيانات للمعالجة
        fetch('process_advanced_import.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(importData)
        })
        .then(response => {
            updateProgress(50, '📥 معالجة الاستجابة...');
            addProgressDetail('<i class="fas fa-server text-warning"></i> استلام الاستجابة من الخادم...');

            if (!response.ok) {
                throw new Error('خطأ في الخادم: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            updateProgress(80, '📊 تحليل النتائج...');
            addProgressDetail('<i class="fas fa-chart-bar text-info"></i> تحليل نتائج الاستيراد...');

            setTimeout(() => {
                if (data.success) {
                    updateProgress(100, '✅ تم الاستيراد بنجاح!');
                    addProgressDetail('<i class="fas fa-check-circle text-success"></i> تم الانتهاء من الاستيراد بنجاح!');

                    setTimeout(() => {
                        showImportResults(data);
                    }, 1000);
                } else {
                    updateProgress(100, 'فشل في الاستيراد');
                    addProgressDetail('<i class="fas fa-times-circle text-danger"></i> فشل في الاستيراد: ' + data.error);

                    setTimeout(() => {
                        results.innerHTML = `
                            <div class="alert alert-danger">
                                <h4><i class="fas fa-exclamation-triangle"></i> خطأ في الاستيراد</h4>
                                <p>${data.error}</p>
                                <button class="btn btn-secondary" onclick="location.reload()">
                                    <i class="fas fa-redo"></i> إعادة المحاولة
                                </button>
                            </div>
                        `;
                    }, 2000);
                }
            }, 500);
        })
        .catch(error => {
            console.error('خطأ:', error);
            updateProgress(100, 'خطأ في الاتصال');
            addProgressDetail('<i class="fas fa-exclamation-triangle text-danger"></i> خطأ في الاتصال: ' + error.message);

            setTimeout(() => {
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h4><i class="fas fa-wifi"></i> خطأ في الاتصال</h4>
                        <p>تعذر الاتصال بالخادم: ${error.message}</p>
                        <button class="btn btn-secondary" onclick="location.reload()">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                `;
            }, 2000);
        })
        .finally(() => {
            // إعادة تفعيل الزر
            importButton.disabled = false;
            importButton.innerHTML = '<i class="fas fa-rocket"></i> بدء عملية الاستيراد';
        });
    }, 500);
}

function updateProgress(percentage, text) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    if (progressBar) {
        progressBar.style.width = percentage + '%';
        progressBar.textContent = percentage + '%';

        // تغيير لون شريط التقدم حسب النسبة
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        if (percentage === 100) {
            progressBar.classList.add('bg-success');
        } else if (percentage >= 50) {
            progressBar.classList.add('bg-info');
        } else {
            progressBar.classList.add('bg-primary');
        }
    }

    if (progressText) {
        progressText.textContent = text;
    }
}

function addProgressDetail(message) {
    const progressDetails = document.getElementById('progressDetails');
    if (progressDetails) {
        const timestamp = new Date().toLocaleTimeString('ar-SA');
        progressDetails.innerHTML += `<p><small class="text-muted">[${timestamp}]</small> ${message}</p>`;
        progressDetails.scrollTop = progressDetails.scrollHeight;
    }
}

function showImportResults(data) {
    const results = document.getElementById('importResults');

    // رسالة نجاح مع صوت تنبيه
    if (data.total > 0) {
        // محاولة تشغيل صوت نجاح (إذا كان متاحاً)
        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.play().catch(() => {}); // تجاهل الأخطاء
        } catch (e) {}
    }

    // تحديد نوع التنبيه بناءً على النتائج
    const alertClass = data.errors > 0 ? 'alert-warning' : 'alert-success';
    const iconClass = data.errors > 0 ? 'fa-exclamation-triangle' : 'fa-check-circle';
    const statusMessage = data.status_message || 'تمت معالجة البيانات وحفظها في قاعدة البيانات';

    let html = `
        <div class="alert ${alertClass} text-center mb-4">
            <h3><i class="fas ${iconClass}"></i> ${statusMessage}</h3>
            <p>تم معالجة ${data.total || 0} سجل بنجاح${data.errors > 0 ? ' مع تخطي ' + data.errors + ' خطأ' : ''}</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-success text-center">
                    <div class="card-body">
                        <i class="fas fa-plus-circle fa-2x text-success mb-2"></i>
                        <h3 class="text-success">${data.imported || 0}</h3>
                        <p class="mb-0">سجل جديد</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-info text-center">
                    <div class="card-body">
                        <i class="fas fa-edit fa-2x text-info mb-2"></i>
                        <h3 class="text-info">${data.updated || 0}</h3>
                        <p class="mb-0">سجل محدث</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h3 class="text-warning">${data.errors || 0}</h3>
                        <p class="mb-0">خطأ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-primary text-center">
                    <div class="card-body">
                        <i class="fas fa-calculator fa-2x text-primary mb-2"></i>
                        <h3 class="text-primary">${data.total || 0}</h3>
                        <p class="mb-0">إجمالي العمليات</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة ملخص قاعدة البيانات إذا كان متاحاً
    if (data.summary) {
        html += `
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-database"></i> ملخص قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h6>إجمالي السجلات</h6>
                            <h4 class="text-primary">${data.summary.total_records}</h4>
                        </div>
                        <div class="col-md-3">
                            <h6>إجمالي البوابة</h6>
                            <h4 class="text-success">${data.summary.total_gateway}</h4>
                        </div>
                        <div class="col-md-3">
                            <h6>إجمالي ريال موبايل</h6>
                            <h4 class="text-info">${data.summary.total_riyal}</h4>
                        </div>
                        <div class="col-md-3">
                            <h6>الإجمالي العام</h6>
                            <h4 class="text-warning">${data.summary.grand_total}</h4>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // إضافة تفاصيل العملية
    if (data.details && data.details.length > 0) {
        html += `
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> تفاصيل العملية
                        <span class="badge bg-secondary">${data.details.length} عنصر</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
        `;

        data.details.forEach(detail => {
            html += `<p class="mb-1">${detail}</p>`;
        });

        html += `
                    </div>
                </div>
            </div>
        `;
    }

    // أزرار التنقل
    html += `
        <div class="text-center mt-4">
            <div class="btn-group" role="group">
                <a href="main.php" class="btn btn-success btn-lg">
                    <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                </a>
                <button class="btn btn-primary btn-lg" onclick="location.reload()">
                    <i class="fas fa-redo"></i> استيراد ملف آخر
                </button>
                <button class="btn btn-info btn-lg" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة النتائج
                </button>
            </div>
        </div>
    `;

    results.innerHTML = html;

    // إضافة تأثير بصري للنجاح
    results.style.animation = 'fadeIn 0.5s ease-in';

    // رسالة تأكيد
    setTimeout(() => {
        if (data.total > 0) {
            alert(`✅ تم الاستيراد بنجاح!\n\n📊 النتائج:\n• ${data.imported || 0} سجل جديد\n• ${data.updated || 0} سجل محدث\n• ${data.errors || 0} خطأ\n\n🎉 إجمالي العمليات: ${data.total || 0}`);
        }
    }, 500);
}
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
