<?php
require_once 'config/database.php';

$agent_id = $_GET['agent_id'] ?? '';
$from_date = $_GET['from'] ?? date('Y-m-d', strtotime('-30 days'));
$to_date = $_GET['to'] ?? date('Y-m-d');

// جلب قائمة الوكلاء
$agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll(PDO::FETCH_ASSOC);

if ($agent_id && $from_date && $to_date) {
    // جلب اسم الوكيل
    $agent_stmt = $pdo->prepare("SELECT agent_name FROM agents WHERE agent_id = ?");
    $agent_stmt->execute([$agent_id]);
    $agent_name = $agent_stmt->fetchColumn();
    
    // جلب تحصيلات الوكيل
    $query = "
        SELECT collection_date, gateway_amount, riyal_mobile_amount,
               (gateway_amount + riyal_mobile_amount) as daily_total
        FROM collections
        WHERE agent_id = ? AND collection_date BETWEEN ? AND ?
        ORDER BY collection_date DESC
    ";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$agent_id, $from_date, $to_date]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب الإجماليات
    $total_gateway = array_sum(array_column($results, 'gateway_amount'));
    $total_riyal = array_sum(array_column($results, 'riyal_mobile_amount'));
    $grand_total = $total_gateway + $total_riyal;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير تفصيلي للوكيل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">تقرير تفصيلي للوكيل</h2>
    
    <form method="GET" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <label>الوكيل:</label>
                <select name="agent_id" class="form-control" required>
                    <option value="">اختر الوكيل</option>
                    <?php foreach ($agents as $agent): ?>
                    <option value="<?= $agent['agent_id'] ?>" <?= $agent_id == $agent['agent_id'] ? 'selected' : '' ?>>
                        <?= $agent['agent_name'] ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label>من تاريخ:</label>
                <input type="date" name="from" class="form-control" value="<?= $from_date ?>">
            </div>
            <div class="col-md-3">
                <label>إلى تاريخ:</label>
                <input type="date" name="to" class="form-control" value="<?= $to_date ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">عرض التقرير</button>
            </div>
        </div>
    </form>
    
    <?php if (isset($results) && $agent_name): ?>
    <div class="card mb-4">
        <div class="card-header text-center">
            <h4>تقرير الوكيل: <?= $agent_name ?> من <?= $from_date ?> إلى <?= $to_date ?></h4>
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>التاريخ</th>
                        <th>البوابة</th>
                        <th>الريال موبايل</th>
                        <th>الإجمالي اليومي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($results as $row): ?>
                    <tr>
                        <td><?= $row['collection_date'] ?></td>
                        <td><?= number_format($row['gateway_amount'], 2) ?></td>
                        <td><?= number_format($row['riyal_mobile_amount'], 2) ?></td>
                        <td><?= number_format($row['daily_total'], 2) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-success">
                    <tr>
                        <th>الإجمالي</th>
                        <th><?= number_format($total_gateway, 2) ?></th>
                        <th><?= number_format($total_riyal, 2) ?></th>
                        <th><?= number_format($grand_total, 2) ?></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <?php endif; ?>
    
    <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
</div>
</body>
</html>

