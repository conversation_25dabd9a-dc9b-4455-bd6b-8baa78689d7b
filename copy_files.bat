@echo off
echo نسخ ملفات النظام إلى XAMPP...

set SOURCE_DIR=%~dp0
set TARGET_DIR=C:\xampp\htdocs\collections_system

REM إنشاء المجلد الهدف
if not exist "%TARGET_DIR%" mkdir "%TARGET_DIR%"

REM نسخ الملفات الأساسية
copy "%SOURCE_DIR%index.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%reports.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%add_collection.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%manage_agents.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%install.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%backup.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%export_excel.php" "%TARGET_DIR%\"
copy "%SOURCE_DIR%weekly_detailed_report.php" "%TARGET_DIR%\"

REM نسخ ملفات الإعداد
if not exist "%TARGET_DIR%\config" mkdir "%TARGET_DIR%\config"
copy "%SOURCE_DIR%config\database.php" "%TARGET_DIR%\config\"
copy "%SOURCE_DIR%config\server_config.php" "%TARGET_DIR%\config\"

REM نسخ قاعدة البيانات
if not exist "%TARGET_DIR%\database" mkdir "%TARGET_DIR%\database"
copy "%SOURCE_DIR%database\schema.sql" "%TARGET_DIR%\database\"

REM نسخ ملفات الويب
copy "%SOURCE_DIR%.htaccess" "%TARGET_DIR%\"
copy "%SOURCE_DIR%web.config" "%TARGET_DIR%\"

echo ✓ تم نسخ جميع الملفات بنجاح
pause
