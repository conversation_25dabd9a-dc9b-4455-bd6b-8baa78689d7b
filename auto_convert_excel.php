<?php
$excel_file = 'E:\agent\database\import.xlsx';
$csv_file = 'database/import.csv';

try {
    if (!file_exists($excel_file)) {
        echo "<p style='color: red;'>ملف Excel غير موجود: $excel_file</p>";
        exit;
    }
    
    // محاولة التحويل باستخدام PowerShell
    $ps_script = "
        try {
            \$excel = New-Object -ComObject Excel.Application
            \$excel.Visible = \$false
            \$excel.DisplayAlerts = \$false
            
            \$workbook = \$excel.Workbooks.Open('$excel_file')
            \$worksheet = \$workbook.Worksheets.Item(1)
            
            # حفظ كـ CSV
            \$workbook.SaveAs('$csv_file', 6)
            
            \$workbook.Close()
            \$excel.Quit()
            
            Write-Output 'SUCCESS: تم التحويل بنجاح'
        }
        catch {
            Write-Output 'ERROR: ' + \$_.Exception.Message
        }
    ";
    
    // كتابة السكريبت في ملف مؤقت
    $temp_ps_file = 'temp_convert_' . time() . '.ps1';
    file_put_contents($temp_ps_file, $ps_script);
    
    // تشغيل PowerShell
    $command = "powershell -ExecutionPolicy Bypass -File \"$temp_ps_file\" 2>&1";
    $output = shell_exec($command);
    
    // حذف الملف المؤقت
    if (file_exists($temp_ps_file)) {
        unlink($temp_ps_file);
    }
    
    if (strpos($output, 'SUCCESS') !== false) {
        echo "<p style='color: green;'>✅ تم التحويل بنجاح!</p>";
        echo "<p>تم إنشاء ملف CSV: $csv_file</p>";
        
        if (file_exists($csv_file)) {
            echo "<p>حجم الملف: " . number_format(filesize($csv_file)) . " بايت</p>";
            echo "<p style='color: blue;'>سيتم إعادة تحميل الصفحة خلال 3 ثوان...</p>";
        }
    } else {
        echo "<p style='color: red;'>فشل في التحويل</p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$output</pre>";
        
        // محاولة طريقة بديلة
        echo "<p>محاولة طريقة بديلة...</p>";
        
        // استخدام VBScript كبديل
        $vbs_script = "
            Set objExcel = CreateObject(\"Excel.Application\")
            objExcel.Visible = False
            objExcel.DisplayAlerts = False
            
            Set objWorkbook = objExcel.Workbooks.Open(\"$excel_file\")
            objWorkbook.SaveAs \"$csv_file\", 6
            
            objWorkbook.Close
            objExcel.Quit
            
            WScript.Echo \"VBS: تم التحويل بنجاح\"
        ";
        
        $temp_vbs_file = 'temp_convert_' . time() . '.vbs';
        file_put_contents($temp_vbs_file, $vbs_script);
        
        $vbs_output = shell_exec("cscript //NoLogo \"$temp_vbs_file\" 2>&1");
        
        if (file_exists($temp_vbs_file)) {
            unlink($temp_vbs_file);
        }
        
        if (file_exists($csv_file)) {
            echo "<p style='color: green;'>✅ تم التحويل بنجاح باستخدام VBScript!</p>";
        } else {
            echo "<p style='color: orange;'>لم ينجح التحويل التلقائي. يرجى التحويل يدوياً:</p>";
            echo "<ol>";
            echo "<li>افتح ملف Excel: $excel_file</li>";
            echo "<li>اختر File > Save As</li>";
            echo "<li>اختر نوع الملف: CSV (Comma delimited)</li>";
            echo "<li>احفظ الملف في: $csv_file</li>";
            echo "</ol>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
