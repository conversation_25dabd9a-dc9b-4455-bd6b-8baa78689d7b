<?php
session_start();
require_once 'config/database_sqlite.php';

$error = '';

// إذا كان المستخدم مسجل دخول بالفعل، توجيه إلى الصفحة الرئيسية
if (isset($_SESSION['user_id'])) {
    header('Location: main.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT user_id, employee_name, username, password, permissions FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['employee_name'] = $user['employee_name'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['permissions'] = $user['permissions'];

                // تحديث وقت آخر دخول
                $update_stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE user_id = ?");
                $update_stmt->execute([$user['user_id']]);

                header('Location: main.php');
                exit;
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في الاتصال بقاعدة البيانات';
        }
    }
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة التحصيلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <h2 class="login-title">نظام إدارة تحصيلات الوكلاء</h2>

            <?php if ($error): ?>
                <div class="alert alert-danger text-center" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="username" class="form-label">اسم المستخدم:</label>
                    <input type="text"
                           id="username"
                           name="username"
                           class="form-control"
                           required
                           autocomplete="username"
                           value="<?= isset($_POST['username']) ? htmlspecialchars($_POST['username']) : '' ?>">
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">كلمة المرور:</label>
                    <input type="password"
                           id="password"
                           name="password"
                           class="form-control"
                           required
                           autocomplete="current-password">
                </div>

                <button type="submit" class="btn-login">تسجيل الدخول</button>
            </form>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    <strong>بيانات تجريبية:</strong><br>
                    المدير: admin / admin123<br>
                    مستخدم: user1 / admin123
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
