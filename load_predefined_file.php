<?php
header('Content-Type: application/json');

$file_path = $_GET['file'] ?? '';

try {
    if (empty($file_path)) {
        throw new Exception('لم يتم تحديد مسار الملف');
    }
    
    // التحقق من وجود الملف
    if (!file_exists($file_path)) {
        throw new Exception('الملف غير موجود: ' . $file_path);
    }
    
    $filename = basename($file_path);
    $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
    
    if ($extension === 'csv') {
        // قراءة ملف CSV
        $data = [];
        $handle = fopen($file_path, 'r');
        
        if ($handle) {
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $data[] = $row;
            }
            fclose($handle);
        } else {
            throw new Exception('لا يمكن فتح ملف CSV');
        }
        
    } elseif (in_array($extension, ['xlsx', 'xls'])) {
        // محاولة تحويل Excel إلى CSV مؤقتاً
        $temp_csv = 'temp_' . time() . '.csv';
        
        // استخدام PowerShell لتحويل Excel إلى CSV
        $ps_script = "
            try {
                \$excel = New-Object -ComObject Excel.Application
                \$excel.Visible = \$false
                \$excel.DisplayAlerts = \$false
                
                \$workbook = \$excel.Workbooks.Open('$file_path')
                \$worksheet = \$workbook.Worksheets.Item(1)
                
                \$workbook.SaveAs('$temp_csv', 6)
                \$workbook.Close()
                \$excel.Quit()
                
                Write-Output 'SUCCESS'
            }
            catch {
                Write-Output 'ERROR: ' + \$_.Exception.Message
            }
        ";
        
        $temp_ps_file = 'temp_convert_' . time() . '.ps1';
        file_put_contents($temp_ps_file, $ps_script);
        
        $output = shell_exec("powershell -ExecutionPolicy Bypass -File \"$temp_ps_file\" 2>&1");
        
        // حذف ملف PowerShell المؤقت
        if (file_exists($temp_ps_file)) {
            unlink($temp_ps_file);
        }
        
        if (strpos($output, 'SUCCESS') !== false && file_exists($temp_csv)) {
            // قراءة ملف CSV المؤقت
            $data = [];
            $handle = fopen($temp_csv, 'r');
            
            if ($handle) {
                while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $data[] = $row;
                }
                fclose($handle);
            }
            
            // حذف ملف CSV المؤقت
            unlink($temp_csv);
            
        } else {
            throw new Exception('فشل في تحويل ملف Excel. يرجى تحويله إلى CSV يدوياً');
        }
        
    } else {
        throw new Exception('نوع ملف غير مدعوم. يرجى استخدام CSV أو Excel');
    }
    
    // التحقق من صحة البيانات
    if (empty($data)) {
        throw new Exception('الملف فارغ أو لا يحتوي على بيانات صالحة');
    }
    
    // تنظيف البيانات
    $cleaned_data = [];
    foreach ($data as $row) {
        $cleaned_row = [];
        foreach ($row as $cell) {
            $cleaned_row[] = trim($cell);
        }
        $cleaned_data[] = $cleaned_row;
    }
    
    echo json_encode([
        'success' => true,
        'filename' => $filename,
        'data' => $cleaned_data,
        'rows' => count($cleaned_data),
        'columns' => count($cleaned_data[0] ?? [])
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
