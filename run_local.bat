@echo off
title نظام إدارة تحصيلات الوكلاء - محلي

echo ========================================
echo تشغيل النظام محلي<|im_start|>
echo ========================================

REM 1. التحقق من وجود XAMPP
if not exist "C:\xampp\xampp-control.exe" (
    echo خطأ: XAMPP غير مثبت
    echo يرجى تحميله من: https://www.apachefriends.org/download.html
    pause
    exit /b 1
)

REM 2. نسخ الملفات
echo خطوة 1: نسخ الملفات...
call copy_files.bat

REM 3. إعداد قاعدة البيانات
echo خطوة 2: إعداد قاعدة البيانات...
"C:\xampp\mysql\bin\mysql.exe" -u root -e "CREATE DATABASE IF NOT EXISTS collections_system CHARACTER SET utf8 COLLATE utf8_general_ci;"
"C:\xampp\mysql\bin\mysql.exe" -u root collections_system < database\schema.sql

REM 4. بدء XAMPP
echo خطوة 3: بدء XAMPP...
start "" "C:\xampp\xampp-control.exe"

REM 5. فتح المتصفح محلي<|im_start|>
timeout /t 5 /nobreak >nul
start "" "http://localhost:7445"

echo.
echo ✓ النظام يعمل محلي3
echo الرابط المحلي: http://localhost:7445
echo.
pause
