<?php
require_once 'config/database_sqlite.php';

// جلب الوكلاء
$agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents ORDER BY agent_name");
$agents_list = $agents_stmt->fetchAll();

// التاريخ المحدد
$selected_date = date('Y-m-d');

// جلب البيانات الموجودة للتاريخ المحدد
$existing_data = [];
$existing_query = $pdo->prepare("
    SELECT agent_id, gateway_amount, riyal_mobile_amount
    FROM collections
    WHERE collection_date = ?
");
$existing_query->execute([$selected_date]);
$existing_results = $existing_query->fetchAll(PDO::FETCH_ASSOC);

foreach ($existing_results as $row) {
    $existing_data[$row['agent_id']] = [
        'gateway' => $row['gateway_amount'],
        'riyal' => $row['riyal_mobile_amount']
    ];
}
?>

<div class="container-fluid">
    <!-- شريط التحكم -->
    <div class="row mb-2">
        <div class="col-md-6">
            <div class="input-group input-group-sm">
                <span class="input-group-text">التاريخ:</span>
                <input type="date" id="collection-date" class="form-control" value="<?= $selected_date ?>" onchange="loadCollectionData()">
            </div>
        </div>
        <div class="col-md-6 text-end">
            <span class="badge bg-primary" id="records-count">
                <?= count($existing_data) ?> سجل موجود
            </span>
            <button class="btn btn-success btn-sm" onclick="saveAllCollections()">
                <i class="fas fa-save"></i> حفظ الكل
            </button>
        </div>
    </div>

    <!-- جدول إدخال البيانات -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h6><i class="fas fa-edit"></i> إدخال تحصيلات الوكلاء</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-sm table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 30%;">الوكيل</th>
                            <th style="width: 25%;">البوابة</th>
                            <th style="width: 25%;">ريال موبايل</th>
                            <th style="width: 20%;">الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody id="collections-table">
                        <?php foreach ($agents_list as $agent): ?>
                        <?php 
                            $gateway_amount = $existing_data[$agent['agent_id']]['gateway'] ?? 0;
                            $riyal_amount = $existing_data[$agent['agent_id']]['riyal'] ?? 0;
                            $total = $gateway_amount + $riyal_amount;
                        ?>
                        <tr data-agent-id="<?= $agent['agent_id'] ?>">
                            <td>
                                <strong><?= htmlspecialchars($agent['agent_name']) ?></strong>
                            </td>
                            <td>
                                <input type="number" 
                                       class="form-control form-control-sm gateway-input" 
                                       step="0.01" 
                                       min="0"
                                       value="<?= number_format($gateway_amount, 2, '.', '') ?>"
                                       onchange="calculateTotal(this)"
                                       onkeydown="handleKeyDown(event, this)">
                            </td>
                            <td>
                                <input type="number" 
                                       class="form-control form-control-sm riyal-input" 
                                       step="0.01" 
                                       min="0"
                                       value="<?= number_format($riyal_amount, 2, '.', '') ?>"
                                       onchange="calculateTotal(this)"
                                       onkeydown="handleKeyDown(event, this)">
                            </td>
                            <td>
                                <span class="total-amount fw-bold text-success">
                                    <?= number_format($total, 2) ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-secondary">
                        <tr>
                            <th>الإجمالي العام</th>
                            <th id="total-gateway">0.00</th>
                            <th id="total-riyal">0.00</th>
                            <th id="grand-total">0.00</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- نتائج العمليات -->
    <div id="collection-results" class="mt-2"></div>
</div>

<script>
// حساب الإجمالي لكل صف
function calculateTotal(input) {
    const row = input.closest('tr');
    const gatewayInput = row.querySelector('.gateway-input');
    const riyalInput = row.querySelector('.riyal-input');
    const totalSpan = row.querySelector('.total-amount');
    
    const gateway = parseFloat(gatewayInput.value) || 0;
    const riyal = parseFloat(riyalInput.value) || 0;
    const total = gateway + riyal;
    
    totalSpan.textContent = total.toFixed(2);
    
    // تحديث الإجماليات العامة
    updateGrandTotals();
}

// تحديث الإجماليات العامة
function updateGrandTotals() {
    let totalGateway = 0;
    let totalRiyal = 0;
    
    document.querySelectorAll('#collections-table tr').forEach(row => {
        const gateway = parseFloat(row.querySelector('.gateway-input').value) || 0;
        const riyal = parseFloat(row.querySelector('.riyal-input').value) || 0;
        
        totalGateway += gateway;
        totalRiyal += riyal;
    });
    
    document.getElementById('total-gateway').textContent = totalGateway.toFixed(2);
    document.getElementById('total-riyal').textContent = totalRiyal.toFixed(2);
    document.getElementById('grand-total').textContent = (totalGateway + totalRiyal).toFixed(2);
}

// التنقل بين الحقول باستخدام Enter
function handleKeyDown(event, input) {
    if (event.key === 'Enter') {
        event.preventDefault();
        
        const row = input.closest('tr');
        const currentInput = input;
        let nextInput = null;
        
        // إذا كان في حقل البوابة، انتقل لحقل ريال موبايل في نفس الصف
        if (currentInput.classList.contains('gateway-input')) {
            nextInput = row.querySelector('.riyal-input');
        }
        // إذا كان في حقل ريال موبايل، انتقل لحقل البوابة في الصف التالي
        else if (currentInput.classList.contains('riyal-input')) {
            const nextRow = row.nextElementSibling;
            if (nextRow) {
                nextInput = nextRow.querySelector('.gateway-input');
            }
        }
        
        if (nextInput) {
            nextInput.focus();
            nextInput.select();
        }
    }
}

// تحميل بيانات التحصيلات للتاريخ المحدد
function loadCollectionData() {
    const date = document.getElementById('collection-date').value;
    
    if (!date) return;
    
    showAlert('collection-results', 'info', 'جاري تحميل البيانات...');
    
    fetch('load_collection_data.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'date=' + date
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث الجدول بالبيانات الجديدة
            updateTableData(data.collections);
            
            // تحديث عداد السجلات
            document.getElementById('records-count').textContent = data.count + ' سجل موجود';
            
            showAlert('collection-results', 'success', `تم تحميل بيانات ${date} بنجاح`);
        } else {
            showAlert('collection-results', 'danger', `خطأ في تحميل البيانات: ${data.error}`);
        }
    })
    .catch(error => {
        showAlert('collection-results', 'danger', `خطأ في الشبكة: ${error.message}`);
    });
}

// تحديث بيانات الجدول
function updateTableData(collections) {
    document.querySelectorAll('#collections-table tr').forEach(row => {
        const agentId = row.getAttribute('data-agent-id');
        const gatewayInput = row.querySelector('.gateway-input');
        const riyalInput = row.querySelector('.riyal-input');
        
        if (collections[agentId]) {
            gatewayInput.value = parseFloat(collections[agentId].gateway || 0).toFixed(2);
            riyalInput.value = parseFloat(collections[agentId].riyal || 0).toFixed(2);
        } else {
            gatewayInput.value = '0.00';
            riyalInput.value = '0.00';
        }
        
        calculateTotal(gatewayInput);
    });
}

// حفظ جميع التحصيلات
function saveAllCollections() {
    const date = document.getElementById('collection-date').value;
    
    if (!date) {
        showAlert('collection-results', 'warning', 'يرجى تحديد التاريخ');
        return;
    }
    
    // جمع البيانات
    const collections = [];
    document.querySelectorAll('#collections-table tr').forEach(row => {
        const agentId = row.getAttribute('data-agent-id');
        const gateway = parseFloat(row.querySelector('.gateway-input').value) || 0;
        const riyal = parseFloat(row.querySelector('.riyal-input').value) || 0;
        
        if (gateway > 0 || riyal > 0) {
            collections.push({
                agent_id: agentId,
                gateway_amount: gateway,
                riyal_mobile_amount: riyal
            });
        }
    });
    
    if (collections.length === 0) {
        showAlert('collection-results', 'warning', 'لا توجد بيانات للحفظ');
        return;
    }
    
    // إرسال البيانات
    showAlert('collection-results', 'info', 'جاري حفظ البيانات...');
    
    fetch('save_collections.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            date: date,
            collections: collections
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('collection-results', 'success', 
                `تم حفظ البيانات بنجاح! (${data.saved} سجل محفوظ، ${data.updated} سجل محدث)`);
            
            // تحديث عداد السجلات
            document.getElementById('records-count').textContent = collections.length + ' سجل موجود';
            
            // تحديث التقارير إذا كانت مفتوحة
            if (typeof generateReport === 'function') {
                setTimeout(() => generateReport(), 1000);
            }
        } else {
            showAlert('collection-results', 'danger', `خطأ في حفظ البيانات: ${data.error}`);
        }
    })
    .catch(error => {
        showAlert('collection-results', 'danger', `خطأ في الشبكة: ${error.message}`);
    });
}

// عرض التنبيهات
function showAlert(elementId, type, message) {
    const alertClass = {
        'success': 'alert-success',
        'danger': 'alert-danger', 
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    document.getElementById(elementId).innerHTML = `
        <div class="alert ${alertClass[type]} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

// تحديث الإجماليات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrandTotals();
});
</script>
