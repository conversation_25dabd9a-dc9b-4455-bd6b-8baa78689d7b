<?php
require_once 'config/database.php';

header('Content-Type: application/json');

try {
    // إحصائيات قاعدة البيانات
    $stats_query = $pdo->query("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN gateway_amount > 0 THEN 1 END) as gateway_records,
            COUNT(CASE WHEN riyal_mobile_amount > 0 THEN 1 END) as riyal_records,
            SUM(gateway_amount) as total_gateway,
            SUM(riyal_mobile_amount) as total_riyal,
            MAX(collection_date) as last_update
        FROM collections
    ");
    
    $stats = $stats_query->fetch();
    
    // عينة من البيانات الحديثة
    $sample_query = $pdo->query("
        SELECT 
            a.agent_name,
            c.collection_date,
            c.gateway_amount,
            c.riyal_mobile_amount
        FROM collections c
        JOIN agents a ON c.agent_id = a.agent_id
        ORDER BY c.collection_date DESC, a.agent_name
        LIMIT 10
    ");
    
    $sample_data = $sample_query->fetchAll();
    
    echo json_encode([
        'success' => true,
        'total_records' => (int)$stats['total_records'],
        'gateway_records' => (int)$stats['gateway_records'],
        'riyal_records' => (int)$stats['riyal_records'],
        'total_gateway' => number_format((float)$stats['total_gateway'], 2),
        'total_riyal' => number_format((float)$stats['total_riyal'], 2),
        'last_update' => $stats['last_update'] ?: 'لا توجد بيانات',
        'sample_data' => $sample_data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
