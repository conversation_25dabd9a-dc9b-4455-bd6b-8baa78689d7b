<?php
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $fileName = $input['file'] ?? '';
    $type = $input['type'] ?? '';
    
    if (empty($fileName) || empty($type)) {
        throw new Exception('معطيات غير صحيحة');
    }
    
    // مسارات الملفات
    $filePath = __DIR__ . '/database/' . $fileName;
    
    if (!file_exists($filePath)) {
        throw new Exception("الملف غير موجود: $fileName");
    }
    
    // قراءة الملف
    $content = file_get_contents($filePath);
    if ($content === false) {
        throw new Exception("فشل في قراءة الملف: $fileName");
    }
    
    // تحليل CSV
    $lines = explode("\n", $content);
    $lines = array_filter($lines, function($line) {
        return !empty(trim($line));
    });
    
    if (empty($lines)) {
        throw new Exception("الملف فارغ أو لا يحتوي على بيانات صحيحة");
    }
    
    // إنشاء معاينة
    $preview = '<table class="table table-sm table-bordered">';
    $preview .= '<thead><tr><th>الوكيل</th>';
    
    // إضافة رؤوس الأعمدة (التواريخ)
    $firstLine = explode(',', $lines[0]);
    $dateColumns = count($firstLine) - 1; // عدد أعمدة التواريخ
    
    for ($i = 1; $i <= min($dateColumns, 10); $i++) { // عرض أول 10 أعمدة فقط
        $preview .= "<th>يوم $i</th>";
    }
    if ($dateColumns > 10) {
        $preview .= "<th>...</th>";
    }
    $preview .= '</tr></thead><tbody>';
    
    // عرض أول 5 صفوف
    $rowCount = 0;
    foreach ($lines as $line) {
        if ($rowCount >= 5) break;
        
        $cells = explode(',', trim($line));
        if (count($cells) < 2) continue; // تخطي الصفوف غير الصحيحة
        
        $preview .= '<tr>';
        $preview .= '<td><strong>' . htmlspecialchars(trim($cells[0])) . '</strong></td>';
        
        for ($i = 1; $i <= min(count($cells) - 1, 10); $i++) {
            $value = isset($cells[$i]) ? trim($cells[$i]) : '0';
            $preview .= '<td>' . number_format((float)$value, 2) . '</td>';
        }
        
        if (count($cells) > 11) {
            $preview .= '<td>...</td>';
        }
        
        $preview .= '</tr>';
        $rowCount++;
    }
    
    $preview .= '</tbody></table>';
    
    // حفظ البيانات في الجلسة للاستخدام لاحقاً
    session_start();
    $_SESSION['csv_data_' . $type] = $content;
    $_SESSION['csv_file_' . $type] = $fileName;
    
    echo json_encode([
        'success' => true,
        'preview' => $preview,
        'rows' => count($lines),
        'columns' => $dateColumns,
        'file' => $fileName
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
