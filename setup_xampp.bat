@echo off
echo إعداد XAMPP لنظام التحصيلات...

REM نسخ الملفات
call copy_files.bat

REM تحديث إعدادات Apache
echo Listen 7445 >> C:\xampp\apache\conf\httpd.conf

REM إنشاء Virtual Host
echo. >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo ^<VirtualHost *:7445^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     ServerName *********** >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     DocumentRoot "C:/xampp/htdocs/collections_system" >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo     DirectoryIndex index.php >> C:\xampp\apache\conf\extra\httpd-vhosts.conf
echo ^</VirtualHost^> >> C:\xampp\apache\conf\extra\httpd-vhosts.conf

REM بدء XAMPP
"C:\xampp\xampp-control.exe"

echo ✓ تم إعداد XAMPP بنجاح
echo الرابط: http://***********:7445
pause

