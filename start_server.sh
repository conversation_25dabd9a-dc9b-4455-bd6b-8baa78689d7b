#!/bin/bash

echo "بدء تشغيل نظام إدارة تحصيلات الوكلاء..."

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "خطأ: PHP غير مثبت"
    exit 1
fi

# التحقق من وجود MySQL
if ! command -v mysql &> /dev/null; then
    echo "خطأ: MySQL غير مثبت"
    exit 1
fi

# بدء خدمة MySQL
sudo systemctl start mysql
echo "✓ تم بدء خدمة MySQL"

# بدء خدمة Apache
sudo systemctl start apache2
echo "✓ تم بدء خدمة Apache"

# تفعيل المنفذ 7445
sudo ufw allow 7445
echo "✓ تم فتح المنفذ 7445"

# إعادة تحميل إعدادات Apache
sudo systemctl reload apache2
echo "✓ تم إعادة تحميل Apache"

echo ""
echo "النظام يعمل الآن على:"
echo "الرابط الداخلي: http://localhost:7445"
echo "الرابط الخارجي: http://***********:7445"
echo ""
echo "للتوقف: sudo systemctl stop apache2"
