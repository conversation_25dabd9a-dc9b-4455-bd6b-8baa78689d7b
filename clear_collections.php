<?php
require_once 'config/database_sqlite.php';

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    if ($action === 'clear_all') {
        // حذف جميع التحصيلات
        $pdo->exec("DELETE FROM collections");
        $pdo->exec("DELETE FROM sqlite_sequence WHERE name='collections'");
        echo json_encode(['success' => true, 'message' => 'تم حذف جميع التحصيلات']);
        
    } elseif ($action === 'clear_today') {
        // حذف تحصيلات اليوم فقط
        $today = date('Y-m-d');
        $stmt = $pdo->prepare("DELETE FROM collections WHERE collection_date = ?");
        $stmt->execute([$today]);
        echo json_encode(['success' => true, 'message' => 'تم حذف تحصيلات اليوم']);
        
    } else {
        echo json_encode(['success' => false, 'error' => 'إجراء غير صحيح']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
