<?php
require_once 'config/database_sqlite.php';

$from_date = $_GET['from'] ?? date('Y-m-d', strtotime('-7 days'));
$to_date = $_GET['to'] ?? date('Y-m-d');

if ($from_date && $to_date) {
    $query = "
        SELECT a.agent_name, c.collection_date, c.gateway_amount, c.riyal_mobile_amount
        FROM collections c
        JOIN agents a ON c.agent_id = a.agent_id
        WHERE c.collection_date BETWEEN ? AND ?
        ORDER BY a.agent_name, c.collection_date
    ";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$from_date, $to_date]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقرير الأسبوعي التفصيلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">التقرير الأسبوعي التفصيلي</h2>

    <form method="GET" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <label>من تاريخ:</label>
                <input type="date" name="from" class="form-control" value="<?= $from_date ?>">
            </div>
            <div class="col-md-3">
                <label>إلى تاريخ:</label>
                <input type="date" name="to" class="form-control" value="<?= $to_date ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">عرض التقرير</button>
            </div>
        </div>
    </form>

    <?php if (isset($results)): ?>
    <table class="table table-bordered">
        <thead class="table-dark">
            <tr>
                <th>الوكيل</th>
                <th>التاريخ</th>
                <th>البوابة</th>
                <th>الريال موبايل</th>
                <th>الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($results as $row): ?>
            <tr>
                <td><?= $row['agent_name'] ?></td>
                <td><?= date('Y-m-d', strtotime($row['collection_date'])) ?></td>
                <td><?= number_format($row['gateway_amount'], 2) ?></td>
                <td><?= number_format($row['riyal_mobile_amount'], 2) ?></td>
                <td><?= number_format($row['gateway_amount'] + $row['riyal_mobile_amount'], 2) ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php endif; ?>

    <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
</div>
</body>
</html>
