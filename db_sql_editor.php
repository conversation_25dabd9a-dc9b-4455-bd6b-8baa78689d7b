<?php
$sql = $_POST['sql'] ?? '';
$results = null;
$error = null;
$execution_time = 0;

if (!empty($sql)) {
    $start_time = microtime(true);
    
    try {
        // منع استعلامات خطيرة
        $dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE'];
        $sql_upper = strtoupper(trim($sql));
        
        $is_dangerous = false;
        foreach ($dangerous_keywords as $keyword) {
            if (strpos($sql_upper, $keyword) === 0) {
                $is_dangerous = true;
                break;
            }
        }
        
        if ($is_dangerous && !isset($_POST['confirm_dangerous'])) {
            $error = "هذا الاستعلام قد يكون خطيراً. يرجى تأكيد تنفيذه.";
        } else {
            $stmt = $pdo->prepare($sql);
            $stmt->execute();
            
            if (strpos($sql_upper, 'SELECT') === 0) {
                $results = $stmt->fetchAll();
            } else {
                $results = "تم تنفيذ الاستعلام بنجاح. عدد الصفوف المتأثرة: " . $stmt->rowCount();
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
    
    $execution_time = round((microtime(true) - $start_time) * 1000, 2);
}
?>

<div class="card">
    <div class="card-header bg-warning text-dark">
        <h5><i class="fas fa-code"></i> محرر SQL</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="mb-3">
                <label for="sql" class="form-label">استعلام SQL:</label>
                <textarea class="form-control sql-editor" id="sql" name="sql" rows="8" placeholder="أدخل استعلام SQL هنا..."><?= htmlspecialchars($sql) ?></textarea>
            </div>
            
            <?php if ($error && strpos($error, 'خطيراً') !== false): ?>
            <div class="alert alert-warning">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirm_dangerous" name="confirm_dangerous">
                    <label class="form-check-label" for="confirm_dangerous">
                        أؤكد أنني أريد تنفيذ هذا الاستعلام الخطير
                    </label>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-play"></i> تنفيذ
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearEditor()">
                    <i class="fas fa-eraser"></i> مسح
                </button>
                <button type="button" class="btn btn-info" onclick="insertSample()">
                    <i class="fas fa-lightbulb"></i> استعلام تجريبي
                </button>
            </div>
        </form>
        
        <!-- أمثلة سريعة -->
        <div class="mt-3">
            <h6>أمثلة سريعة:</h6>
            <div class="btn-group-vertical btn-group-sm w-100" role="group">
                <button type="button" class="btn btn-outline-secondary text-start" onclick="setSql('SELECT * FROM collections LIMIT 10')">
                    عرض أول 10 تحصيلات
                </button>
                <button type="button" class="btn btn-outline-secondary text-start" onclick="setSql('SELECT agent_name, COUNT(*) as total FROM agents a LEFT JOIN collections c ON a.agent_id = c.agent_id GROUP BY a.agent_id')">
                    إحصائيات الوكلاء
                </button>
                <button type="button" class="btn btn-outline-secondary text-start" onclick="setSql('SELECT collection_date, SUM(gateway_amount + riyal_mobile_amount) as daily_total FROM collections GROUP BY collection_date ORDER BY collection_date DESC')">
                    الإجماليات اليومية
                </button>
                <button type="button" class="btn btn-outline-secondary text-start" onclick="setSql('SELECT name FROM sqlite_master WHERE type=\'table\'')">
                    عرض جميع الجداول
                </button>
            </div>
        </div>
    </div>
</div>

<?php if ($error): ?>
<div class="card mt-3">
    <div class="card-header bg-danger text-white">
        <h6><i class="fas fa-exclamation-triangle"></i> خطأ في الاستعلام</h6>
    </div>
    <div class="card-body">
        <pre class="text-danger"><?= htmlspecialchars($error) ?></pre>
    </div>
</div>
<?php endif; ?>

<?php if ($results && !$error): ?>
<div class="card mt-3">
    <div class="card-header bg-success text-white d-flex justify-content-between">
        <h6><i class="fas fa-check-circle"></i> نتائج الاستعلام</h6>
        <small>وقت التنفيذ: <?= $execution_time ?> مللي ثانية</small>
    </div>
    <div class="card-body">
        <?php if (is_array($results)): ?>
            <?php if (empty($results)): ?>
                <div class="alert alert-info">لا توجد نتائج</div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm">
                        <thead class="table-dark">
                            <tr>
                                <?php foreach (array_keys($results[0]) as $column): ?>
                                    <th><?= htmlspecialchars($column) ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row): ?>
                                <tr>
                                    <?php foreach ($row as $value): ?>
                                        <td>
                                            <?php if (is_null($value)): ?>
                                                <span class="text-muted fst-italic">NULL</span>
                                            <?php elseif (is_numeric($value)): ?>
                                                <span class="text-primary fw-bold"><?= $value ?></span>
                                            <?php else: ?>
                                                <?= htmlspecialchars($value) ?>
                                            <?php endif; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <p class="text-muted mt-2">
                    <small>عدد النتائج: <?= count($results) ?></small>
                </p>
            <?php endif; ?>
        <?php else: ?>
            <div class="alert alert-success"><?= htmlspecialchars($results) ?></div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<script>
function clearEditor() {
    document.getElementById('sql').value = '';
}

function setSql(sql) {
    document.getElementById('sql').value = sql;
}

function insertSample() {
    const samples = [
        'SELECT * FROM collections WHERE collection_date = CURRENT_DATE',
        'SELECT a.agent_name, SUM(c.gateway_amount + c.riyal_mobile_amount) as total\nFROM agents a\nLEFT JOIN collections c ON a.agent_id = c.agent_id\nGROUP BY a.agent_id\nORDER BY total DESC',
        'SELECT collection_date, COUNT(*) as records, SUM(gateway_amount) as gateway_total, SUM(riyal_mobile_amount) as riyal_total\nFROM collections\nGROUP BY collection_date\nORDER BY collection_date DESC'
    ];
    
    const randomSample = samples[Math.floor(Math.random() * samples.length)];
    document.getElementById('sql').value = randomSample;
}

// تحسين محرر SQL
document.getElementById('sql').addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
        e.preventDefault();
        const start = this.selectionStart;
        const end = this.selectionEnd;
        this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
        this.selectionStart = this.selectionEnd = start + 4;
    }
});
</script>
