<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>مستعرض قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-container { max-height: 500px; overflow-y: auto; }
        .sql-editor { font-family: 'Courier New', monospace; }
        .table-info { background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px; }
    </style>
</head>
<body>

<?php
require_once 'config/database_sqlite.php';

$action = $_GET['action'] ?? 'tables';
$table = $_GET['table'] ?? '';
$sql = $_POST['sql'] ?? '';
?>

<div class="container-fluid mt-3">
    <div class="row">
        <!-- الشريط الجانبي -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-database"></i> قاعدة البيانات</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="?action=info" class="list-group-item list-group-item-action <?= $action === 'info' ? 'active' : '' ?>">
                            <i class="fas fa-info-circle"></i> معلومات قاعدة البيانات
                        </a>
                        <a href="?action=tables" class="list-group-item list-group-item-action <?= $action === 'tables' ? 'active' : '' ?>">
                            <i class="fas fa-table"></i> الجداول
                        </a>
                        <a href="?action=sql" class="list-group-item list-group-item-action <?= $action === 'sql' ? 'active' : '' ?>">
                            <i class="fas fa-code"></i> محرر SQL
                        </a>
                        <a href="?action=backup" class="list-group-item list-group-item-action <?= $action === 'backup' ? 'active' : '' ?>">
                            <i class="fas fa-download"></i> نسخ احتياطي
                        </a>
                    </div>
                    
                    <!-- قائمة الجداول -->
                    <div class="mt-3 px-3">
                        <h6>الجداول المتاحة:</h6>
                        <?php
                        try {
                            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
                            foreach ($tables as $tbl) {
                                $isActive = ($table === $tbl['name']) ? 'fw-bold text-primary' : '';
                                echo "<div class='mb-1'>";
                                echo "<a href='?action=table&table={$tbl['name']}' class='text-decoration-none $isActive'>";
                                echo "<i class='fas fa-table fa-sm'></i> {$tbl['name']}";
                                echo "</a></div>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='text-danger'>خطأ: " . $e->getMessage() . "</p>";
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <a href="main.php" class="btn btn-secondary btn-sm">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9">
            <?php
            switch ($action) {
                case 'info':
                    include 'db_info.php';
                    break;
                case 'table':
                    include 'db_table_view.php';
                    break;
                case 'sql':
                    include 'db_sql_editor.php';
                    break;
                case 'backup':
                    include 'db_backup.php';
                    break;
                default:
                    include 'db_tables_list.php';
            }
            ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
