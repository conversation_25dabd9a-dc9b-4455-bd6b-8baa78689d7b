<?php
// الصفحة الرئيسية للتقارير
?>

<div class="reports-main-container">
    <!-- أيقونات التقارير الفرعية -->
    <div class="sub-navigation mb-2">
        <div class="sub-nav-icons">
            <a href="#" class="sub-nav-icon active" onclick="showReportType('daily')">
                <i class="fas fa-calendar-day"></i><br>يومي
            </a>
            <a href="#" class="sub-nav-icon" onclick="showReportType('weekly')">
                <i class="fas fa-calendar-week"></i><br>أسبوعي
            </a>
            <a href="#" class="sub-nav-icon" onclick="showReportType('monthly')">
                <i class="fas fa-calendar-alt"></i><br>شهري
            </a>
            <a href="#" class="sub-nav-icon" onclick="showReportType('agent')">
                <i class="fas fa-user-chart"></i><br>وكيل
            </a>
            <a href="#" class="sub-nav-icon" onclick="showReportType('custom')">
                <i class="fas fa-cogs"></i><br>مخصص
            </a>
        </div>
    </div>

    <!-- منطقة إعدادات التقرير -->
    <div class="report-settings-area">
        <!-- التقرير اليومي -->
        <div id="daily-settings" class="report-settings active">
            <div class="row mb-2">
                <div class="col-md-3">
                    <label class="form-label">التاريخ:</label>
                    <input type="date" id="daily-date" class="form-control" value="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary btn-sm" onclick="generateReport()">
                        <i class="fas fa-search"></i> عرض
                    </button>
                </div>
            </div>
        </div>

        <!-- التقرير الأسبوعي -->
        <div id="weekly-settings" class="report-settings">
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ:</label>
                    <input type="date" id="weekly-start" class="form-control" value="<?= date('Y-m-d', strtotime('monday this week')) ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ:</label>
                    <input type="date" id="weekly-end" class="form-control" value="<?= date('Y-m-d', strtotime('sunday this week')) ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search"></i> عرض التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- التقرير الشهري -->
        <div id="monthly-settings" class="report-settings">
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">الشهر والسنة:</label>
                    <input type="month" id="monthly-date" class="form-control" value="<?= date('Y-m') ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search"></i> عرض التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- تقرير الوكيل -->
        <div id="agent-settings" class="report-settings">
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">اختر الوكيل:</label>
                    <select id="agent-select" class="form-control">
                        <option value="">جميع الوكلاء</option>
                        <?php foreach ($agents as $agent): ?>
                        <option value="<?= $agent['agent_id'] ?>"><?= htmlspecialchars($agent['agent_name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ:</label>
                    <input type="date" id="agent-start" class="form-control" value="<?= date('Y-m-01') ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ:</label>
                    <input type="date" id="agent-end" class="form-control" value="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search"></i> عرض التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- التقرير المخصص -->
        <div id="custom-settings" class="report-settings">
            <div class="row mb-3">
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير:</label>
                    <select id="custom-type" class="form-control">
                        <option value="summary">ملخص إجمالي</option>
                        <option value="detailed">تفصيلي</option>
                        <option value="comparison">مقارنة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ:</label>
                    <input type="date" id="custom-start" class="form-control" value="<?= date('Y-m-01') ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ:</label>
                    <input type="date" id="custom-end" class="form-control" value="<?= date('Y-m-d') ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search"></i> عرض التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- منطقة عرض التقرير -->
    <div id="report-display-area">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 id="report-title"><i class="fas fa-calendar-day"></i> التقرير اليومي</h5>
                <div>
                    <button class="btn btn-sm btn-light" onclick="printReport()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-sm btn-success" onclick="exportReport()">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="report-content">
                    <!-- محتوى التقرير سيظهر هنا -->
                    <div class="text-center text-muted">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <p>اختر نوع التقرير وحدد التاريخ ثم اضغط "عرض التقرير"</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentReportType = 'daily';

// إظهار نوع التقرير المحدد
function showReportType(reportType) {
    currentReportType = reportType;

    // إخفاء جميع إعدادات التقارير
    document.querySelectorAll('.report-settings').forEach(setting => {
        setting.classList.remove('active');
    });

    // إزالة الفئة النشطة من جميع الأيقونات الفرعية
    document.querySelectorAll('.sub-nav-icon').forEach(icon => {
        icon.classList.remove('active');
    });

    // إظهار إعدادات التقرير المحدد
    document.getElementById(reportType + '-settings').classList.add('active');

    // إضافة الفئة النشطة للأيقونة المحددة
    event.target.closest('.sub-nav-icon').classList.add('active');

    // تحديث عنوان التقرير
    const titles = {
        'daily': 'التقرير اليومي',
        'weekly': 'التقرير الأسبوعي',
        'monthly': 'التقرير الشهري',
        'agent': 'تقرير الوكيل',
        'custom': 'التقرير المخصص'
    };

    const icons = {
        'daily': 'fas fa-calendar-day',
        'weekly': 'fas fa-calendar-week',
        'monthly': 'fas fa-calendar-alt',
        'agent': 'fas fa-user-chart',
        'custom': 'fas fa-cogs'
    };

    document.getElementById('report-title').innerHTML = `<i class="${icons[reportType]}"></i> ${titles[reportType]}`;

    // مسح محتوى التقرير السابق
    document.getElementById('report-content').innerHTML = `
        <div class="text-center text-muted">
            <i class="${icons[reportType]} fa-3x mb-3"></i>
            <p>حدد ${titles[reportType]} ثم اضغط "عرض التقرير"</p>
        </div>
    `;
}

// إنشاء التقرير
function generateReport() {
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تحميل التقرير...</div>';

    let url = '';

    switch(currentReportType) {
        case 'daily':
            const date = document.getElementById('daily-date').value;
            url = `daily_report_ajax.php?date=${date}`;
            break;

        case 'weekly':
            const startDate = document.getElementById('weekly-start').value;
            const endDate = document.getElementById('weekly-end').value;
            url = `weekly_report_ajax.php?start=${startDate}&end=${endDate}`;
            break;

        case 'monthly':
            const monthDate = document.getElementById('monthly-date').value;
            url = `monthly_report_ajax.php?month=${monthDate}`;
            break;

        case 'agent':
            const agentId = document.getElementById('agent-select').value;
            const agentStart = document.getElementById('agent-start').value;
            const agentEnd = document.getElementById('agent-end').value;
            url = `agent_report_ajax.php?agent=${agentId}&start=${agentStart}&end=${agentEnd}`;
            break;

        case 'custom':
            const customType = document.getElementById('custom-type').value;
            const customStart = document.getElementById('custom-start').value;
            const customEnd = document.getElementById('custom-end').value;
            url = `custom_report_ajax.php?type=${customType}&start=${customStart}&end=${customEnd}`;
            break;
    }

    fetch(url)
        .then(response => response.text())
        .then(data => {
            reportContent.innerHTML = data;
        })
        .catch(error => {
            reportContent.innerHTML = '<div class="alert alert-danger">خطأ في تحميل التقرير</div>';
        });
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تصدير التقرير
function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// تحميل التقرير اليومي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    generateReport();
});
</script>
