@echo off
echo بدء تشغيل نظام إدارة تحصيلات الوكلاء...

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: PHP غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

REM التحقق من وجود MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: MySQL غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

REM بدء خدمة MySQL
net start MySQL80
echo ✓ تم بدء خدمة MySQL

REM بدء خدمة Apache (إذا كان مثبت كخدمة)
net start Apache2.4
echo ✓ تم بدء خدمة Apache

REM فتح المنفذ في جدار الحماية
netsh advfirewall firewall add rule name="Collections System Port 7445" dir=in action=allow protocol=TCP localport=7445
echo ✓ تم فتح المنفذ 7445 في جدار الحماية

echo.
echo النظام يعمل الآن على:
echo الرابط الداخلي: http://localhost:7445
echo الرابط الخارجي: http://***********:7445
echo.
echo للتوقف: اضغط أي مفتاح
pause

