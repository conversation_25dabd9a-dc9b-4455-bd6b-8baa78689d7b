<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير اليومي الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .report-table { font-size: 0.9em; }
        .total-row { background-color: #f8f9fa; font-weight: bold; }
        .print-btn { position: fixed; top: 20px; left: 20px; z-index: 1000; }
        @media print {
            .no-print { display: none !important; }
            .container-fluid { margin: 0; padding: 0; }
        }
    </style>
</head>
<body>

<?php
require_once 'config/database.php';

try {

    // التاريخ المحدد
    $selected_date = $_GET['date'] ?? date('Y-m-d');

    // جلب بيانات اليوم
    $day_stmt = $pdo->prepare("SELECT * FROM days WHERE date = ?");
    $day_stmt->execute([$selected_date]);
    $day_info = $day_stmt->fetch();

    // جلب جميع الوكلاء
    $agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll();

    // جلب بيانات البوابة
    $gateway_stmt = $pdo->prepare("
        SELECT g.*, a.agent_name
        FROM gateway g
        JOIN agents a ON g.agent_id = a.agent_id
        JOIN days d ON g.day_id = d.day_id
        WHERE d.date = ?
        ORDER BY a.agent_name
    ");
    $gateway_stmt->execute([$selected_date]);
    $gateway_data = $gateway_stmt->fetchAll();

    // جلب بيانات ريال موبايل
    $riyal_stmt = $pdo->prepare("
        SELECT r.*, a.agent_name
        FROM riyal_mobile r
        JOIN agents a ON r.agent_id = a.agent_id
        JOIN days d ON r.day_id = d.day_id
        WHERE d.date = ?
        ORDER BY a.agent_name
    ");
    $riyal_stmt->execute([$selected_date]);
    $riyal_data = $riyal_stmt->fetchAll();

    // تنظيم البيانات حسب الوكيل
    $gateway_by_agent = [];
    foreach ($gateway_data as $row) {
        $gateway_by_agent[$row['agent_id']] = $row;
    }

    $riyal_by_agent = [];
    foreach ($riyal_data as $row) {
        $riyal_by_agent[$row['agent_id']] = $row;
    }

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
}
?>

<div class="container-fluid mt-4">
    <!-- زر الطباعة -->
    <button onclick="window.print()" class="btn btn-primary print-btn no-print">
        <i class="fas fa-print"></i> طباعة
    </button>

    <!-- رأس التقرير -->
    <div class="row">
        <div class="col-12 text-center mb-4">
            <h1>تقرير التحصيلات اليومي</h1>
            <h3><?= $day_info ? $day_info['day_name'] . ' - ' . $day_info['date'] : $selected_date ?></h3>
            <p class="text-muted">تم إنشاؤه في: <?= date('Y-m-d H:i:s') ?></p>
        </div>
    </div>

    <!-- اختيار التاريخ -->
    <div class="row mb-4 no-print">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-calendar"></i> اختيار التاريخ</h5>
                </div>
                <div class="card-body">
                    <form method="GET">
                        <div class="mb-3">
                            <input type="date" name="date" class="form-control"
                                   value="<?= $selected_date ?>" onchange="this.form.submit()">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-chart-pie"></i> ملخص سريع</h5>
                </div>
                <div class="card-body">
                    <?php
                    $total_gateway = array_sum(array_column($gateway_data, 'amount'));
                    $total_riyal = array_sum(array_column($riyal_data, 'amount'));
                    $total_agents = count($agents);
                    $active_agents = count(array_unique(array_merge(
                        array_column($gateway_data, 'agent_id'),
                        array_column($riyal_data, 'agent_id')
                    )));
                    ?>

                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning"><?= number_format($total_gateway, 2) ?></h4>
                            <small>إجمالي البوابة</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-danger"><?= number_format($total_riyal, 2) ?></h4>
                            <small>إجمالي ريال موبايل</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success"><?= number_format($total_gateway + $total_riyal, 2) ?></h4>
                            <small>الإجمالي العام</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-info"><?= $active_agents ?>/<?= $total_agents ?></h4>
                            <small>الوكلاء النشطين</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول التقرير -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-table"></i> تفاصيل التحصيلات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped report-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>اسم الوكيل</th>
                                    <th>البوابة</th>
                                    <th>ريال موبايل</th>
                                    <th>الإجمالي</th>
                                    <th>الحالة</th>
                                    <th class="no-print">آخر تحديث</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $row_num = 1;
                                $total_gateway_sum = 0;
                                $total_riyal_sum = 0;

                                foreach ($agents as $agent):
                                    $gateway_amount = $gateway_by_agent[$agent['agent_id']]['amount'] ?? 0;
                                    $riyal_amount = $riyal_by_agent[$agent['agent_id']]['amount'] ?? 0;
                                    $total_amount = $gateway_amount + $riyal_amount;

                                    $total_gateway_sum += $gateway_amount;
                                    $total_riyal_sum += $riyal_amount;

                                    $has_data = $gateway_amount > 0 || $riyal_amount > 0;
                                ?>
                                <tr class="<?= $has_data ? '' : 'table-light' ?>">
                                    <td><?= $row_num++ ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($agent['agent_name']) ?></strong>
                                    </td>
                                    <td class="text-end">
                                        <?php if ($gateway_amount > 0): ?>
                                            <span class="text-warning fw-bold"><?= number_format($gateway_amount, 2) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">0.00</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <?php if ($riyal_amount > 0): ?>
                                            <span class="text-danger fw-bold"><?= number_format($riyal_amount, 2) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">0.00</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <?php if ($total_amount > 0): ?>
                                            <span class="text-success fw-bold"><?= number_format($total_amount, 2) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">0.00</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($has_data): ?>
                                            <span class="badge bg-success">مكتمل</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">لا توجد بيانات</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <small class="text-muted">
                                            <?php
                                            $last_update = '';
                                            if (isset($gateway_by_agent[$agent['agent_id']])) {
                                                $last_update = $gateway_by_agent[$agent['agent_id']]['updated_at'];
                                            }
                                            if (isset($riyal_by_agent[$agent['agent_id']])) {
                                                $riyal_update = $riyal_by_agent[$agent['agent_id']]['updated_at'];
                                                if (!$last_update || $riyal_update > $last_update) {
                                                    $last_update = $riyal_update;
                                                }
                                            }
                                            echo $last_update ? date('H:i', strtotime($last_update)) : '-';
                                            ?>
                                        </small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>

                                <!-- صف الإجمالي -->
                                <tr class="total-row">
                                    <td colspan="2"><strong>الإجمالي العام</strong></td>
                                    <td class="text-end"><strong><?= number_format($total_gateway_sum, 2) ?></strong></td>
                                    <td class="text-end"><strong><?= number_format($total_riyal_sum, 2) ?></strong></td>
                                    <td class="text-end"><strong><?= number_format($total_gateway_sum + $total_riyal_sum, 2) ?></strong></td>
                                    <td colspan="2"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mt-4 no-print">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-chart-bar"></i> إحصائيات البوابة</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>عدد الوكلاء النشطين:</strong> <?= count($gateway_data) ?></li>
                        <li><strong>إجمالي المبلغ:</strong> <?= number_format($total_gateway_sum, 2) ?></li>
                        <li><strong>متوسط المبلغ:</strong> <?= count($gateway_data) > 0 ? number_format($total_gateway_sum / count($gateway_data), 2) : '0.00' ?></li>
                        <li><strong>أعلى مبلغ:</strong> <?= count($gateway_data) > 0 ? number_format(max(array_column($gateway_data, 'amount')), 2) : '0.00' ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-mobile-alt"></i> إحصائيات ريال موبايل</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>عدد الوكلاء النشطين:</strong> <?= count($riyal_data) ?></li>
                        <li><strong>إجمالي المبلغ:</strong> <?= number_format($total_riyal_sum, 2) ?></li>
                        <li><strong>متوسط المبلغ:</strong> <?= count($riyal_data) > 0 ? number_format($total_riyal_sum / count($riyal_data), 2) : '0.00' ?></li>
                        <li><strong>أعلى مبلغ:</strong> <?= count($riyal_data) > 0 ? number_format(max(array_column($riyal_data, 'amount')), 2) : '0.00' ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="row mt-4 no-print">
        <div class="col-12 text-center">
            <a href="add_gateway_data.php?selected_date=<?= $selected_date ?>" class="btn btn-warning me-2">
                <i class="fas fa-door-open"></i> تعديل بيانات البوابة
            </a>
            <a href="add_riyal_data.php?selected_date=<?= $selected_date ?>" class="btn btn-danger me-2">
                <i class="fas fa-mobile-alt"></i> تعديل بيانات ريال موبايل
            </a>
            <a href="view_new_structure.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
            <button onclick="exportToExcel()" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </button>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    // يمكن إضافة تصدير Excel هنا
    alert('سيتم إضافة تصدير Excel قريباً');
}
</script>

</body>
</html>
