<?php
require_once 'config/database_sqlite.php';

// جلب الوكلاء
$agents_stmt = $pdo->query("SELECT agent_id, agent_name FROM agents ORDER BY agent_name");
$agents_list = $agents_stmt->fetchAll();
?>

<div class="container-fluid">
    <div class="row">
        <!-- استيراد البوابة -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6><i class="fas fa-building"></i> استيراد بيانات البوابة</h6>
                </div>
                <div class="card-body p-2">
                    <p class="small mb-2">ملف: database/import.csv</p>

                    <button class="btn btn-primary btn-sm" onclick="importCSV('gateway')" id="gateway-import-btn">
                        <i class="fas fa-upload"></i> استيراد البوابة
                    </button>

                    <div id="gateway-result" class="mt-2"></div>
                </div>
            </div>
        </div>

        <!-- استيراد ريال موبايل -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-mobile-alt"></i> استيراد بيانات ريال موبايل</h6>
                </div>
                <div class="card-body p-2">
                    <p class="small mb-2">ملف: database/ryal.csv</p>

                    <button class="btn btn-info btn-sm" onclick="importCSV('riyal')" id="riyal-import-btn">
                        <i class="fas fa-upload"></i> استيراد ريال موبايل
                    </button>

                    <div id="riyal-result" class="mt-2"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- حالة قاعدة البيانات -->
    <div class="row mt-2">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-database"></i> حالة قاعدة البيانات</h6>
                </div>
                <div class="card-body p-2">
                    <button class="btn btn-success btn-sm" onclick="checkDatabase()">
                        <i class="fas fa-search"></i> فحص البيانات
                    </button>
                    <div id="db-status" class="mt-2"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// استيراد CSV
function importCSV(type) {
    const resultDiv = type + '-result';
    const button = document.getElementById(type + '-import-btn');

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';

    showAlert(resultDiv, 'info', 'جاري معالجة البيانات...');

    fetch('process_csv_import.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'type=' + type
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let message = `✅ تم الاستيراد بنجاح!<br>`;
            message += `• الملف: ${data.file}<br>`;
            message += `• النوع: ${data.type}<br>`;
            message += `• سجلات جديدة: ${data.imported || 0}<br>`;
            message += `• سجلات محدثة: ${data.updated || 0}<br>`;
            if (data.errors > 0) {
                message += `• أخطاء متخطاة: ${data.errors}<br>`;
            }
            message += `• الإجمالي: ${data.total || 0} عملية`;

            showAlert(resultDiv, 'success', message);

            // تحديث التقارير إذا كانت مفتوحة
            if (typeof generateReport === 'function') {
                setTimeout(() => generateReport(), 1000);
            }
        } else {
            showAlert(resultDiv, 'danger', `❌ فشل الاستيراد: ${data.error}`);
        }
    })
    .catch(error => {
        showAlert(resultDiv, 'danger', `❌ خطأ في الشبكة: ${error.message}`);
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = type === 'gateway' ?
            '<i class="fas fa-upload"></i> استيراد البوابة' :
            '<i class="fas fa-upload"></i> استيراد ريال موبايل';
    });
}

// فحص قاعدة البيانات
function checkDatabase() {
    fetch('check_import_results.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let message = `📊 حالة قاعدة البيانات:<br>`;
            message += `• إجمالي السجلات: ${data.total_records}<br>`;
            message += `• سجلات البوابة: ${data.gateway_records}<br>`;
            message += `• سجلات ريال موبايل: ${data.riyal_records}<br>`;
            message += `• إجمالي البوابة: ${data.total_gateway}<br>`;
            message += `• إجمالي ريال موبايل: ${data.total_riyal}<br>`;
            message += `• آخر تحديث: ${data.last_update}`;

            showAlert('db-status', 'info', message);
        } else {
            showAlert('db-status', 'danger', `خطأ في فحص قاعدة البيانات: ${data.error}`);
        }
    })
    .catch(error => {
        showAlert('db-status', 'danger', `خطأ في الشبكة: ${error.message}`);
    });
}

// عرض التنبيهات
function showAlert(elementId, type, message) {
    const alertClass = {
        'success': 'alert-success',
        'danger': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };

    document.getElementById(elementId).innerHTML = `
        <div class="alert ${alertClass[type]} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

// فحص تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkDatabase();
});
</script>
