<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إضافة بيانات ريال موبايل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .agent-card { transition: all 0.3s; cursor: pointer; }
        .agent-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .amount-input { font-size: 1.2em; text-align: center; }
        .save-btn { position: fixed; bottom: 20px; right: 20px; z-index: 1000; }
    </style>
</head>
<body>

<?php
session_start();
try {
    $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معالجة حفظ البيانات
    if ($_POST['action'] ?? '' === 'save_riyal_data') {
        $selected_date = $_POST['selected_date'];
        $riyal_data = $_POST['riyal_data'] ?? [];
        $user_id = $_SESSION['user_id'] ?? 1;
        
        $saved_count = 0;
        $updated_count = 0;
        
        foreach ($riyal_data as $agent_id => $amount) {
            if ($amount > 0) {
                // البحث عن day_id
                $day_stmt = $pdo->prepare("SELECT day_id FROM days WHERE date = ?");
                $day_stmt->execute([$selected_date]);
                $day = $day_stmt->fetch();
                
                if (!$day) {
                    // إنشاء اليوم إذا لم يكن موجوداً
                    $day_name = date('l', strtotime($selected_date));
                    $month_name = date('F', strtotime($selected_date));
                    $year = date('Y', strtotime($selected_date));
                    
                    $day_names_ar = [
                        'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء', 'Wednesday' => 'الأربعاء',
                        'Thursday' => 'الخميس', 'Friday' => 'الجمعة', 'Saturday' => 'السبت', 'Sunday' => 'الأحد'
                    ];
                    $month_names_ar = [
                        'January' => 'يناير', 'February' => 'فبراير', 'March' => 'مارس',
                        'April' => 'أبريل', 'May' => 'مايو', 'June' => 'يونيو',
                        'July' => 'يوليو', 'August' => 'أغسطس', 'September' => 'سبتمبر',
                        'October' => 'أكتوبر', 'November' => 'نوفمبر', 'December' => 'ديسمبر'
                    ];
                    
                    $insert_day = $pdo->prepare("INSERT INTO days (date, day_name, month_name, year) VALUES (?, ?, ?, ?)");
                    $insert_day->execute([$selected_date, $day_names_ar[$day_name], $month_names_ar[$month_name], $year]);
                    $day_id = $pdo->lastInsertId();
                } else {
                    $day_id = $day['day_id'];
                }
                
                // التحقق من وجود السجل
                $check_stmt = $pdo->prepare("SELECT riyal_id FROM riyal_mobile WHERE agent_id = ? AND day_id = ?");
                $check_stmt->execute([$agent_id, $day_id]);
                $existing = $check_stmt->fetch();
                
                if ($existing) {
                    // تحديث السجل الموجود
                    $update_stmt = $pdo->prepare("
                        UPDATE riyal_mobile SET 
                            amount = ?, 
                            updated_at = NOW(), 
                            updated_by = ?
                        WHERE riyal_id = ?
                    ");
                    $update_stmt->execute([$amount, $user_id, $existing['riyal_id']]);
                    $updated_count++;
                } else {
                    // إنشاء سجل جديد
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO riyal_mobile (agent_id, day_id, amount, created_by) 
                        VALUES (?, ?, ?, ?)
                    ");
                    $insert_stmt->execute([$agent_id, $day_id, $amount, $user_id]);
                    $saved_count++;
                }
            }
        }
        
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ تم حفظ البيانات بنجاح!</h5>";
        echo "<p>تم إنشاء $saved_count سجل جديد وتحديث $updated_count سجل موجود</p>";
        echo "</div>";
    }
    
    // جلب الوكلاء
    $agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll();
    
    // التاريخ المحدد
    $selected_date = $_POST['selected_date'] ?? date('Y-m-d');
    
    // جلب البيانات الموجودة
    $existing_data = [];
    $existing_stmt = $pdo->prepare("
        SELECT r.agent_id, r.amount 
        FROM riyal_mobile r
        JOIN days d ON r.day_id = d.day_id
        WHERE d.date = ?
    ");
    $existing_stmt->execute([$selected_date]);
    $existing_results = $existing_stmt->fetchAll();
    
    foreach ($existing_results as $row) {
        $existing_data[$row['agent_id']] = $row['amount'];
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
}
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger">
                <h2><i class="fas fa-mobile-alt"></i> إضافة بيانات ريال موبايل</h2>
                <p>إدخال مبالغ ريال موبايل لجميع الوكلاء</p>
            </div>
        </div>
    </div>

    <form method="POST" id="riyalForm">
        <input type="hidden" name="action" value="save_riyal_data">
        
        <!-- اختيار التاريخ -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-calendar"></i> اختيار التاريخ</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">التاريخ:</label>
                            <input type="date" name="selected_date" class="form-control" 
                                   value="<?= $selected_date ?>" onchange="this.form.submit()">
                        </div>
                        
                        <div class="alert alert-info">
                            <small>
                                <strong>التاريخ المحدد:</strong><br>
                                <?= date('l, d F Y', strtotime($selected_date)) ?>
                            </small>
                        </div>
                        
                        <!-- مقارنة مع البوابة -->
                        <?php
                        $gateway_stmt = $pdo->prepare("
                            SELECT COUNT(*) as count, SUM(g.amount) as total
                            FROM gateway g
                            JOIN days d ON g.day_id = d.day_id
                            WHERE d.date = ?
                        ");
                        $gateway_stmt->execute([$selected_date]);
                        $gateway_stats = $gateway_stmt->fetch();
                        ?>
                        
                        <div class="alert alert-warning">
                            <small>
                                <strong>بيانات البوابة لنفس اليوم:</strong><br>
                                الوكلاء: <?= $gateway_stats['count'] ?><br>
                                المبلغ: <?= number_format($gateway_stats['total'] ?? 0, 2) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات ريال موبايل</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="total-agents"><?= count($agents) ?></h4>
                                    <small>إجمالي الوكلاء</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="filled-agents"><?= count($existing_data) ?></h4>
                                    <small>تم إدخال بياناتهم</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="total-amount"><?= number_format(array_sum($existing_data), 2) ?></h4>
                                    <small>إجمالي المبلغ</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="remaining-agents"><?= count($agents) - count($existing_data) ?></h4>
                                    <small>متبقي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات الوكلاء -->
        <div class="row">
            <?php foreach ($agents as $agent): ?>
                <div class="col-md-3 mb-3">
                    <div class="card agent-card <?= isset($existing_data[$agent['agent_id']]) ? 'border-danger' : 'border-secondary' ?>">
                        <div class="card-header <?= isset($existing_data[$agent['agent_id']]) ? 'bg-danger text-white' : 'bg-light' ?>">
                            <h6 class="mb-0">
                                <i class="fas fa-mobile-alt"></i>
                                <?= htmlspecialchars($agent['agent_name']) ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <label class="form-label">المبلغ:</label>
                                <input type="number" 
                                       name="riyal_data[<?= $agent['agent_id'] ?>]" 
                                       class="form-control amount-input" 
                                       step="0.01" 
                                       min="0"
                                       value="<?= $existing_data[$agent['agent_id']] ?? '' ?>"
                                       placeholder="0.00"
                                       onchange="updateStats()">
                            </div>
                            
                            <?php if (isset($existing_data[$agent['agent_id']])): ?>
                                <div class="alert alert-success alert-sm">
                                    <small><i class="fas fa-check"></i> تم الحفظ مسبقاً</small>
                                </div>
                            <?php endif; ?>
                            
                            <!-- عرض بيانات البوابة للمقارنة -->
                            <?php
                            $gateway_amount_stmt = $pdo->prepare("
                                SELECT g.amount 
                                FROM gateway g
                                JOIN days d ON g.day_id = d.day_id
                                WHERE g.agent_id = ? AND d.date = ?
                            ");
                            $gateway_amount_stmt->execute([$agent['agent_id'], $selected_date]);
                            $gateway_amount = $gateway_amount_stmt->fetch();
                            
                            if ($gateway_amount):
                            ?>
                                <div class="alert alert-info alert-sm">
                                    <small>
                                        <i class="fas fa-door-open"></i> البوابة: 
                                        <?= number_format($gateway_amount['amount'], 2) ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- أزرار التحكم -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-danger btn-lg me-3">
                            <i class="fas fa-save"></i> حفظ جميع البيانات
                        </button>
                        
                        <button type="button" class="btn btn-warning me-3" onclick="clearAll()">
                            <i class="fas fa-eraser"></i> مسح الكل
                        </button>
                        
                        <button type="button" class="btn btn-info me-3" onclick="fillSample()">
                            <i class="fas fa-magic"></i> بيانات تجريبية
                        </button>
                        
                        <button type="button" class="btn btn-secondary me-3" onclick="copyFromGateway()">
                            <i class="fas fa-copy"></i> نسخ من البوابة
                        </button>
                        
                        <a href="view_new_structure.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- زر الحفظ السريع -->
<button type="button" class="btn btn-danger btn-lg save-btn" onclick="document.getElementById('riyalForm').submit()">
    <i class="fas fa-save"></i>
</button>

<script>
function updateStats() {
    let totalAgents = <?= count($agents) ?>;
    let filledAgents = 0;
    let totalAmount = 0;
    
    document.querySelectorAll('.amount-input').forEach(input => {
        if (input.value && parseFloat(input.value) > 0) {
            filledAgents++;
            totalAmount += parseFloat(input.value);
        }
    });
    
    document.getElementById('filled-agents').textContent = filledAgents;
    document.getElementById('total-amount').textContent = totalAmount.toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    document.getElementById('remaining-agents').textContent = totalAgents - filledAgents;
}

function clearAll() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        document.querySelectorAll('.amount-input').forEach(input => {
            input.value = '';
        });
        updateStats();
    }
}

function fillSample() {
    if (confirm('هل تريد ملء البيانات التجريبية؟')) {
        document.querySelectorAll('.amount-input').forEach((input, index) => {
            input.value = (Math.random() * 8000 + 500).toFixed(2);
        });
        updateStats();
    }
}

function copyFromGateway() {
    if (confirm('هل تريد نسخ البيانات من البوابة؟')) {
        // البحث عن بيانات البوابة وملء الحقول
        document.querySelectorAll('.alert-info').forEach(alert => {
            const text = alert.textContent;
            const match = text.match(/البوابة:\s*([\d,]+\.?\d*)/);
            if (match) {
                const amount = match[1].replace(/,/g, '');
                const card = alert.closest('.card');
                const input = card.querySelector('.amount-input');
                if (input) {
                    input.value = amount;
                }
            }
        });
        updateStats();
    }
}

// تحديث الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateStats);
</script>

</body>
</html>
