@echo off
echo ========================================
echo    تشغيل خادم Node.js + PHP
echo    المنفذ: 7445
echo ========================================
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP وإضافته إلى PATH
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo ✅ PHP متاح
echo.

REM تثبيت المكتبات إذا لم تكن موجودة
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install
    echo.
)

REM فتح المنفذ في جدار الحماية
netsh advfirewall firewall add rule name="Node.js Server Port 7445" dir=in action=allow protocol=TCP localport=7445 >nul 2>&1
echo ✅ تم فتح المنفذ 7445 في جدار الحماية

echo.
echo 🚀 بدء تشغيل الخادم...
echo 🌐 الرابط المحلي: http://localhost:7445
echo 🌍 الرابط الخارجي: http://0.0.0.0:7445
echo 🛑 اضغط Ctrl+C لإيقاف الخادم
echo.

REM بدء الخادم
node server.js

echo.
echo تم إيقاف الخادم
pause
