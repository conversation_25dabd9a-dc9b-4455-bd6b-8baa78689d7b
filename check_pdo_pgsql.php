<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>فحص وتفعيل امتداد PostgreSQL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-cogs"></i> فحص وتفعيل امتداد PostgreSQL</h2>
        <p>أداة شاملة لفحص وتوجيه عملية تفعيل امتداد pdo_pgsql</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>🔍 حالة الامتدادات</h5>
                </div>
                <div class="card-body">
                    <?php
                    echo "<h6>فحص امتدادات PostgreSQL:</h6>";
                    
                    // فحص امتداد pdo_pgsql
                    if (extension_loaded('pdo_pgsql')) {
                        echo "<p class='text-success'><i class='fas fa-check'></i> <strong>pdo_pgsql:</strong> متاح ✅</p>";
                        $pdo_pgsql_available = true;
                    } else {
                        echo "<p class='text-danger'><i class='fas fa-times'></i> <strong>pdo_pgsql:</strong> غير متاح ❌</p>";
                        $pdo_pgsql_available = false;
                    }
                    
                    // فحص امتداد pgsql العادي
                    if (extension_loaded('pgsql')) {
                        echo "<p class='text-success'><i class='fas fa-check'></i> <strong>pgsql:</strong> متاح ✅</p>";
                    } else {
                        echo "<p class='text-warning'><i class='fas fa-exclamation'></i> <strong>pgsql:</strong> غير متاح ⚠️</p>";
                    }
                    
                    // فحص PDO العام
                    if (extension_loaded('pdo')) {
                        echo "<p class='text-success'><i class='fas fa-check'></i> <strong>PDO:</strong> متاح ✅</p>";
                        
                        // عرض drivers المتاحة
                        $drivers = PDO::getAvailableDrivers();
                        echo "<p><strong>PDO Drivers المتاحة:</strong></p>";
                        echo "<ul>";
                        foreach ($drivers as $driver) {
                            if ($driver === 'pgsql') {
                                echo "<li class='text-success'><strong>$driver</strong> ✅</li>";
                            } else {
                                echo "<li>$driver</li>";
                            }
                        }
                        echo "</ul>";
                    } else {
                        echo "<p class='text-danger'><i class='fas fa-times'></i> <strong>PDO:</strong> غير متاح ❌</p>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5>📋 معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <?php
                    echo "<h6>معلومات PHP:</h6>";
                    echo "<ul>";
                    echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
                    echo "<li><strong>نظام التشغيل:</strong> " . PHP_OS . "</li>";
                    echo "<li><strong>معمارية النظام:</strong> " . php_uname('m') . "</li>";
                    
                    // تحديد نوع الخادم
                    $server_software = $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد';
                    echo "<li><strong>خادم الويب:</strong> $server_software</li>";
                    
                    // تحديد مسار php.ini
                    $php_ini_path = php_ini_loaded_file();
                    echo "<li><strong>ملف php.ini:</strong> " . ($php_ini_path ?: 'غير محدد') . "</li>";
                    
                    // تحديد مجلد الامتدادات
                    $extension_dir = ini_get('extension_dir');
                    echo "<li><strong>مجلد الامتدادات:</strong> $extension_dir</li>";
                    echo "</ul>";
                    
                    // تحديد نوع التثبيت
                    echo "<h6>نوع التثبيت المحتمل:</h6>";
                    if (strpos(strtolower($php_ini_path), 'xampp') !== false) {
                        echo "<p class='text-info'><i class='fas fa-server'></i> <strong>XAMPP</strong> مكتشف</p>";
                        $installation_type = 'xampp';
                    } elseif (strpos(strtolower($php_ini_path), 'wamp') !== false) {
                        echo "<p class='text-info'><i class='fas fa-server'></i> <strong>WAMP</strong> مكتشف</p>";
                        $installation_type = 'wamp';
                    } elseif (PHP_OS_FAMILY === 'Windows') {
                        echo "<p class='text-info'><i class='fab fa-windows'></i> <strong>Windows</strong> - تثبيت منفصل</p>";
                        $installation_type = 'windows';
                    } elseif (PHP_OS_FAMILY === 'Linux') {
                        echo "<p class='text-info'><i class='fab fa-linux'></i> <strong>Linux</strong></p>";
                        $installation_type = 'linux';
                    } else {
                        echo "<p class='text-muted'><i class='fas fa-question'></i> <strong>غير محدد</strong></p>";
                        $installation_type = 'unknown';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (!$pdo_pgsql_available): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> امتداد pdo_pgsql غير مفعل</h5>
                <p>يجب تفعيل هذا الامتداد للاتصال بقاعدة بيانات PostgreSQL.</p>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5>🛠️ خطوات التفعيل</h5>
                </div>
                <div class="card-body">
                    <?php
                    switch ($installation_type) {
                        case 'xampp':
                            echo "<h6>خطوات التفعيل في XAMPP:</h6>";
                            echo "<ol>";
                            echo "<li>افتح <strong>XAMPP Control Panel</strong></li>";
                            echo "<li>اضغط <strong>Config</strong> بجانب Apache</li>";
                            echo "<li>اختر <strong>PHP (php.ini)</strong></li>";
                            echo "<li>ابحث عن السطر: <code>;extension=pdo_pgsql</code></li>";
                            echo "<li>احذف علامة <code>;</code> لتصبح: <code>extension=pdo_pgsql</code></li>";
                            echo "<li>احفظ الملف</li>";
                            echo "<li>أعد تشغيل Apache في XAMPP</li>";
                            echo "</ol>";
                            
                            if ($php_ini_path) {
                                echo "<div class='alert alert-info'>";
                                echo "<h6>مسار ملف php.ini:</h6>";
                                echo "<code>$php_ini_path</code>";
                                echo "</div>";
                            }
                            break;
                            
                        case 'wamp':
                            echo "<h6>خطوات التفعيل في WAMP:</h6>";
                            echo "<ol>";
                            echo "<li>اضغط على أيقونة <strong>WAMP</strong> في شريط المهام</li>";
                            echo "<li>اختر: <strong>PHP</strong> → <strong>PHP Extensions</strong></li>";
                            echo "<li>ابحث عن <strong>pdo_pgsql</strong></li>";
                            echo "<li>اضغط عليه لتفعيله</li>";
                            echo "<li>أعد تشغيل الخدمات</li>";
                            echo "</ol>";
                            break;
                            
                        case 'windows':
                            echo "<h6>خطوات التفعيل في Windows:</h6>";
                            echo "<ol>";
                            echo "<li>افتح ملف <code>php.ini</code> في محرر نصوص</li>";
                            echo "<li>ابحث عن قسم <code>[Extensions]</code></li>";
                            echo "<li>أضف أو فعّل: <code>extension=pdo_pgsql</code></li>";
                            echo "<li>تأكد من وجود ملف <code>php_pdo_pgsql.dll</code> في مجلد الامتدادات</li>";
                            echo "<li>أعد تشغيل خادم الويب</li>";
                            echo "</ol>";
                            break;
                            
                        case 'linux':
                            echo "<h6>خطوات التفعيل في Linux:</h6>";
                            echo "<div class='alert alert-secondary'>";
                            echo "<h6>Ubuntu/Debian:</h6>";
                            echo "<pre><code>sudo apt update\nsudo apt install php-pgsql\nsudo systemctl restart apache2</code></pre>";
                            echo "</div>";
                            echo "<div class='alert alert-secondary'>";
                            echo "<h6>CentOS/RHEL:</h6>";
                            echo "<pre><code>sudo yum install php-pgsql\nsudo systemctl restart httpd</code></pre>";
                            echo "</div>";
                            break;
                            
                        default:
                            echo "<h6>خطوات عامة:</h6>";
                            echo "<ol>";
                            echo "<li>حدد مسار ملف <code>php.ini</code></li>";
                            echo "<li>افتح الملف في محرر نصوص</li>";
                            echo "<li>ابحث عن <code>extension=pdo_pgsql</code></li>";
                            echo "<li>تأكد من عدم وجود <code>;</code> في بداية السطر</li>";
                            echo "<li>احفظ الملف وأعد تشغيل الخادم</li>";
                            echo "</ol>";
                    }
                    ?>
                    
                    <div class="mt-3">
                        <button onclick="location.reload()" class="btn btn-primary">
                            <i class="fas fa-sync"></i> إعادة فحص الامتدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle"></i> امتداد PostgreSQL مفعل بنجاح!</h5>
                <p>يمكنك الآن استخدام PostgreSQL مع PHP.</p>
                <div class="mt-3">
                    <a href="setup_postgresql.php" class="btn btn-success">
                        <i class="fas fa-database"></i> إعداد PostgreSQL
                    </a>
                    <a href="main.php" class="btn btn-secondary">
                        <i class="fas fa-home"></i> النظام الرئيسي
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5>💡 نصائح إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>مشاكل شائعة:</h6>
                            <ul>
                                <li><strong>الامتداد غير موجود:</strong> تأكد من تثبيت PostgreSQL</li>
                                <li><strong>ملف DLL مفقود:</strong> أعد تثبيت PHP أو XAMPP</li>
                                <li><strong>خطأ في التحميل:</strong> تحقق من مسار الامتدادات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>بدائل أخرى:</h6>
                            <ul>
                                <li>استخدام MySQL بدلاً من PostgreSQL</li>
                                <li>البقاء مع SQLite (يعمل بدون امتدادات)</li>
                                <li>استخدام خادم ويب مختلف</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
