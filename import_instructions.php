<?php
// تحديد نوع الاستيراد
$import_type = $_GET['type'] ?? 'gateway';
$page_title = $import_type == 'riyal' ? 'تعليمات استيراد بيانات ريال موبايل' : 'تعليمات استيراد بيانات البوابة';
$excel_file = $import_type == 'riyal' ? 'E:\agent\database\ryal.xlsx' : 'E:\agent\database\import.xlsx';
$csv_file = $import_type == 'riyal' ? 'database/ryal.csv' : 'database/import.csv';
$column_name = $import_type == 'riyal' ? 'ريال موبايل' : 'البوابة';

echo "<h2>$page_title</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>الخطوات المطلوبة:</h3>";
echo "<ol>";
echo "<li><strong>فتح ملف Excel:</strong> افتح الملف الموجود في: <code>$excel_file</code></li>";
echo "<li><strong>تحويل إلى CSV:</strong> في Excel، اختر File > Save As > CSV (Comma delimited)</li>";
echo "<li><strong>حفظ الملف:</strong> احفظ الملف باسم <code>import.csv</code> في مجلد <code>database</code></li>";
echo "<li><strong>تشغيل الاستيراد:</strong> اضغط على الزر أدناه لبدء الاستيراد</li>";
echo "</ol>";
echo "</div>";

// التحقق من وجود ملف CSV
if (file_exists($csv_file)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 20px 0;'>";
    echo "<h4>✅ تم العثور على ملف CSV!</h4>";
    echo "<p>الملف جاهز للاستيراد: <code>$csv_file</code></p>";
    echo "<p>حجم الملف: " . number_format(filesize($csv_file)) . " بايت</p>";
    echo "<p>تاريخ التعديل: " . date('Y-m-d H:i:s', filemtime($csv_file)) . "</p>";
    echo "</div>";

    echo "<a href='process_csv_import.php?type=$import_type' class='btn btn-success' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;'>🚀 بدء استيراد بيانات $column_name</a>";

    // معاينة الملف
    echo "<h3>معاينة البيانات:</h3>";
    $handle = fopen($csv_file, 'r');
    if ($handle) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        $row_count = 0;
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE && $row_count < 10) {
            echo "<tr>";
            foreach ($data as $cell) {
                $style = $row_count == 0 ? 'background: #f8f9fa; font-weight: bold;' : '';
                echo "<td style='padding: 8px; border: 1px solid #ddd; $style'>" . htmlspecialchars($cell) . "</td>";
            }
            echo "</tr>";
            $row_count++;
        }
        echo "</table>";
        fclose($handle);

        if ($row_count >= 10) {
            echo "<p><em>عرض أول 10 صفوف فقط...</em></p>";
        }
    }

} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404; margin: 20px 0;'>";
    echo "<h4>⚠️ ملف CSV غير موجود</h4>";
    echo "<p>يرجى اتباع الخطوات أعلاه لتحويل ملف Excel إلى CSV</p>";
    echo "<p>المسار المطلوب: <code>$csv_file</code></p>";
    echo "</div>";

    // محاولة تحويل تلقائي باستخدام PowerShell
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>محاولة التحويل التلقائي</h4>";
    echo "<p>يمكن محاولة التحويل التلقائي باستخدام PowerShell:</p>";
    echo "<button onclick='autoConvert()' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🔄 محاولة التحويل التلقائي</button>";
    echo "<div id='convert-result' style='margin-top: 10px;'></div>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>معلومات إضافية:</h3>";
echo "<ul>";
echo "<li><strong>تنسيق البيانات المتوقع:</strong> العمود الأول يحتوي على أسماء الوكلاء، والأعمدة التالية تحتوي على مبالغ $column_name لتواريخ مختلفة</li>";
echo "<li><strong>الوكلاء المدعومون:</strong> المامون، الشرعبي، عومان، الكون، سكافا، أبو اسامه، أسس، الاثير، المترب، باتكو، بران</li>";
echo "<li><strong>نوع البيانات:</strong> سيتم إدراج البيانات في عمود '$column_name' فقط</li>";
echo "</ul>";

echo "<p><a href='main.php'>العودة للصفحة الرئيسية</a></p>";
?>

<script>
function autoConvert() {
    const resultDiv = document.getElementById('convert-result');
    resultDiv.innerHTML = '<p style="color: #007bff;">جاري المحاولة...</p>';

    fetch('auto_convert_excel.php?type=<?= $import_type ?>')
        .then(response => response.text())
        .then(data => {
            resultDiv.innerHTML = data;
            // إعادة تحميل الصفحة بعد 3 ثوان إذا نجح التحويل
            if (data.includes('نجح')) {
                setTimeout(() => {
                    location.reload();
                }, 3000);
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style="color: red;">خطأ في التحويل: ' + error + '</p>';
        });
}
</script>
