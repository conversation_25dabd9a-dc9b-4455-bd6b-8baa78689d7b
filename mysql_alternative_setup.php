<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إعداد MySQL بدون phpMyAdmin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-database"></i> إعداد MySQL بدون phpMyAdmin</h2>
        <p>إنشاء قاعدة بيانات MySQL مباشرة من خلال PHP</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>🔍 فحص MySQL</h5>
                </div>
                <div class="card-body">
                    <?php
                    echo "<h6>1. فحص اتصال MySQL:</h6>";
                    
                    // فحص MySQL على منافذ مختلفة
                    $mysql_ports = [3306, 3307, 33060];
                    $mysql_working = false;
                    $working_port = null;
                    
                    foreach ($mysql_ports as $port) {
                        $connection = @fsockopen('localhost', $port, $errno, $errstr, 2);
                        if ($connection) {
                            echo "<p class='text-success'><i class='fas fa-check'></i> MySQL يعمل على المنفذ $port</p>";
                            $mysql_working = true;
                            $working_port = $port;
                            fclose($connection);
                            break;
                        }
                    }
                    
                    if (!$mysql_working) {
                        echo "<p class='text-danger'><i class='fas fa-times'></i> MySQL لا يعمل على أي منفذ</p>";
                        echo "<div class='alert alert-warning'>";
                        echo "<h6>⚠️ MySQL غير مشغل</h6>";
                        echo "<p>يرجى تشغيل MySQL من خلال:</p>";
                        echo "<ul>";
                        echo "<li>XAMPP Control Panel</li>";
                        echo "<li>WAMP Server</li>";
                        echo "<li>MySQL Service في Windows</li>";
                        echo "</ul>";
                        echo "</div>";
                    } else {
                        echo "<h6>2. اختبار الاتصال بقاعدة البيانات:</h6>";
                        
                        // اختبار اتصال PDO
                        try {
                            $test_pdo = new PDO("mysql:host=localhost;port=$working_port", 'root', '');
                            $test_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            echo "<p class='text-success'><i class='fas fa-check'></i> اتصال PDO ناجح</p>";
                            
                            // عرض معلومات MySQL
                            $version = $test_pdo->query("SELECT VERSION() as version")->fetch()['version'];
                            echo "<p><strong>إصدار MySQL:</strong> $version</p>";
                            
                            // عرض قواعد البيانات الموجودة
                            $databases = $test_pdo->query("SHOW DATABASES")->fetchAll();
                            echo "<h6>قواعد البيانات الموجودة:</h6>";
                            echo "<ul>";
                            foreach ($databases as $db) {
                                $db_name = $db['Database'];
                                if ($db_name === 'collections_system') {
                                    echo "<li class='text-success'><strong>$db_name</strong> (موجودة)</li>";
                                } else {
                                    echo "<li>$db_name</li>";
                                }
                            }
                            echo "</ul>";
                            
                        } catch (PDOException $e) {
                            echo "<p class='text-danger'><i class='fas fa-times'></i> فشل اتصال PDO: " . $e->getMessage() . "</p>";
                            
                            if (strpos($e->getMessage(), 'Access denied') !== false) {
                                echo "<div class='alert alert-warning'>";
                                echo "<h6>مشكلة في كلمة المرور</h6>";
                                echo "<p>جرب كلمات مرور مختلفة:</p>";
                                echo "<button onclick='testPasswords()' class='btn btn-warning btn-sm'>اختبار كلمات مرور مختلفة</button>";
                                echo "<div id='password-test-result'></div>";
                                echo "</div>";
                            }
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>🚀 إنشاء قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <?php if ($mysql_working): ?>
                    <div class="alert alert-info">
                        <h6>MySQL جاهز للاستخدام!</h6>
                        <p>يمكنك الآن إنشاء قاعدة البيانات مباشرة.</p>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button onclick="createDatabase()" class="btn btn-success">
                            <i class="fas fa-database"></i> إنشاء قاعدة البيانات
                        </button>
                        
                        <button onclick="createWithSampleData()" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إنشاء مع بيانات تجريبية
                        </button>
                        
                        <button onclick="importFromSQLite()" class="btn btn-warning">
                            <i class="fas fa-exchange-alt"></i> نقل من SQLite
                        </button>
                    </div>
                    
                    <div id="creation-result" class="mt-3"></div>
                    
                    <?php else: ?>
                    <div class="alert alert-danger">
                        <h6>MySQL غير متاح</h6>
                        <p>يرجى تشغيل MySQL أولاً.</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6>بدائل أخرى:</h6>
                        <ul>
                            <li><a href="main.php">استخدام SQLite (الحالي)</a></li>
                            <li>تشغيل XAMPP وإعادة المحاولة</li>
                            <li>استخدام MySQL Workbench</li>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5>🛠️ أدوات إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>MySQL Command Line:</h6>
                            <div class="alert alert-secondary">
                                <small>
                                    <code>mysql -u root -p</code><br>
                                    <code>CREATE DATABASE collections_system;</code>
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <h6>MySQL Workbench:</h6>
                            <p>برنامج مجاني من Oracle لإدارة MySQL</p>
                            <a href="https://dev.mysql.com/downloads/workbench/" target="_blank" class="btn btn-outline-primary btn-sm">تحميل</a>
                        </div>
                        
                        <div class="col-md-3">
                            <h6>HeidiSQL:</h6>
                            <p>برنامج مجاني لإدارة قواعد البيانات</p>
                            <a href="https://www.heidisql.com/" target="_blank" class="btn btn-outline-info btn-sm">تحميل</a>
                        </div>
                        
                        <div class="col-md-3">
                            <h6>Adminer:</h6>
                            <p>بديل خفيف لـ phpMyAdmin</p>
                            <button onclick="downloadAdminer()" class="btn btn-outline-success btn-sm">تحميل Adminer</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <a href="main.php" class="btn btn-secondary">العودة للنظام الحالي (SQLite)</a>
        <a href="mysql_setup_guide.html" class="btn btn-info">دليل الإعداد</a>
    </div>
</div>

<script>
function testPasswords() {
    document.getElementById('password-test-result').innerHTML = '<p>🔄 جاري اختبار كلمات المرور...</p>';
    
    fetch('test_mysql_passwords.php')
    .then(response => response.text())
    .then(data => {
        document.getElementById('password-test-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('password-test-result').innerHTML = '<p class="text-danger">خطأ: ' + error.message + '</p>';
    });
}

function createDatabase() {
    document.getElementById('creation-result').innerHTML = '<div class="alert alert-info">🔄 جاري إنشاء قاعدة البيانات...</div>';
    
    fetch('create_mysql_database.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=create_only'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('creation-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('creation-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}

function createWithSampleData() {
    document.getElementById('creation-result').innerHTML = '<div class="alert alert-info">🔄 جاري إنشاء قاعدة البيانات مع البيانات التجريبية...</div>';
    
    fetch('create_mysql_database.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=create_with_data'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('creation-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('creation-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}

function importFromSQLite() {
    document.getElementById('creation-result').innerHTML = '<div class="alert alert-info">🔄 جاري نقل البيانات من SQLite...</div>';
    
    fetch('create_mysql_database.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=import_sqlite'
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById('creation-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('creation-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}

function downloadAdminer() {
    window.open('https://www.adminer.org/latest.php', '_blank');
}
</script>

</body>
</html>
