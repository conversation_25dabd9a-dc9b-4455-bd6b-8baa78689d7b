<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إنشاء هيكل قاعدة البيانات الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-database"></i> إنشاء هيكل قاعدة البيانات الجديد</h2>
        <p>إنشاء الجداول الجديدة: الأيام، البوابة، ريال موبايل، وتحديث جدول المستخدمين</p>
    </div>

    <?php
    try {
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ تم الاتصال بقاعدة البيانات MySQL</h5>";
        echo "</div>";
        
        // 1. إنشاء جدول الأيام
        echo "<div class='card mb-3'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h5>📅 إنشاء جدول الأيام</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `days` (
                `day_id` INT AUTO_INCREMENT PRIMARY KEY,
                `date` DATE NOT NULL UNIQUE,
                `day_name` VARCHAR(20) NOT NULL,
                `month_name` VARCHAR(20) NOT NULL,
                `year` INT NOT NULL,
                `is_weekend` BOOLEAN DEFAULT FALSE,
                `is_holiday` BOOLEAN DEFAULT FALSE,
                `notes` TEXT NULL,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX `idx_date` (`date`),
                INDEX `idx_year_month` (`year`, `month_name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p>✅ تم إنشاء جدول الأيام</p>";
        
        // 2. إنشاء جدول البوابة
        echo "<h5>🚪 إنشاء جدول البوابة</h5>";
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `gateway` (
                `gateway_id` INT AUTO_INCREMENT PRIMARY KEY,
                `agent_id` INT NOT NULL,
                `day_id` INT NOT NULL,
                `amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
                `transaction_count` INT DEFAULT 0,
                `notes` TEXT NULL,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `created_by` INT NULL,
                `updated_by` INT NULL,
                INDEX `idx_agent_day` (`agent_id`, `day_id`),
                INDEX `idx_day` (`day_id`),
                INDEX `idx_amount` (`amount`),
                UNIQUE KEY `unique_agent_day` (`agent_id`, `day_id`),
                FOREIGN KEY (`agent_id`) REFERENCES `agents`(`agent_id`) ON DELETE CASCADE,
                FOREIGN KEY (`day_id`) REFERENCES `days`(`day_id`) ON DELETE CASCADE,
                FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
                FOREIGN KEY (`updated_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p>✅ تم إنشاء جدول البوابة</p>";
        
        // 3. إنشاء جدول ريال موبايل
        echo "<h5>📱 إنشاء جدول ريال موبايل</h5>";
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `riyal_mobile` (
                `riyal_id` INT AUTO_INCREMENT PRIMARY KEY,
                `agent_id` INT NOT NULL,
                `day_id` INT NOT NULL,
                `amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
                `transaction_count` INT DEFAULT 0,
                `notes` TEXT NULL,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `created_by` INT NULL,
                `updated_by` INT NULL,
                INDEX `idx_agent_day` (`agent_id`, `day_id`),
                INDEX `idx_day` (`day_id`),
                INDEX `idx_amount` (`amount`),
                UNIQUE KEY `unique_agent_day` (`agent_id`, `day_id`),
                FOREIGN KEY (`agent_id`) REFERENCES `agents`(`agent_id`) ON DELETE CASCADE,
                FOREIGN KEY (`day_id`) REFERENCES `days`(`day_id`) ON DELETE CASCADE,
                FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
                FOREIGN KEY (`updated_by`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p>✅ تم إنشاء جدول ريال موبايل</p>";
        
        echo "</div>";
        echo "</div>";
        
        // 4. تحديث جدول المستخدمين
        echo "<div class='card mb-3'>";
        echo "<div class='card-header bg-warning text-dark'>";
        echo "<h5>👥 تحديث جدول المستخدمين</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        // إضافة أعمدة جديدة لجدول المستخدمين
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `department` VARCHAR(100) NULL AFTER `employee_name`");
            echo "<p>✅ تم إضافة عمود القسم</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ عمود القسم موجود مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `phone` VARCHAR(20) NULL AFTER `department`");
            echo "<p>✅ تم إضافة عمود الهاتف</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ عمود الهاتف موجود مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `email` VARCHAR(255) NULL AFTER `phone`");
            echo "<p>✅ تم إضافة عمود البريد الإلكتروني</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ عمود البريد الإلكتروني موجود مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `can_view_all` BOOLEAN DEFAULT FALSE AFTER `permissions`");
            echo "<p>✅ تم إضافة صلاحية المشاهدة الشاملة</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ صلاحية المشاهدة الشاملة موجودة مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `can_add_data` BOOLEAN DEFAULT TRUE AFTER `can_view_all`");
            echo "<p>✅ تم إضافة صلاحية إضافة البيانات</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ صلاحية إضافة البيانات موجودة مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `can_edit_data` BOOLEAN DEFAULT FALSE AFTER `can_add_data`");
            echo "<p>✅ تم إضافة صلاحية تعديل البيانات</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ صلاحية تعديل البيانات موجودة مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `can_delete_data` BOOLEAN DEFAULT FALSE AFTER `can_edit_data`");
            echo "<p>✅ تم إضافة صلاحية حذف البيانات</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ صلاحية حذف البيانات موجودة مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `assigned_agents` TEXT NULL AFTER `can_delete_data`");
            echo "<p>✅ تم إضافة الوكلاء المخصصين</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ الوكلاء المخصصين موجود مسبقاً</p>";
        }
        
        try {
            $pdo->exec("ALTER TABLE `users` ADD COLUMN `is_active` BOOLEAN DEFAULT TRUE AFTER `assigned_agents`");
            echo "<p>✅ تم إضافة حالة النشاط</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ حالة النشاط موجودة مسبقاً</p>";
        }
        
        echo "</div>";
        echo "</div>";
        
        // 5. حذف جدول collections القديم
        echo "<div class='card mb-3'>";
        echo "<div class='card-header bg-danger text-white'>";
        echo "<h5>🗑️ حذف جدول collections القديم</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        // أولاً نسخ احتياطي من البيانات
        try {
            $collections_data = $pdo->query("SELECT * FROM collections")->fetchAll();
            $backup_file = 'collections_backup_' . date('Y-m-d_H-i-s') . '.json';
            file_put_contents($backup_file, json_encode($collections_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo "<p>✅ تم إنشاء نسخة احتياطية: $backup_file</p>";
            
            // حذف الجدول
            $pdo->exec("DROP TABLE IF EXISTS collections");
            echo "<p>✅ تم حذف جدول collections القديم</p>";
            
        } catch (Exception $e) {
            echo "<p>⚠️ جدول collections غير موجود أو تم حذفه مسبقاً</p>";
        }
        
        echo "</div>";
        echo "</div>";
        
        // 6. إدراج بيانات تجريبية للأيام
        echo "<div class='card mb-3'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h5>📊 إدراج بيانات تجريبية</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        // إدراج أيام الشهر الحالي
        $current_month = date('Y-m');
        $days_in_month = date('t');
        
        for ($day = 1; $day <= $days_in_month; $day++) {
            $date = $current_month . '-' . sprintf('%02d', $day);
            $day_name = date('l', strtotime($date));
            $month_name = date('F', strtotime($date));
            $year = date('Y', strtotime($date));
            $is_weekend = in_array($day_name, ['Friday', 'Saturday']);
            
            // ترجمة أسماء الأيام والشهور
            $day_names_ar = [
                'Monday' => 'الاثنين',
                'Tuesday' => 'الثلاثاء', 
                'Wednesday' => 'الأربعاء',
                'Thursday' => 'الخميس',
                'Friday' => 'الجمعة',
                'Saturday' => 'السبت',
                'Sunday' => 'الأحد'
            ];
            
            $month_names_ar = [
                'January' => 'يناير', 'February' => 'فبراير', 'March' => 'مارس',
                'April' => 'أبريل', 'May' => 'مايو', 'June' => 'يونيو',
                'July' => 'يوليو', 'August' => 'أغسطس', 'September' => 'سبتمبر',
                'October' => 'أكتوبر', 'November' => 'نوفمبر', 'December' => 'ديسمبر'
            ];
            
            $day_name_ar = $day_names_ar[$day_name];
            $month_name_ar = $month_names_ar[$month_name];
            
            try {
                $stmt = $pdo->prepare("
                    INSERT IGNORE INTO days (date, day_name, month_name, year, is_weekend) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$date, $day_name_ar, $month_name_ar, $year, $is_weekend]);
            } catch (Exception $e) {
                // تجاهل الأخطاء للتواريخ المكررة
            }
        }
        
        echo "<p>✅ تم إدراج أيام الشهر الحالي</p>";
        
        // تحديث صلاحيات المستخدم admin
        $pdo->exec("
            UPDATE users SET 
                can_view_all = TRUE,
                can_add_data = TRUE,
                can_edit_data = TRUE,
                can_delete_data = TRUE,
                is_active = TRUE
            WHERE username = 'admin'
        ");
        echo "<p>✅ تم تحديث صلاحيات المدير</p>";
        
        echo "</div>";
        echo "</div>";
        
        // 7. عرض الإحصائيات النهائية
        echo "<div class='card mb-3'>";
        echo "<div class='card-header bg-primary text-white'>";
        echo "<h5>📈 إحصائيات قاعدة البيانات الجديدة</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $tables = ['users', 'agents', 'days', 'gateway', 'riyal_mobile'];
        
        echo "<div class='row'>";
        foreach ($tables as $table) {
            try {
                $count = $pdo->query("SELECT COUNT(*) as count FROM `$table`")->fetch()['count'];
                echo "<div class='col-md-2'>";
                echo "<div class='alert alert-info text-center'>";
                echo "<h6>$table</h6>";
                echo "<h4>$count</h4>";
                echo "<small>سجل</small>";
                echo "</div>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='col-md-2'>";
                echo "<div class='alert alert-warning text-center'>";
                echo "<h6>$table</h6>";
                echo "<h4>خطأ</h4>";
                echo "</div>";
                echo "</div>";
            }
        }
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        
        // 8. روابط مفيدة
        echo "<div class='card'>";
        echo "<div class='card-header bg-dark text-white'>";
        echo "<h5>🔗 الخطوات التالية</h5>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-4'>";
        echo "<h6>إدارة البيانات:</h6>";
        echo "<ul>";
        echo "<li><a href='view_new_structure.php' class='btn btn-sm btn-info'>عرض الهيكل الجديد</a></li>";
        echo "<li><a href='add_gateway_data.php' class='btn btn-sm btn-success'>إضافة بيانات البوابة</a></li>";
        echo "<li><a href='add_riyal_data.php' class='btn btn-sm btn-primary'>إضافة بيانات ريال موبايل</a></li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-4'>";
        echo "<h6>إدارة المستخدمين:</h6>";
        echo "<ul>";
        echo "<li><a href='manage_users.php' class='btn btn-sm btn-warning'>إدارة المستخدمين</a></li>";
        echo "<li><a href='user_permissions.php' class='btn btn-sm btn-secondary'>إدارة الصلاحيات</a></li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-4'>";
        echo "<h6>التقارير:</h6>";
        echo "<ul>";
        echo "<li><a href='daily_report_new.php' class='btn btn-sm btn-info'>التقرير اليومي</a></li>";
        echo "<li><a href='monthly_report.php' class='btn btn-sm btn-success'>التقرير الشهري</a></li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='alert alert-success mt-4'>";
        echo "<h4>🎉 تم إنشاء هيكل قاعدة البيانات الجديد بنجاح!</h4>";
        echo "<p><strong>الجداول المنشأة:</strong></p>";
        echo "<ul>";
        echo "<li>✅ جدول الأيام (days) - لإدارة التواريخ</li>";
        echo "<li>✅ جدول البوابة (gateway) - لبيانات البوابة</li>";
        echo "<li>✅ جدول ريال موبايل (riyal_mobile) - لبيانات ريال موبايل</li>";
        echo "<li>✅ تحديث جدول المستخدمين (users) - بالصلاحيات الجديدة</li>";
        echo "<li>🗑️ حذف جدول التحصيلات القديم (collections)</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ خطأ في إنشاء قاعدة البيانات</h5>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
</div>

</body>
</html>
