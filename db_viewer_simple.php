<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>مستعرض قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-container { max-height: 500px; overflow-y: auto; }
        .sql-editor { font-family: 'Courier New', monospace; }
    </style>
</head>
<body>

<?php
require_once 'config/database_sqlite.php';

$action = $_GET['action'] ?? 'tables';
$table = $_GET['table'] ?? '';
$sql = $_POST['sql'] ?? '';
?>

<div class="container-fluid mt-3">
    <div class="row">
        <!-- الشريط الجانبي -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-database"></i> قاعدة البيانات</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="?action=info" class="list-group-item list-group-item-action <?= $action === 'info' ? 'active' : '' ?>">
                            <i class="fas fa-info-circle"></i> معلومات قاعدة البيانات
                        </a>
                        <a href="?action=tables" class="list-group-item list-group-item-action <?= $action === 'tables' ? 'active' : '' ?>">
                            <i class="fas fa-table"></i> الجداول
                        </a>
                        <a href="?action=sql" class="list-group-item list-group-item-action <?= $action === 'sql' ? 'active' : '' ?>">
                            <i class="fas fa-code"></i> محرر SQL
                        </a>
                    </div>
                    
                    <!-- قائمة الجداول -->
                    <div class="mt-3 px-3">
                        <h6>الجداول المتاحة:</h6>
                        <?php
                        try {
                            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
                            foreach ($tables as $tbl) {
                                $isActive = ($table === $tbl['name']) ? 'fw-bold text-primary' : '';
                                echo "<div class='mb-1'>";
                                echo "<a href='?action=table&table={$tbl['name']}' class='text-decoration-none $isActive'>";
                                echo "<i class='fas fa-table fa-sm'></i> {$tbl['name']}";
                                echo "</a></div>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='text-danger'>خطأ: " . $e->getMessage() . "</p>";
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <a href="main.php" class="btn btn-secondary btn-sm">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9">
            <?php
            switch ($action) {
                case 'info':
                    // معلومات قاعدة البيانات
                    try {
                        $db_file = __DIR__ . '/database/collections_system.db';
                        $db_size = file_exists($db_file) ? filesize($db_file) : 0;
                        $db_size_mb = round($db_size / 1024 / 1024, 2);
                        
                        echo "<div class='card'>";
                        echo "<div class='card-header bg-info text-white'>";
                        echo "<h5><i class='fas fa-info-circle'></i> معلومات قاعدة البيانات</h5>";
                        echo "</div>";
                        echo "<div class='card-body'>";
                        
                        echo "<div class='row'>";
                        echo "<div class='col-md-6'>";
                        echo "<h6>معلومات عامة:</h6>";
                        echo "<ul class='list-unstyled'>";
                        echo "<li><strong>نوع قاعدة البيانات:</strong> SQLite</li>";
                        echo "<li><strong>حجم الملف:</strong> $db_size_mb MB</li>";
                        echo "<li><strong>آخر تعديل:</strong> " . date('Y-m-d H:i:s', filemtime($db_file)) . "</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                        echo "<div class='col-md-6'>";
                        echo "<h6>إحصائيات الجداول:</h6>";
                        
                        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>الجدول</th><th>عدد السجلات</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($tables as $table_info) {
                            $table_name = $table_info['name'];
                            $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
                            echo "<tr>";
                            echo "<td><a href='?action=table&table=$table_name'>$table_name</a></td>";
                            echo "<td><span class='badge bg-primary'>$count</span></td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        echo "</div>";
                        echo "</div>";
                        
                        echo "</div>";
                        echo "</div>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
                    }
                    break;
                    
                case 'table':
                    // عرض جدول محدد
                    if (empty($table)) {
                        echo "<div class='alert alert-warning'>يرجى اختيار جدول لعرضه</div>";
                        break;
                    }
                    
                    try {
                        $page = (int)($_GET['page'] ?? 1);
                        $limit = 50;
                        $offset = ($page - 1) * $limit;
                        
                        $total_count = $pdo->query("SELECT COUNT(*) as count FROM `$table`")->fetch()['count'];
                        $columns = $pdo->query("PRAGMA table_info(`$table`)")->fetchAll();
                        
                        echo "<div class='card'>";
                        echo "<div class='card-header bg-success text-white'>";
                        echo "<h5><i class='fas fa-table'></i> جدول: $table</h5>";
                        echo "<span class='badge bg-light text-dark'>$total_count سجل</span>";
                        echo "</div>";
                        echo "<div class='card-body'>";
                        
                        if ($total_count > 0) {
                            $data = $pdo->query("SELECT * FROM `$table` LIMIT $limit OFFSET $offset")->fetchAll();
                            
                            echo "<div class='table-container'>";
                            echo "<table class='table table-striped table-hover table-sm'>";
                            echo "<thead class='table-dark'>";
                            echo "<tr>";
                            foreach ($columns as $column) {
                                echo "<th>{$column['name']}</th>";
                            }
                            echo "</tr>";
                            echo "</thead>";
                            echo "<tbody>";
                            
                            foreach ($data as $row) {
                                echo "<tr>";
                                foreach ($columns as $column) {
                                    $value = $row[$column['name']];
                                    echo "<td>";
                                    if (is_null($value)) {
                                        echo "<span class='text-muted'>NULL</span>";
                                    } elseif (is_numeric($value)) {
                                        echo "<span class='text-primary'>$value</span>";
                                    } else {
                                        $display_value = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
                                        echo htmlspecialchars($display_value);
                                    }
                                    echo "</td>";
                                }
                                echo "</tr>";
                            }
                            
                            echo "</tbody>";
                            echo "</table>";
                            echo "</div>";
                            
                            // التصفح
                            if ($total_count > $limit) {
                                $total_pages = ceil($total_count / $limit);
                                echo "<nav><ul class='pagination pagination-sm justify-content-center'>";
                                
                                if ($page > 1) {
                                    echo "<li class='page-item'><a class='page-link' href='?action=table&table=$table&page=" . ($page - 1) . "'>السابق</a></li>";
                                }
                                
                                for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++) {
                                    $active = ($i == $page) ? 'active' : '';
                                    echo "<li class='page-item $active'><a class='page-link' href='?action=table&table=$table&page=$i'>$i</a></li>";
                                }
                                
                                if ($page < $total_pages) {
                                    echo "<li class='page-item'><a class='page-link' href='?action=table&table=$table&page=" . ($page + 1) . "'>التالي</a></li>";
                                }
                                
                                echo "</ul></nav>";
                            }
                        } else {
                            echo "<div class='alert alert-info'>الجدول فارغ</div>";
                        }
                        
                        echo "</div>";
                        echo "</div>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
                    }
                    break;
                    
                case 'sql':
                    // محرر SQL
                    $results = null;
                    $error = null;
                    
                    if (!empty($sql)) {
                        try {
                            $stmt = $pdo->prepare($sql);
                            $stmt->execute();
                            
                            if (stripos($sql, 'SELECT') === 0) {
                                $results = $stmt->fetchAll();
                            } else {
                                $results = "تم تنفيذ الاستعلام. الصفوف المتأثرة: " . $stmt->rowCount();
                            }
                        } catch (Exception $e) {
                            $error = $e->getMessage();
                        }
                    }
                    
                    echo "<div class='card'>";
                    echo "<div class='card-header bg-warning text-dark'>";
                    echo "<h5><i class='fas fa-code'></i> محرر SQL</h5>";
                    echo "</div>";
                    echo "<div class='card-body'>";
                    
                    echo "<form method='POST'>";
                    echo "<div class='mb-3'>";
                    echo "<textarea class='form-control sql-editor' name='sql' rows='6' placeholder='أدخل استعلام SQL...'>" . htmlspecialchars($sql) . "</textarea>";
                    echo "</div>";
                    echo "<button type='submit' class='btn btn-primary'><i class='fas fa-play'></i> تنفيذ</button>";
                    echo "</form>";
                    
                    if ($error) {
                        echo "<div class='alert alert-danger mt-3'>خطأ: " . htmlspecialchars($error) . "</div>";
                    }
                    
                    if ($results && !$error) {
                        echo "<div class='card mt-3'>";
                        echo "<div class='card-header bg-success text-white'>النتائج</div>";
                        echo "<div class='card-body'>";
                        
                        if (is_array($results)) {
                            if (empty($results)) {
                                echo "<div class='alert alert-info'>لا توجد نتائج</div>";
                            } else {
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-striped table-sm'>";
                                echo "<thead class='table-dark'>";
                                echo "<tr>";
                                foreach (array_keys($results[0]) as $column) {
                                    echo "<th>" . htmlspecialchars($column) . "</th>";
                                }
                                echo "</tr>";
                                echo "</thead>";
                                echo "<tbody>";
                                foreach ($results as $row) {
                                    echo "<tr>";
                                    foreach ($row as $value) {
                                        echo "<td>";
                                        if (is_null($value)) {
                                            echo "<span class='text-muted'>NULL</span>";
                                        } else {
                                            echo htmlspecialchars($value);
                                        }
                                        echo "</td>";
                                    }
                                    echo "</tr>";
                                }
                                echo "</tbody>";
                                echo "</table>";
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'>" . htmlspecialchars($results) . "</div>";
                        }
                        
                        echo "</div>";
                        echo "</div>";
                    }
                    
                    echo "</div>";
                    echo "</div>";
                    break;
                    
                default:
                    // عرض الجداول
                    try {
                        echo "<div class='card'>";
                        echo "<div class='card-header bg-primary text-white'>";
                        echo "<h5><i class='fas fa-table'></i> جداول قاعدة البيانات</h5>";
                        echo "</div>";
                        echo "<div class='card-body'>";
                        
                        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
                        
                        if (empty($tables)) {
                            echo "<div class='alert alert-warning'>لا توجد جداول</div>";
                        } else {
                            echo "<div class='row'>";
                            foreach ($tables as $table_info) {
                                $table_name = $table_info['name'];
                                $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
                                
                                echo "<div class='col-md-4 mb-3'>";
                                echo "<div class='card'>";
                                echo "<div class='card-body text-center'>";
                                echo "<h5><i class='fas fa-table'></i> $table_name</h5>";
                                echo "<p class='text-muted'>$count سجل</p>";
                                echo "<a href='?action=table&table=$table_name' class='btn btn-primary btn-sm'>عرض البيانات</a>";
                                echo "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                            echo "</div>";
                        }
                        
                        echo "</div>";
                        echo "</div>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
                    }
            }
            ?>
        </div>
    </div>
</div>

</body>
</html>
