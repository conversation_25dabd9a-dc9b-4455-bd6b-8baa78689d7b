<?php
require_once 'config/database_sqlite.php';

header('Content-Type: application/json');

try {
    $date = $_POST['date'] ?? '';
    
    if (empty($date)) {
        throw new Exception('التاريخ مطلوب');
    }
    
    // التحقق من صحة التاريخ
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        throw new Exception('تنسيق التاريخ غير صحيح');
    }
    
    // جلب البيانات الموجودة للتاريخ المحدد
    $stmt = $pdo->prepare("
        SELECT agent_id, gateway_amount, riyal_mobile_amount
        FROM collections
        WHERE collection_date = ?
    ");
    $stmt->execute([$date]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنظيم البيانات
    $collections = [];
    foreach ($results as $row) {
        $collections[$row['agent_id']] = [
            'gateway' => (float)$row['gateway_amount'],
            'riyal' => (float)$row['riyal_mobile_amount']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'collections' => $collections,
        'count' => count($collections),
        'date' => $date
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
