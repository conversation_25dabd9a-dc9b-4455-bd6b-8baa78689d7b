<?php
require_once 'config/database_sqlite.php';

echo "<h2>استيراد بيانات ريال موبايل</h2>";

$csv_file = 'E:\agent\database\ryal.csv';

// التحقق من وجود الملف
if (!file_exists($csv_file)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 20px 0;'>";
    echo "<h4>❌ ملف ريال موبايل غير موجود</h4>";
    echo "<p>المسار المطلوب: <code>$csv_file</code></p>";
    echo "<p>يرجى التأكد من وجود الملف في المسار الصحيح</p>";
    echo "</div>";
    echo "<p><a href='main.php'>العودة للصفحة الرئيسية</a></p>";
    exit;
}

// معلومات الملف
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460; margin: 20px 0;'>";
echo "<h4>📁 معلومات الملف</h4>";
echo "<p><strong>المسار:</strong> $csv_file</p>";
echo "<p><strong>الحجم:</strong> " . number_format(filesize($csv_file)) . " بايت</p>";
echo "<p><strong>تاريخ التعديل:</strong> " . date('Y-m-d H:i:s', filemtime($csv_file)) . "</p>";
echo "</div>";

// معاينة الملف
echo "<h3>معاينة محتوى الملف:</h3>";
$handle = fopen($csv_file, 'r');
if ($handle) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    $row_count = 0;
    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE && $row_count < 5) {
        echo "<tr>";
        foreach ($data as $cell) {
            $style = $row_count == 0 ? 'background: #f8f9fa; font-weight: bold;' : '';
            echo "<td style='padding: 8px; border: 1px solid #ddd; $style'>" . htmlspecialchars($cell) . "</td>";
        }
        echo "</tr>";
        $row_count++;
    }
    echo "</table>";
    fclose($handle);
    echo "<p><em>عرض أول 5 صفوف فقط...</em></p>";
}

// زر بدء الاستيراد
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='process_riyal_import.php' class='btn btn-primary' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>";
echo "🚀 بدء استيراد بيانات ريال موبايل";
echo "</a>";
echo "</div>";

// تعليمات
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404; margin: 20px 0;'>";
echo "<h4>📋 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>سيتم إدراج البيانات في عمود <strong>ريال موبايل</strong></li>";
echo "<li>إذا كان هناك سجل موجود للوكيل في نفس التاريخ، سيتم تحديث عمود ريال موبايل فقط</li>";
echo "<li>إذا لم يكن هناك سجل، سيتم إنشاء سجل جديد مع ريال موبايل فقط</li>";
echo "<li>البيانات الموجودة في عمود البوابة لن تتأثر</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='main.php'>العودة للصفحة الرئيسية</a></p>";
?>
