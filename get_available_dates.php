<?php
require_once 'auth.php';
require_once 'config/database.php';

header('Content-Type: application/json');

try {
    // جلب التواريخ المتاحة مع عدد السجلات
    $stmt = $pdo->query("
        SELECT 
            collection_date as date,
            COUNT(*) as count,
            SUM(gateway_amount + riyal_mobile_amount) as total_amount
        FROM collections
        GROUP BY collection_date
        ORDER BY collection_date DESC
    ");
    
    $dates = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'dates' => $dates,
        'total_dates' => count($dates)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
