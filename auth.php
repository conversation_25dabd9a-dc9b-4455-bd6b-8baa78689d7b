<?php
// ملف التحقق من الجلسة والصلاحيات
session_start();

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

// التحقق من الصلاحيات
function checkPermission($required_permission = 'user') {
    checkLogin();
    
    $user_permission = $_SESSION['permissions'] ?? 'user';
    
    $permission_levels = [
        'viewer' => 1,
        'user' => 2,
        'admin' => 3
    ];
    
    $user_level = $permission_levels[$user_permission] ?? 1;
    $required_level = $permission_levels[$required_permission] ?? 2;
    
    if ($user_level < $required_level) {
        header('Location: main.php?error=no_permission');
        exit;
    }
}

// تسجيل الخروج
function logout() {
    session_destroy();
    header('Location: login.php');
    exit;
}

// معلومات المستخدم الحالي
function getCurrentUser() {
    return [
        'user_id' => $_SESSION['user_id'] ?? null,
        'employee_name' => $_SESSION['employee_name'] ?? '',
        'username' => $_SESSION['username'] ?? '',
        'permissions' => $_SESSION['permissions'] ?? 'user'
    ];
}

// التحقق من تسجيل الدخول للصفحات التي تتطلب ذلك
checkLogin();
?>
