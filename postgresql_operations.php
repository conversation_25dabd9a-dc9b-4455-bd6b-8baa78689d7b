<?php
$action = $_POST['action'] ?? '';

try {
    require_once 'config/database_postgresql.php';
    
    switch ($action) {
        case 'create_tables':
            echo "<div class='alert alert-info'>";
            echo "<h6>🔄 إنشاء الجداول...</h6>";
            echo "</div>";
            
            // إنشاء جدول المستخدمين
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS users (
                    user_id SERIAL PRIMARY KEY,
                    employee_name VARCHAR(255) NOT NULL,
                    username VARCHAR(100) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    permissions VARCHAR(20) DEFAULT 'user' CHECK (permissions IN ('admin', 'user')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL
                )
            ");
            echo "<p>✅ تم إنشاء جدول المستخدمين</p>";
            
            // إنشاء جدول الوكلاء
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS agents (
                    agent_id SERIAL PRIMARY KEY,
                    agent_name VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            echo "<p>✅ تم إنشاء جدول الوكلاء</p>";
            
            // إنشاء جدول التحصيلات
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS collections (
                    id SERIAL PRIMARY KEY,
                    agent_id INTEGER REFERENCES agents(agent_id) ON DELETE CASCADE,
                    gateway_amount DECIMAL(15,2) DEFAULT 0.00,
                    riyal_mobile_amount DECIMAL(15,2) DEFAULT 0.00,
                    collection_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            echo "<p>✅ تم إنشاء جدول التحصيلات</p>";
            
            // إنشاء الفهارس
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_collections_agent_date ON collections(agent_id, collection_date)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_collections_date ON collections(collection_date)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_agents_name ON agents(agent_name)");
            echo "<p>✅ تم إنشاء الفهارس</p>";
            
            // إنشاء trigger للتحديث التلقائي
            $pdo->exec("
                CREATE OR REPLACE FUNCTION update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ language 'plpgsql'
            ");
            
            $pdo->exec("
                DROP TRIGGER IF EXISTS update_collections_updated_at ON collections;
                CREATE TRIGGER update_collections_updated_at
                    BEFORE UPDATE ON collections
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column()
            ");
            echo "<p>✅ تم إنشاء المشغلات (Triggers)</p>";
            
            // إضافة البيانات الافتراضية
            $user_count = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
            if ($user_count == 0) {
                $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
                $user_password = password_hash('user123', PASSWORD_DEFAULT);
                
                $pdo->exec("
                    INSERT INTO users (employee_name, username, password, permissions) VALUES
                    ('المدير العام', 'admin', '$admin_password', 'admin'),
                    ('مستخدم تجريبي', 'user1', '$user_password', 'user')
                ");
                echo "<p>✅ تم إضافة المستخدمين الافتراضيين</p>";
            }
            
            $agents_count = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
            if ($agents_count == 0) {
                $pdo->exec("
                    INSERT INTO agents (agent_name) VALUES
                    ('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
                    ('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
                    ('المترب'), ('باتكو'), ('بران')
                ");
                echo "<p>✅ تم إضافة الوكلاء الافتراضيين</p>";
            }
            
            echo "<div class='alert alert-success'>";
            echo "<h6>🎉 تم إنشاء جميع الجداول بنجاح!</h6>";
            echo "</div>";
            break;
            
        case 'import_data':
            echo "<div class='alert alert-info'>";
            echo "<h6>🔄 استيراد البيانات من SQLite...</h6>";
            echo "</div>";
            
            // محاولة الاتصال بـ SQLite
            $sqlite_file = __DIR__ . '/database/collections_system.db';
            if (file_exists($sqlite_file)) {
                try {
                    $sqlite_pdo = new PDO("sqlite:$sqlite_file");
                    $sqlite_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // نقل بيانات التحصيلات
                    $collections = $sqlite_pdo->query("SELECT * FROM collections")->fetchAll();
                    if (!empty($collections)) {
                        $pdo->exec("DELETE FROM collections"); // مسح البيانات الموجودة
                        
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO collections (agent_id, gateway_amount, riyal_mobile_amount, collection_date) 
                            VALUES (?, ?, ?, ?)
                        ");
                        
                        $transferred = 0;
                        foreach ($collections as $collection) {
                            $insert_stmt->execute([
                                $collection['agent_id'],
                                $collection['gateway_amount'],
                                $collection['riyal_mobile_amount'],
                                $collection['collection_date']
                            ]);
                            $transferred++;
                        }
                        
                        echo "<p>✅ تم نقل $transferred سجل تحصيل من SQLite</p>";
                    } else {
                        echo "<p class='text-info'>ℹ️ لا توجد بيانات تحصيلات في SQLite</p>";
                    }
                    
                    echo "<div class='alert alert-success'>";
                    echo "<h6>🎉 تم استيراد البيانات بنجاح!</h6>";
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-warning'>";
                    echo "<h6>⚠️ لا يمكن استيراد البيانات من SQLite</h6>";
                    echo "<p>" . $e->getMessage() . "</p>";
                    echo "</div>";
                }
            } else {
                echo "<div class='alert alert-info'>";
                echo "<h6>ℹ️ ملف SQLite غير موجود</h6>";
                echo "<p>سيتم استخدام قاعدة بيانات PostgreSQL فارغة.</p>";
                echo "</div>";
            }
            break;
            
        case 'switch_system':
            echo "<div class='alert alert-info'>";
            echo "<h6>🔄 تحويل النظام لاستخدام PostgreSQL...</h6>";
            echo "</div>";
            
            // إنشاء نسخة احتياطية من ملف قاعدة البيانات الحالي
            $current_db_file = __DIR__ . '/config/database_sqlite.php';
            if (file_exists($current_db_file)) {
                $backup_file = __DIR__ . '/config/database_sqlite_backup_' . date('Y-m-d_H-i-s') . '.php';
                copy($current_db_file, $backup_file);
                echo "<p>✅ تم إنشاء نسخة احتياطية: " . basename($backup_file) . "</p>";
            }
            
            // إنشاء ملف الاتصال الموحد
            $unified_db_content = '<?php
// ملف الاتصال الموحد بقاعدة البيانات
// يمكن التبديل بين PostgreSQL و SQLite و MySQL

$database_type = "postgresql"; // postgresql, mysql, sqlite

switch ($database_type) {
    case "postgresql":
        require_once __DIR__ . "/database_postgresql.php";
        break;
    case "mysql":
        require_once __DIR__ . "/database_mysql.php";
        break;
    case "sqlite":
    default:
        require_once __DIR__ . "/database_sqlite.php";
        break;
}
?>';
            
            file_put_contents(__DIR__ . '/config/database.php', $unified_db_content);
            echo "<p>✅ تم إنشاء ملف الاتصال الموحد</p>";
            
            // تحديث الملفات التي تستخدم قاعدة البيانات
            $files_to_update = [
                'auth.php',
                'login.php',
                'daily_report_ajax.php',
                'process_csv_import.php',
                'save_collections.php',
                'check_import_results.php',
                'get_available_dates.php',
                'db_viewer_simple.php'
            ];
            
            $updated_files = 0;
            foreach ($files_to_update as $file) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    $original_content = $content;
                    
                    // استبدال require_once لقاعدة البيانات
                    $content = str_replace(
                        "require_once 'config/database_sqlite.php';",
                        "require_once 'config/database.php';",
                        $content
                    );
                    
                    // استبدال استعلامات SQLite المحددة بـ PostgreSQL
                    $sqlite_to_postgresql = [
                        'AUTOINCREMENT' => 'SERIAL',
                        'INTEGER PRIMARY KEY AUTOINCREMENT' => 'SERIAL PRIMARY KEY'
                    ];
                    
                    foreach ($sqlite_to_postgresql as $sqlite => $postgresql) {
                        $content = str_replace($sqlite, $postgresql, $content);
                    }
                    
                    if ($content !== $original_content) {
                        file_put_contents($file, $content);
                        $updated_files++;
                    }
                }
            }
            
            echo "<p>✅ تم تحديث $updated_files ملف</p>";
            
            // اختبار الاتصال بقاعدة البيانات الجديدة
            require_once 'config/database.php';
            
            $test_users = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch();
            echo "<p>✅ اختبار المستخدمين: {$test_users['count']}</p>";
            
            $test_agents = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch();
            echo "<p>✅ اختبار الوكلاء: {$test_agents['count']}</p>";
            
            $test_collections = $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch();
            echo "<p>✅ اختبار التحصيلات: {$test_collections['count']}</p>";
            
            echo "<div class='alert alert-success'>";
            echo "<h6>🎉 تم تحويل النظام بنجاح!</h6>";
            echo "<p>النظام يستخدم الآن قاعدة بيانات PostgreSQL.</p>";
            echo "<ul>";
            echo "<li><strong>قاعدة البيانات:</strong> agent</li>";
            echo "<li><strong>نوع قاعدة البيانات:</strong> PostgreSQL</li>";
            echo "<li><strong>الخادم:</strong> localhost:5432</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<div class='mt-3'>";
            echo "<a href='main.php' class='btn btn-success'>اختبار النظام</a> ";
            echo "<a href='db_viewer_simple.php' class='btn btn-info'>مستعرض قاعدة البيانات</a>";
            echo "</div>";
            break;
            
        default:
            echo "<div class='alert alert-danger'>عملية غير صحيحة</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>❌ خطأ في العملية:</h6>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
