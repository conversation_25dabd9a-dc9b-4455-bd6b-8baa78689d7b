<?php
require_once 'auth.php';
require_once 'config/database.php';

$date = $_GET['date'] ?? date('Y-m-d');

// التحقق من صحة التاريخ
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    echo '<div class="alert alert-danger">تاريخ غير صحيح</div>';
    exit;
}

try {
    // جلب بيانات التحصيلات لليوم المحدد
    $stmt = $pdo->prepare("
        SELECT 
            a.agent_name,
            c.gateway_amount,
            c.riyal_mobile_amount,
            (c.gateway_amount + c.riyal_mobile_amount) as total_amount
        FROM agents a
        LEFT JOIN collections c ON a.agent_id = c.agent_id AND c.collection_date = ?
        ORDER BY a.agent_name
    ");
    $stmt->execute([$date]);
    $collections = $stmt->fetchAll();

    // حساب الإجماليات
    $total_gateway = 0;
    $total_riyal = 0;
    $total_amount = 0;
    $active_agents = 0;

    foreach ($collections as $collection) {
        if ($collection['gateway_amount'] > 0 || $collection['riyal_mobile_amount'] > 0) {
            $active_agents++;
        }
        $total_gateway += $collection['gateway_amount'] ?? 0;
        $total_riyal += $collection['riyal_mobile_amount'] ?? 0;
        $total_amount += $collection['total_amount'] ?? 0;
    }

    // تنسيق التاريخ للعرض
    $formatted_date = date('d/m/Y', strtotime($date));
    $day_name = [
        'Sunday' => 'الأحد',
        'Monday' => 'الاثنين',
        'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء',
        'Thursday' => 'الخميس',
        'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ][date('l', strtotime($date))];

    ?>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-calendar-day"></i> 
                تقرير يوم <?= $day_name ?> الموافق <?= $formatted_date ?>
            </h5>
        </div>
        <div class="card-body">
            <!-- ملخص سريع -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي جيت واي</h6>
                            <h4 class="text-primary"><?= number_format($total_gateway, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">إجمالي ريال موبايل</h6>
                            <h4 class="text-info"><?= number_format($total_riyal, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الإجمالي العام</h6>
                            <h4 class="text-success"><?= number_format($total_amount, 2) ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الوكلاء النشطون</h6>
                            <h4 class="text-warning"><?= $active_agents ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول التفاصيل -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>اسم الوكيل</th>
                            <th>جيت واي</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($collections as $collection): ?>
                        <tr class="<?= ($collection['total_amount'] > 0) ? 'table-success' : '' ?>">
                            <td><?= htmlspecialchars($collection['agent_name']) ?></td>
                            <td><?= number_format($collection['gateway_amount'] ?? 0, 2) ?></td>
                            <td><?= number_format($collection['riyal_mobile_amount'] ?? 0, 2) ?></td>
                            <td><strong><?= number_format($collection['total_amount'] ?? 0, 2) ?></strong></td>
                            <td>
                                <?php if ($collection['total_amount'] > 0): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-dark">
                        <tr>
                            <th>الإجمالي العام</th>
                            <th><?= number_format($total_gateway, 2) ?></th>
                            <th><?= number_format($total_riyal, 2) ?></th>
                            <th><?= number_format($total_amount, 2) ?></th>
                            <th><?= $active_agents ?> وكيل</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex justify-content-between mt-3">
                <div>
                    <button class="btn btn-primary" onclick="printReport()">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </button>
                    <button class="btn btn-success" onclick="exportToExcel('<?= $date ?>')">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                </div>
                <div>
                    <?php if ($total_amount == 0): ?>
                        <span class="text-muted">لا توجد تحصيلات لهذا اليوم</span>
                    <?php else: ?>
                        <span class="text-success">
                            <i class="fas fa-check-circle"></i> 
                            تم تسجيل تحصيلات بقيمة <?= number_format($total_amount, 2) ?> ريال
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function printReport() {
            window.print();
        }

        function exportToExcel(date) {
            window.open(`export_excel.php?type=daily&date=${date}`, '_blank');
        }
    </script>

    <?php
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
