<?php
// إعدادات قاعدة بيانات MySQL
$mysql_config = [
    'host' => 'localhost',
    'dbname' => 'collections_system',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'port' => 3306
];

try {
    $pdo = new PDO(
        "mysql:host={$mysql_config['host']};dbname={$mysql_config['dbname']};port={$mysql_config['port']};charset={$mysql_config['charset']}", 
        $mysql_config['username'], 
        $mysql_config['password']
    );
    
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}
?>