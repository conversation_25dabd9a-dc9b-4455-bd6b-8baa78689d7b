/* تصميم مضغوط وعملي */
@font-face {
    font-family: '<PERSON><PERSON><PERSON>';
    src: url('../fonts/<PERSON>-Art-bold.ttf') format('truetype');
    font-weight: bold;
    font-display: swap;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    margin: 0;
    padding: 0;
    font-size: 13px;
    direction: rtl;
}

/* حاوي رئيسي مضغوط */
.main-container {
    background: white;
    margin: 0;
    min-height: 100vh;
    box-shadow: none;
}

/* شريط أيقونات مضغوط */
.top-navigation {
    background: linear-gradient(45deg, #2c3e50, #3498db);
    padding: 8px 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.nav-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.nav-icon {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 6px 10px;
    color: white;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 11px;
    text-align: center;
    min-width: 70px;
}

.nav-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.nav-icon.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.nav-icon i {
    font-size: 14px;
    margin-bottom: 2px;
    display: block;
}

.user-info {
    background: rgba(255, 255, 255, 0.2) !important;
    cursor: default;
    font-size: 10px;
}

/* منطقة المحتوى مضغوطة */
.content-area {
    padding: 10px 15px;
    min-height: calc(100vh - 60px);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.content-section h3 {
    margin-bottom: 10px;
    font-size: 16px;
    color: #495057;
    padding: 5px 0;
    border-bottom: 1px solid #dee2e6;
}

/* تصميم التقارير مضغوط */
.reports-main-container {
    margin: 0;
}

.sub-navigation {
    margin-bottom: 10px;
}

.sub-nav-icons {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.sub-nav-icon {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 6px 10px;
    color: #495057;
    text-decoration: none;
    font-size: 11px;
    text-align: center;
    min-width: 60px;
    transition: all 0.2s ease;
}

.sub-nav-icon:hover {
    background: #e9ecef;
    color: #495057;
}

.sub-nav-icon.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.sub-nav-icon i {
    font-size: 12px;
    margin-bottom: 2px;
    display: block;
}

/* إعدادات التقرير مضغوطة */
.report-settings-area {
    margin-bottom: 10px;
}

.report-settings {
    display: none;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.report-settings.active {
    display: block;
}

.form-label {
    font-size: 12px;
    margin-bottom: 3px;
    font-weight: 600;
}

.form-control {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
}

.btn {
    font-size: 12px;
    padding: 5px 12px;
}

/* منطقة عرض التقرير مضغوطة */
#report-display-area .card {
    margin: 0;
    border-radius: 5px;
}

.card-header {
    padding: 8px 15px;
    font-size: 14px;
}

.card-body {
    padding: 10px 15px;
}

/* جداول مضغوطة */
.table {
    font-size: 12px;
    margin-bottom: 10px;
}

.table th,
.table td {
    padding: 4px 8px;
    vertical-align: middle;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

/* بطاقات الإحصائيات مضغوطة */
.stats-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 8px;
    text-align: center;
    margin-bottom: 8px;
}

.stats-card h6 {
    font-size: 11px;
    margin-bottom: 3px;
    color: #6c757d;
}

.stats-card h4 {
    font-size: 16px;
    margin: 0;
    font-weight: bold;
}

/* تنسيق الصفوف والأعمدة */
.row {
    margin: 0 -5px;
}

.col-md-3,
.col-md-4,
.col-md-6 {
    padding: 0 5px;
}

/* تنبيهات مضغوطة */
.alert {
    padding: 8px 12px;
    margin-bottom: 10px;
    font-size: 12px;
}

/* أزرار مضغوطة */
.btn-sm {
    font-size: 11px;
    padding: 3px 8px;
}

/* إخفاء العناصر غير الضرورية على الشاشات الصغيرة */
@media (max-width: 768px) {
    .nav-icon {
        min-width: 50px;
        font-size: 10px;
        padding: 4px 6px;
    }
    
    .nav-icon i {
        font-size: 12px;
    }
    
    .content-area {
        padding: 8px 10px;
    }
    
    .table {
        font-size: 11px;
    }
    
    .stats-card h4 {
        font-size: 14px;
    }
}

/* تحسينات إضافية للمساحة */
.mb-4 {
    margin-bottom: 10px !important;
}

.mb-3 {
    margin-bottom: 8px !important;
}

.mb-2 {
    margin-bottom: 5px !important;
}

.mt-4 {
    margin-top: 10px !important;
}

.mt-3 {
    margin-top: 8px !important;
}

/* إزالة المساحات الزائدة */
.container-fluid {
    padding: 0;
}

.card-deck .card {
    margin-bottom: 10px;
}

/* تحسين عرض البيانات */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    border: 1px solid #dee2e6;
    padding: 4px 6px;
    text-align: center;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.data-table tbody tr:nth-child(even) {
    background: #f9f9f9;
}

.data-table tbody tr:hover {
    background: #e3f2fd;
}

/* تحسين النماذج */
.form-row {
    margin: 0 -3px;
}

.form-row .col {
    padding: 0 3px;
}

.input-group {
    margin-bottom: 8px;
}

/* تحسين الأيقونات */
.fas,
.far {
    width: 14px;
    text-align: center;
}

/* إزالة الحدود والظلال الزائدة */
.card {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.btn {
    box-shadow: none;
}

.form-control:focus {
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}
