<?php
require_once 'config/database_sqlite.php';

$from_date = $_GET['from'] ?? date('Y-m-d', strtotime('-7 days'));
$to_date = $_GET['to'] ?? date('Y-m-d');

if ($from_date && $to_date) {
    $query = "
        SELECT
            a.agent_name,
            SUM(c.gateway_amount) as total_gateway,
            SUM(c.riyal_mobile_amount) as total_riyal,
            SUM(c.gateway_amount + c.riyal_mobile_amount) as grand_total
        FROM collections c
        JOIN agents a ON c.agent_id = a.agent_id
        WHERE c.collection_date BETWEEN ? AND ?
        GROUP BY a.agent_id, a.agent_name
        ORDER BY a.agent_name
    ";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$from_date, $to_date]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب الإجماليات العامة
    $total_gateway = array_sum(array_column($results, 'total_gateway'));
    $total_riyal = array_sum(array_column($results, 'total_riyal'));
    $grand_total = $total_gateway + $total_riyal;
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقرير الأسبوعي الإجمالي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">التقرير الأسبوعي الإجمالي</h2>

    <form method="GET" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <label>من تاريخ:</label>
                <input type="date" name="from" class="form-control" value="<?= $from_date ?>">
            </div>
            <div class="col-md-3">
                <label>إلى تاريخ:</label>
                <input type="date" name="to" class="form-control" value="<?= $to_date ?>">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">عرض التقرير</button>
            </div>
        </div>
    </form>

    <?php if (isset($results)): ?>
    <div class="text-center mb-3">
        <h4>تقرير من <?= $from_date ?> إلى <?= $to_date ?></h4>
        <div class="btn-group no-print">
            <button onclick="window.print()" class="btn btn-outline-primary btn-sm">طباعة</button>
            <a href="export_excel.php?type=summary&from=<?= $from_date ?>&to=<?= $to_date ?>"
               class="btn btn-outline-success btn-sm">تصدير Excel</a>
        </div>
    </div>

    <table class="table table-bordered">
        <thead class="table-dark">
            <tr>
                <th>الوكيل</th>
                <th>البوابة</th>
                <th>الريال موبايل</th>
                <th>الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($results as $row): ?>
            <tr>
                <td><?= $row['agent_name'] ?></td>
                <td><?= number_format($row['total_gateway'], 2) ?></td>
                <td><?= number_format($row['total_riyal'], 2) ?></td>
                <td><?= number_format($row['grand_total'], 2) ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot class="table-success">
            <tr>
                <th>الإجمالي</th>
                <th><?= number_format($total_gateway, 2) ?></th>
                <th><?= number_format($total_riyal, 2) ?></th>
                <th><?= number_format($grand_total, 2) ?></th>
            </tr>
        </tfoot>
    </table>
    <?php endif; ?>

    <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
</div>
</body>
</html>

