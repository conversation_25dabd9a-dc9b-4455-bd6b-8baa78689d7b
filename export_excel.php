<?php
require_once 'config/database.php';

$report_type = $_GET['type'] ?? '';
$from_date = $_GET['from'] ?? '';
$to_date = $_GET['to'] ?? '';
$agent_id = $_GET['agent_id'] ?? '';

if (!$report_type) {
    die('نوع التقرير غير محدد');
}

// تحديد اسم الملف والاستعلام حسب نوع التقرير
switch ($report_type) {
    case 'detailed':
        $filename = "تقرير_تفصيلي_" . date('Y-m-d');
        $query = "
            SELECT a.agent_name as 'الوكيل', c.collection_date as 'التاريخ', 
                   c.gateway_amount as 'البوابة', c.riyal_mobile_amount as 'الريال موبايل',
                   (c.gateway_amount + c.riyal_mobile_amount) as 'الإجمالي'
            FROM collections c
            JOIN agents a ON c.agent_id = a.agent_id
            WHERE c.collection_date BETWEEN ? AND ?
            ORDER BY a.agent_name, c.collection_date
        ";
        $params = [$from_date, $to_date];
        break;
        
    case 'summary':
        $filename = "تقرير_إجمالي_" . date('Y-m-d');
        $query = "
            SELECT a.agent_name as 'الوكيل',
                   SUM(c.gateway_amount) as 'البوابة',
                   SUM(c.riyal_mobile_amount) as 'الريال موبايل',
                   SUM(c.gateway_amount + c.riyal_mobile_amount) as 'الإجمالي'
            FROM collections c
            JOIN agents a ON c.agent_id = a.agent_id
            WHERE c.collection_date BETWEEN ? AND ?
            GROUP BY a.agent_id, a.agent_name
            ORDER BY a.agent_name
        ";
        $params = [$from_date, $to_date];
        break;
        
    case 'agent':
        $agent_stmt = $pdo->prepare("SELECT agent_name FROM agents WHERE agent_id = ?");
        $agent_stmt->execute([$agent_id]);
        $agent_name = $agent_stmt->fetchColumn();
        
        $filename = "تقرير_" . $agent_name . "_" . date('Y-m-d');
        $query = "
            SELECT collection_date as 'التاريخ',
                   gateway_amount as 'البوابة',
                   riyal_mobile_amount as 'الريال موبايل',
                   (gateway_amount + riyal_mobile_amount) as 'الإجمالي'
            FROM collections
            WHERE agent_id = ? AND collection_date BETWEEN ? AND ?
            ORDER BY collection_date DESC
        ";
        $params = [$agent_id, $from_date, $to_date];
        break;
        
    default:
        die('نوع تقرير غير صحيح');
}

// تنفيذ الاستعلام
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إعداد headers للتحميل
header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

// بداية ملف Excel
echo "\xEF\xBB\xBF"; // UTF-8 BOM
echo '<table border="1">';

// العناوين
if ($results) {
    echo '<tr style="background-color: #4CAF50; color: white;">';
    foreach (array_keys($results[0]) as $header) {
        echo '<th>' . $header . '</th>';
    }
    echo '</tr>';
    
    // البيانات
    foreach ($results as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . $cell . '</td>';
        }
        echo '</tr>';
    }
}

echo '</table>';
?>

