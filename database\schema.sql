-- جدول الوكلاء
CREATE TABLE agents (
    agent_id INT PRIMARY KEY AUTO_INCREMENT,
    agent_name VARCHAR(100) NOT NULL
);

-- جدول التحصيلات
CREATE TABLE collections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    agent_id INT,
    gateway_amount DECIMAL(15,2) DEFAULT 0,
    riyal_mobile_amount DECIMAL(15,2) DEFAULT 0,
    collection_date DATE NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(agent_id)
);

-- إدراج بيانات تجريبية للوكلاء
INSERT INTO agents (agent_name) VALUES 
('الوكيل الأول'), ('الشرق'), ('جوث'), ('الشمال'), 
('سقف'), ('أبو أسامة'), ('الوسط'), ('الأثير'), 
('المريخ'), ('الزعيم');
