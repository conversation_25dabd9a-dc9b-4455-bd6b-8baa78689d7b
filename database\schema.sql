-- جدول المستخدمين
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    employee_name VARCHAR(100) NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    permissions ENUM('admin', 'user', 'viewer') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- جدول الوكلاء
CREATE TABLE agents (
    agent_id INT PRIMARY KEY AUTO_INCREMENT,
    agent_name VARCHAR(100) NOT NULL
);

-- جدول التحصيلات
CREATE TABLE collections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    agent_id INT,
    gateway_amount DECIMAL(15,2) DEFAULT 0,
    riyal_mobile_amount DECIMAL(15,2) DEFAULT 0,
    collection_date DATE NOT NULL,
    FOREIG<PERSON> KEY (agent_id) REFERENCES agents(agent_id)
);

-- إد<PERSON><PERSON><PERSON> مستخدم افتراضي (admin/admin123)
INSERT INTO users (employee_name, username, password, permissions) VALUES
('المدير العام', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('مستخدم تجريبي', 'user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user');

-- إدراج الوكلاء الحقيقيين
INSERT INTO agents (agent_name) VALUES
('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
('المترب'), ('باتكو'), ('بران');
