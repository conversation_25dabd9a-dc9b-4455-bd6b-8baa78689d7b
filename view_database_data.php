<?php
require_once 'config/database_sqlite.php';

try {
    echo "<h4>📊 بيانات قاعدة البيانات</h4>";
    
    // إحصائيات عامة
    $stats = $pdo->query("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN gateway_amount > 0 THEN 1 END) as gateway_records,
            COUNT(CASE WHEN riyal_mobile_amount > 0 THEN 1 END) as riyal_records,
            SUM(gateway_amount) as total_gateway,
            SUM(riyal_mobile_amount) as total_riyal,
            MIN(collection_date) as earliest_date,
            MAX(collection_date) as latest_date
        FROM collections
    ")->fetch();
    
    echo "<div class='alert alert-info'>";
    echo "<strong>📈 الإحصائيات العامة:</strong><br>";
    echo "• إجمالي السجلات: {$stats['total_records']}<br>";
    echo "• سجلات البوابة: {$stats['gateway_records']}<br>";
    echo "• سجلات ريال موبايل: {$stats['riyal_records']}<br>";
    echo "• إجمالي البوابة: " . number_format($stats['total_gateway'], 2) . " ريال<br>";
    echo "• إجمالي ريال موبايل: " . number_format($stats['total_riyal'], 2) . " ريال<br>";
    echo "• أقدم تاريخ: {$stats['earliest_date']}<br>";
    echo "• أحدث تاريخ: {$stats['latest_date']}";
    echo "</div>";
    
    if ($stats['total_records'] > 0) {
        // عرض البيانات مجمعة بالوكيل
        echo "<h5>👥 البيانات مجمعة بالوكيل:</h5>";
        
        $agent_data = $pdo->query("
            SELECT 
                a.agent_name,
                COUNT(c.id) as records_count,
                SUM(c.gateway_amount) as total_gateway,
                SUM(c.riyal_mobile_amount) as total_riyal,
                SUM(c.gateway_amount + c.riyal_mobile_amount) as grand_total
            FROM agents a
            LEFT JOIN collections c ON a.agent_id = c.agent_id
            GROUP BY a.agent_id, a.agent_name
            ORDER BY grand_total DESC
        ")->fetchAll();
        
        echo "<table class='table table-sm table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>الوكيل</th><th>عدد السجلات</th><th>إجمالي البوابة</th><th>إجمالي ريال موبايل</th><th>الإجمالي العام</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($agent_data as $row) {
            echo "<tr>";
            echo "<td><strong>{$row['agent_name']}</strong></td>";
            echo "<td>{$row['records_count']}</td>";
            echo "<td>" . number_format($row['total_gateway'], 2) . "</td>";
            echo "<td>" . number_format($row['total_riyal'], 2) . "</td>";
            echo "<td><strong>" . number_format($row['grand_total'], 2) . "</strong></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        // عرض البيانات مجمعة بالتاريخ
        echo "<h5>📅 البيانات مجمعة بالتاريخ:</h5>";
        
        $date_data = $pdo->query("
            SELECT 
                collection_date,
                COUNT(*) as records_count,
                SUM(gateway_amount) as date_gateway,
                SUM(riyal_mobile_amount) as date_riyal,
                SUM(gateway_amount + riyal_mobile_amount) as date_total
            FROM collections
            GROUP BY collection_date
            ORDER BY collection_date DESC
        ")->fetchAll();
        
        echo "<table class='table table-sm table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>التاريخ</th><th>عدد السجلات</th><th>إجمالي البوابة</th><th>إجمالي ريال موبايل</th><th>الإجمالي العام</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($date_data as $row) {
            echo "<tr>";
            echo "<td><strong>{$row['collection_date']}</strong></td>";
            echo "<td>{$row['records_count']}</td>";
            echo "<td>" . number_format($row['date_gateway'], 2) . "</td>";
            echo "<td>" . number_format($row['date_riyal'], 2) . "</td>";
            echo "<td><strong>" . number_format($row['date_total'], 2) . "</strong></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        // عرض عينة من البيانات التفصيلية
        echo "<h5>🔍 عينة من البيانات التفصيلية (آخر 20 سجل):</h5>";
        
        $sample_data = $pdo->query("
            SELECT 
                c.id,
                a.agent_name,
                c.collection_date,
                c.gateway_amount,
                c.riyal_mobile_amount,
                (c.gateway_amount + c.riyal_mobile_amount) as total
            FROM collections c
            JOIN agents a ON c.agent_id = a.agent_id
            ORDER BY c.collection_date DESC, a.agent_name
            LIMIT 20
        ")->fetchAll();
        
        echo "<table class='table table-sm table-hover'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>ID</th><th>الوكيل</th><th>التاريخ</th><th>البوابة</th><th>ريال موبايل</th><th>الإجمالي</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($sample_data as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['agent_name']}</td>";
            echo "<td>{$row['collection_date']}</td>";
            echo "<td>" . number_format($row['gateway_amount'], 2) . "</td>";
            echo "<td>" . number_format($row['riyal_mobile_amount'], 2) . "</td>";
            echo "<td><strong>" . number_format($row['total'], 2) . "</strong></td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<strong>⚠️ لا توجد بيانات في قاعدة البيانات</strong><br>";
        echo "استخدم أداة الاستيراد لإضافة البيانات الأولية.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<strong>❌ خطأ في قراءة قاعدة البيانات:</strong><br>";
    echo $e->getMessage();
    echo "</div>";
}
?>
