<?php
require_once 'config/database.php';

$from_date = $_GET['from'] ?? '';
$to_date = $_GET['to'] ?? '';

if ($from_date && $to_date) {
    $query = "
        SELECT 
            SUM(gateway_amount) as total_gateway,
            SUM(riyal_mobile_amount) as total_riyal,
            SUM(gateway_amount + riyal_mobile_amount) as grand_total
        FROM collections
        WHERE collection_date BETWEEN ? AND ?
    ";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$from_date, $to_date]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير حسب الفترة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">تقرير حسب الفترة</h2>
    
    <form method="GET" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <label>من تاريخ:</label>
                <input type="date" name="from" class="form-control" value="<?= $from_date ?>" required>
            </div>
            <div class="col-md-3">
                <label>إلى تاريخ:</label>
                <input type="date" name="to" class="form-control" value="<?= $to_date ?>" required>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">عرض التقرير</button>
            </div>
        </div>
    </form>
    
    <?php if (isset($result) && $from_date && $to_date): ?>
    <div class="card">
        <div class="card-header text-center">
            <h4>تقرير حسب الفترة من <?= $from_date ?> إلى <?= $to_date ?></h4>
            <button onclick="window.print()" class="btn btn-outline-primary btn-sm no-print">طباعة التقرير</button>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-lg">
                <tbody>
                    <tr class="table-info">
                        <th width="50%">إجمالي البوابة</th>
                        <td class="text-end"><?= number_format($result['total_gateway'], 2) ?></td>
                    </tr>
                    <tr class="table-warning">
                        <th>إجمالي الريال موبايل</th>
                        <td class="text-end"><?= number_format($result['total_riyal'], 2) ?></td>
                    </tr>
                    <tr class="table-success">
                        <th>الإجمالي العام</th>
                        <td class="text-end"><strong><?= number_format($result['grand_total'], 2) ?></strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="mt-3 no-print">
        <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
    </div>
</div>
</body>
</html>


