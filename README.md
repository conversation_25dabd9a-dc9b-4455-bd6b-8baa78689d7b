# نظام إدارة تحصيلات الوكلاء - الإصدار الجديد

## المميزات الجديدة
- **تصميم صفحة واحدة (SPA)**: جميع الوظائف في صفحة واحدة مع أيقونات في الرأس
- **نظام تسجيل دخول**: حماية النظام بأسماء مستخدمين وكلمات مرور
- **إدارة المستخدمين**: إضافة وحذف المستخدمين مع صلاحيات مختلفة
- **التقرير اليومي**: تقرير مخصص لتاريخ واحد فقط
- **خط عربي مخصص**: استخدام خط Khalid-Art من مجلد fonts
- **قاعدة بيانات SQLite**: سهولة في التشغيل بدون الحاجة لخادم MySQL

## المميزات الأساسية
- إدارة الوكلاء (إضافة/تعديل/حذف)
- إدخال التحصيلات اليومية
- تقارير متنوعة ومفصلة
- واجهة عربية متجاوبة مع تدرجات لونية جميلة
- إمكانية الطباعة والتصدير
- نسخ احتياطي للبيانات

## التقارير المتاحة (الصفحة الرئيسية)
1. **التقرير اليومي**: تقرير مفصل ليوم واحد محدد (الافتراضي)
2. **التقرير الأسبوعي**: تحصيلات فترة محددة (من - إلى)
3. **التقرير الشهري**: تحصيلات شهر محدد مع ترتيب الوكلاء
4. **تقرير الوكيل**: تقرير مخصص لوكيل واحد أو جميع الوكلاء
5. **التقرير المخصص**: تقارير متقدمة (ملخص، تفصيلي، مقارنة)

## الأقسام الأخرى
- **إضافة تحصيلات**: إدخال البيانات اليومية
- **إدارة الوكلاء**: إضافة وتعديل وحذف الوكلاء
- **لوحة المعلومات**: إحصائيات سريعة ومعلومات عامة
- **إدارة المستخدمين**: للمدير فقط
- **نسخ احتياطي**: حفظ واستعادة البيانات

## متطلبات التشغيل
- PHP 7.4+ (مع دعم SQLite)
- خادم ويب (Apache/Nginx أو PHP المدمج)

## التثبيت والتشغيل
1. تحميل الملفات في مجلد المشروع
2. تشغيل الخادم المحلي: `php -S localhost:7445`
3. فتح المتصفح على: `http://localhost:7445`
4. قاعدة البيانات ستُنشأ تلقائياً عند أول تشغيل

## بيانات تسجيل الدخول الافتراضية
- **المدير**:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`
  - الصلاحيات: مدير (إدارة كاملة)

- **مستخدم عادي**:
  - اسم المستخدم: `user1`
  - كلمة المرور: `admin123`
  - الصلاحيات: مستخدم (إدخال وعرض)

## الصلاحيات
- **مدير (admin)**: جميع الصلاحيات + إدارة المستخدمين + النسخ الاحتياطي
- **مستخدم (user)**: إدخال التحصيلات + عرض التقارير + إدارة الوكلاء
- **مشاهد (viewer)**: عرض التقارير فقط

## الاستخدام
1. ابدأ من `http://localhost:7445` (سيتم توجيهك لتسجيل الدخول)
2. سجل دخولك باستخدام البيانات الافتراضية
3. استخدم الأيقونات في الرأس للتنقل بين الأقسام
4. أضف الوكلاء من قسم "إدارة الوكلاء"
5. أدخل التحصيلات من قسم "إضافة تحصيلات"
6. اعرض التقارير من قسم "التقارير"

## الملفات المهمة
- `login.php`: صفحة تسجيل الدخول
- `main.php`: الصفحة الرئيسية (SPA)
- `config/database_sqlite.php`: إعدادات قاعدة البيانات
- `assets/style.css`: ملف التصميم مع الخط العربي
- `fonts/Khalid-Art-bold.ttf`: الخط العربي المخصص

## النسخ الاحتياطي
- يمكن إنشاء نسخ احتياطية من قسم "نسخ احتياطي"
- النسخ تُحفظ في مجلد `backups`
- يمكن تحميل النسخ الاحتياطية أو حذفها

## التصدير
- تصدير البيانات بصيغة CSV
- تصدير الوكلاء والتحصيلات والمستخدمين
- دعم اللغة العربية في ملفات Excel
