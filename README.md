# نظام إدارة تحصيلات الوكلاء

## المميزات
- إدارة الوكلاء (إضافة/حذف)
- إدخال التحصيلات اليومية
- تقارير متنوعة ومفصلة
- واجهة عربية متجاوبة
- إمكانية الطباعة

## التقارير المتاحة
1. **التقرير الأسبوعي التفصيلي**: عرض يومي لكل وكيل
2. **التقرير الأسبوعي الإجمالي**: إجماليات الوكلاء
3. **تقرير حسب الفترة**: إجماليات عامة
4. **تقرير تفصيلي للوكيل**: تقرير مخصص لوكيل واحد

## متطلبات التشغيل
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx

## التثبيت
1. إنشاء قاعدة بيانات `collections_system`
2. تشغيل ملف `database/schema.sql`
3. تعديل إعدادات قاعدة البيانات في `config/database.php`
4. رفع الملفات على الخادم

## الاستخدام
- ابدأ من `index.php` أو `reports.php`
- أضف الوكلاء من صفحة "إدارة الوكلاء"
- أدخل التحصيلات اليومية
- اعرض التقارير المختلفة
