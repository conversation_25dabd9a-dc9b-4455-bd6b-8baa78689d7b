<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>فحص حالة XAMPP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-server"></i> فحص حالة XAMPP و phpMyAdmin</h2>
        <p>فحص شامل لحالة الخدمات والروابط المتاحة</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5>🔍 فحص الخدمات</h5>
                </div>
                <div class="card-body">
                    <?php
                    echo "<h6>1. فحص Apache:</h6>";
                    
                    // فحص Apache على المنافذ المختلفة
                    $apache_ports = [80, 8080, 443, 8443];
                    $apache_running = false;
                    
                    foreach ($apache_ports as $port) {
                        $connection = @fsockopen('localhost', $port, $errno, $errstr, 1);
                        if ($connection) {
                            echo "<p class='text-success'><i class='fas fa-check'></i> Apache يعمل على المنفذ $port</p>";
                            $apache_running = true;
                            fclose($connection);
                        }
                    }
                    
                    if (!$apache_running) {
                        echo "<p class='text-danger'><i class='fas fa-times'></i> Apache لا يعمل على أي منفذ</p>";
                    }
                    
                    echo "<h6>2. فحص MySQL:</h6>";
                    
                    // فحص MySQL
                    $mysql_connection = @fsockopen('localhost', 3306, $errno, $errstr, 1);
                    if ($mysql_connection) {
                        echo "<p class='text-success'><i class='fas fa-check'></i> MySQL يعمل على المنفذ 3306</p>";
                        fclose($mysql_connection);
                        
                        // اختبار الاتصال بـ PDO
                        try {
                            $pdo = new PDO("mysql:host=localhost;port=3306", 'root', '');
                            echo "<p class='text-success'><i class='fas fa-check'></i> الاتصال بـ MySQL ناجح</p>";
                        } catch (Exception $e) {
                            echo "<p class='text-warning'><i class='fas fa-exclamation'></i> MySQL يعمل لكن الاتصال فشل: " . $e->getMessage() . "</p>";
                        }
                    } else {
                        echo "<p class='text-danger'><i class='fas fa-times'></i> MySQL لا يعمل</p>";
                    }
                    
                    echo "<h6>3. فحص امتدادات PHP:</h6>";
                    
                    $extensions = ['pdo_mysql', 'mysqli', 'curl'];
                    foreach ($extensions as $ext) {
                        if (extension_loaded($ext)) {
                            echo "<p class='text-success'><i class='fas fa-check'></i> امتداد $ext متاح</p>";
                        } else {
                            echo "<p class='text-danger'><i class='fas fa-times'></i> امتداد $ext غير متاح</p>";
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5>🔗 اختبار الروابط</h5>
                </div>
                <div class="card-body">
                    <h6>روابط phpMyAdmin المحتملة:</h6>
                    
                    <?php
                    $phpmyadmin_urls = [
                        'http://localhost/phpmyadmin/',
                        'http://localhost:8080/phpmyadmin/',
                        'http://localhost/phpMyAdmin/',
                        'http://localhost:8080/phpMyAdmin/',
                        'http://127.0.0.1/phpmyadmin/',
                        'http://127.0.0.1:8080/phpmyadmin/'
                    ];
                    
                    foreach ($phpmyadmin_urls as $url) {
                        echo "<div class='mb-2'>";
                        echo "<a href='$url' target='_blank' class='btn btn-outline-primary btn-sm'>$url</a>";
                        echo "</div>";
                    }
                    ?>
                    
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-lightbulb"></i> نصائح:</h6>
                        <ul class='mb-0'>
                            <li>جرب كل رابط أعلاه</li>
                            <li>تأكد من تشغيل Apache في XAMPP</li>
                            <li>أعد تشغيل XAMPP إذا لزم الأمر</li>
                            <li>تحقق من إعدادات Firewall</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5>🛠️ حلول بديلة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>1. إنشاء قاعدة البيانات مباشرة:</h6>
                            <button onclick="createMySQLDatabase()" class="btn btn-primary">
                                <i class="fas fa-database"></i> إنشاء قاعدة البيانات
                            </button>
                            <div id="create-result" class="mt-2"></div>
                        </div>
                        
                        <div class="col-md-4">
                            <h6>2. استخدام أداة التحويل:</h6>
                            <a href="migrate_to_mysql.php" class="btn btn-success">
                                <i class="fas fa-magic"></i> أداة التحويل
                            </a>
                        </div>
                        
                        <div class="col-md-4">
                            <h6>3. البقاء مع SQLite:</h6>
                            <a href="main.php" class="btn btn-secondary">
                                <i class="fas fa-database"></i> استخدام SQLite
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5>📋 تعليمات XAMPP</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>تشغيل XAMPP:</h6>
                            <ol>
                                <li>افتح XAMPP Control Panel</li>
                                <li>اضغط "Start" بجانب Apache</li>
                                <li>اضغط "Start" بجانب MySQL</li>
                                <li>تأكد من ظهور اللون الأخضر</li>
                                <li>جرب الروابط أعلاه</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>إذا لم يعمل XAMPP:</h6>
                            <ul>
                                <li>أعد تشغيل XAMPP كمدير (Run as Administrator)</li>
                                <li>تحقق من عدم استخدام المنافذ من برامج أخرى</li>
                                <li>أوقف Skype أو برامج أخرى تستخدم المنفذ 80</li>
                                <li>جرب تغيير منفذ Apache إلى 8080</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function createMySQLDatabase() {
    document.getElementById('create-result').innerHTML = '<div class="alert alert-info">🔄 جاري إنشاء قاعدة البيانات...</div>';
    
    fetch('create_mysql_direct.php')
    .then(response => response.text())
    .then(data => {
        document.getElementById('create-result').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('create-result').innerHTML = '<div class="alert alert-danger">خطأ: ' + error.message + '</div>';
    });
}
</script>

</body>
</html>
