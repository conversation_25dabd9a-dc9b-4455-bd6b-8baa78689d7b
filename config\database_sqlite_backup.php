<?php
// استخدام SQLite لسهولة التشغيل
$database_file = __DIR__ . '/../database/collections_system.db';

// إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
$database_dir = dirname($database_file);
if (!is_dir($database_dir)) {
    mkdir($database_dir, 0755, true);
}

try {
    $pdo = new PDO("sqlite:$database_file");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // إنشاء الجداول إذا لم تكن موجودة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_name TEXT NOT NULL,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            permissions TEXT DEFAULT 'user',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME NULL
        )
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS agents (
            agent_id INTEGER PRIMARY KEY AUTOINCREMENT,
            agent_name TEXT NOT NULL
        )
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS collections (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            agent_id INTEGER,
            gateway_amount DECIMAL(15,2) DEFAULT 0,
            riyal_mobile_amount DECIMAL(15,2) DEFAULT 0,
            collection_date DATE NOT NULL,
            FOREIGN KEY (agent_id) REFERENCES agents(agent_id)
        )
    ");

    // التحقق من وجود المستخدم الافتراضي
    $check_users = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch();
    if ($check_users['count'] == 0) {
        // إضافة المستخدم الافتراضي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $user_password = password_hash('admin123', PASSWORD_DEFAULT);

        $pdo->exec("
            INSERT INTO users (employee_name, username, password, permissions) VALUES
            ('المدير العام', 'admin', '$admin_password', 'admin'),
            ('مستخدم تجريبي', 'user1', '$user_password', 'user')
        ");
    }

    // التحقق من وجود الوكلاء
    $check_agents = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch();
    if ($check_agents['count'] == 0) {
        // إضافة الوكلاء الحقيقيين
        $pdo->exec("
            INSERT INTO agents (agent_name) VALUES
            ('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
            ('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
            ('المترب'), ('باتكو'), ('بران')
        ");
    }

} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}
?>
