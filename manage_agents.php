<?php
require_once 'config/database.php';

// معالجة إضافة وكيل جديد
if ($_POST && isset($_POST['add_agent'])) {
    $agent_name = trim($_POST['agent_name']);
    if (!empty($agent_name)) {
        $stmt = $pdo->prepare("INSERT INTO agents (agent_name) VALUES (?)");
        $stmt->execute([$agent_name]);
        $success_msg = "تم إضافة الوكيل بنجاح";
    }
}

// معالجة تعديل اسم الوكيل
if ($_POST && isset($_POST['edit_agent'])) {
    $agent_id = $_POST['agent_id'];
    $new_agent_name = trim($_POST['new_agent_name']);
    
    if (!empty($new_agent_name) && !empty($agent_id)) {
        $stmt = $pdo->prepare("UPDATE agents SET agent_name = ? WHERE agent_id = ?");
        $stmt->execute([$new_agent_name, $agent_id]);
        $success_msg = "تم تعديل اسم الوكيل بنجاح";
    }
}

// معالجة حذف وكيل
if ($_GET['delete'] ?? false) {
    $agent_id = $_GET['delete'];
    
    // التحقق من وجود تحصيلات للوكيل
    $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM collections WHERE agent_id = ?");
    $check_stmt->execute([$agent_id]);
    $collections_count = $check_stmt->fetchColumn();
    
    if ($collections_count > 0) {
        $error_msg = "لا يمكن حذف الوكيل لأنه يحتوي على تحصيلات مسجلة ($collections_count سجل)";
    } else {
        $stmt = $pdo->prepare("DELETE FROM agents WHERE agent_id = ?");
        $stmt->execute([$agent_id]);
        $success_msg = "تم حذف الوكيل بنجاح";
    }
}

// جلب قائمة الوكلاء مع عدد التحصيلات
$agents_query = $pdo->query("
    SELECT a.*, 
           COUNT(c.id) as collections_count,
           COALESCE(SUM(c.riyal_mobile_amount + c.gateway_amount), 0) as total_amount
    FROM agents a 
    LEFT JOIN collections c ON a.agent_id = c.agent_id 
    GROUP BY a.agent_id 
    ORDER BY a.agent_name
");
$agents = $agents_query->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الوكلاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">إدارة الوكلاء</h2>
    
    <?php if (isset($success_msg)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?= $success_msg ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>
    
    <?php if (isset($error_msg)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= $error_msg ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>
    
    <!-- إضافة وكيل جديد -->
    <div class="card mb-4">
        <div class="card-header">إضافة وكيل جديد</div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <input type="text" name="agent_name" class="form-control" placeholder="اسم الوكيل" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" name="add_agent" class="btn btn-primary">إضافة</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- قائمة الوكلاء -->
    <div class="card">
        <div class="card-header">قائمة الوكلاء (<?= count($agents) ?> وكيل)</div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الوكيل</th>
                            <th>اسم الوكيل</th>
                            <th>عدد التحصيلات</th>
                            <th>إجمالي المبلغ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($agents as $agent): ?>
                        <tr>
                            <td><?= $agent['agent_id'] ?></td>
                            <td>
                                <span id="name_<?= $agent['agent_id'] ?>"><?= $agent['agent_name'] ?></span>
                                <div id="edit_<?= $agent['agent_id'] ?>" style="display: none;">
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="agent_id" value="<?= $agent['agent_id'] ?>">
                                        <div class="input-group input-group-sm">
                                            <input type="text" name="new_agent_name" class="form-control" 
                                                   value="<?= $agent['agent_name'] ?>" required>
                                            <button type="submit" name="edit_agent" class="btn btn-success btn-sm">حفظ</button>
                                            <button type="button" class="btn btn-secondary btn-sm" 
                                                    onclick="cancelEdit(<?= $agent['agent_id'] ?>)">إلغاء</button>
                                        </div>
                                    </form>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= $agent['collections_count'] ?></span>
                            </td>
                            <td class="text-end">
                                <?= number_format($agent['total_amount'], 2) ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-warning" 
                                            onclick="editAgent(<?= $agent['agent_id'] ?>)">
                                        تعديل
                                    </button>
                                    
                                    <?php if ($agent['collections_count'] == 0): ?>
                                    <a href="?delete=<?= $agent['agent_id'] ?>" 
                                       class="btn btn-danger"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا الوكيل؟')">
                                        حذف
                                    </a>
                                    <?php else: ?>
                                    <button type="button" class="btn btn-danger" disabled 
                                            title="لا يمكن حذف الوكيل لوجود تحصيلات">
                                        حذف
                                    </button>
                                    <?php endif; ?>
                                    
                                    <a href="agent_report.php?agent_id=<?= $agent['agent_id'] ?>" 
                                       class="btn btn-info">
                                        تقرير
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="mt-3">
        <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
        <a href="add_collection.php" class="btn btn-primary">إضافة تحصيلات</a>
        <a href="bulk_edit_agents.php" class="btn btn-warning">تعديل متعدد</a>
    </div>
</div>

<script>
function editAgent(agentId) {
    document.getElementById('name_' + agentId).style.display = 'none';
    document.getElementById('edit_' + agentId).style.display = 'block';
}

function cancelEdit(agentId) {
    document.getElementById('name_' + agentId).style.display = 'block';
    document.getElementById('edit_' + agentId).style.display = 'none';
}
</script>
</body>
</html>

