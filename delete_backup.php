<?php
require_once 'auth.php';

// التحقق من صلاحيات المدير
checkPermission('admin');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $filename = $input['filename'] ?? '';
    
    if (empty($filename)) {
        echo json_encode(['success' => false, 'message' => 'اسم الملف مطلوب']);
        exit;
    }
    
    // التحقق من أن الملف في مجلد backups فقط
    $backup_file = 'backups/' . basename($filename);
    
    if (file_exists($backup_file) && pathinfo($backup_file, PATHINFO_EXTENSION) === 'sql') {
        if (unlink($backup_file)) {
            echo json_encode(['success' => true, 'message' => 'تم حذف الملف بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'خطأ في حذف الملف']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'الملف غير موجود']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
}
?>
