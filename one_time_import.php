<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>استيراد البيانات لمرة واحدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="alert alert-warning">
        <h4><i class="fas fa-exclamation-triangle"></i> تحذير: استيراد لمرة واحدة فقط</h4>
        <p>هذه الأداة مخصصة لاستيراد البيانات الأولية لمرة واحدة فقط. بعد الاستيراد، استخدم نظام إدخال البيانات العادي.</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-database"></i> حالة قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-info btn-sm" onclick="checkDatabase()">
                        <i class="fas fa-search"></i> فحص قاعدة البيانات
                    </button>
                    <div id="db-status" class="mt-3"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-upload"></i> استيراد البيانات</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-danger btn-sm mb-2" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button><br>
                    <button class="btn btn-primary btn-sm mb-2" onclick="importData('gateway')">
                        <i class="fas fa-building"></i> استيراد البوابة
                    </button><br>
                    <button class="btn btn-info btn-sm" onclick="importData('riyal')">
                        <i class="fas fa-mobile-alt"></i> استيراد ريال موبايل
                    </button>
                    <div id="import-status" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-table"></i> عرض البيانات المستوردة</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-secondary btn-sm" onclick="viewData()">
                        <i class="fas fa-eye"></i> عرض البيانات
                    </button>
                    <div id="data-view" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <a href="main.php" class="btn btn-success">
            <i class="fas fa-home"></i> العودة للصفحة الرئيسية
        </a>
        <button class="btn btn-warning" onclick="runCompleteImport()">
            <i class="fas fa-play"></i> تشغيل استيراد كامل
        </button>
    </div>
</div>

<script>
function checkDatabase() {
    fetch('check_import_results.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('db-status').innerHTML = `
                <div class="alert alert-info">
                    <strong>📊 حالة قاعدة البيانات:</strong><br>
                    • إجمالي السجلات: ${data.total_records}<br>
                    • سجلات البوابة: ${data.gateway_records}<br>
                    • سجلات ريال موبايل: ${data.riyal_records}<br>
                    • إجمالي البوابة: ${data.total_gateway}<br>
                    • إجمالي ريال موبايل: ${data.total_riyal}<br>
                    • آخر تحديث: ${data.last_update}
                </div>
            `;
        } else {
            document.getElementById('db-status').innerHTML = `
                <div class="alert alert-danger">خطأ: ${data.error}</div>
            `;
        }
    });
}

function clearAllData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        fetch('clear_collections.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({action: 'clear_all'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showImportStatus('✅ تم مسح جميع البيانات', 'success');
                checkDatabase();
            } else {
                showImportStatus('❌ فشل في مسح البيانات: ' + data.error, 'danger');
            }
        });
    }
}

function importData(type) {
    const typeName = type === 'gateway' ? 'البوابة' : 'ريال موبايل';
    showImportStatus(`🔄 جاري استيراد ${typeName}...`, 'info');
    
    fetch('process_csv_import.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'type=' + type
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const message = `✅ ${typeName}: ${data.imported} جديد، ${data.updated} محدث، العدد النهائي: ${data.final_count}`;
            showImportStatus(message, 'success');
            checkDatabase();
        } else {
            showImportStatus(`❌ فشل استيراد ${typeName}: ${data.error}`, 'danger');
        }
    })
    .catch(error => {
        showImportStatus(`❌ خطأ في استيراد ${typeName}: ${error.message}`, 'danger');
    });
}

function viewData() {
    fetch('view_database_data.php')
    .then(response => response.text())
    .then(data => {
        document.getElementById('data-view').innerHTML = data;
    })
    .catch(error => {
        document.getElementById('data-view').innerHTML = `
            <div class="alert alert-danger">خطأ في عرض البيانات: ${error.message}</div>
        `;
    });
}

function runCompleteImport() {
    if (confirm('هل تريد تشغيل استيراد كامل؟ سيتم مسح البيانات الموجودة واستيراد البيانات الجديدة.')) {
        showImportStatus('🚀 بدء الاستيراد الكامل...', 'info');
        
        // مسح البيانات
        fetch('clear_collections.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({action: 'clear_all'})
        })
        .then(() => {
            showImportStatus('1️⃣ تم مسح البيانات القديمة', 'info');
            return new Promise(resolve => setTimeout(resolve, 1000));
        })
        .then(() => {
            // استيراد البوابة
            showImportStatus('2️⃣ استيراد بيانات البوابة...', 'info');
            return fetch('process_csv_import.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'type=gateway'
            });
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showImportStatus(`✅ البوابة: ${data.imported} سجل مستورد`, 'success');
                return new Promise(resolve => setTimeout(resolve, 1000));
            } else {
                throw new Error('فشل استيراد البوابة: ' + data.error);
            }
        })
        .then(() => {
            // استيراد ريال موبايل
            showImportStatus('3️⃣ استيراد بيانات ريال موبايل...', 'info');
            return fetch('process_csv_import.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'type=riyal'
            });
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showImportStatus(`✅ ريال موبايل: ${data.updated} سجل محدث`, 'success');
                return new Promise(resolve => setTimeout(resolve, 1000));
            } else {
                throw new Error('فشل استيراد ريال موبايل: ' + data.error);
            }
        })
        .then(() => {
            showImportStatus('🎉 تم الاستيراد الكامل بنجاح!', 'success');
            checkDatabase();
            viewData();
        })
        .catch(error => {
            showImportStatus('❌ فشل الاستيراد الكامل: ' + error.message, 'danger');
        });
    }
}

function showImportStatus(message, type) {
    const alertClass = {
        'success': 'alert-success',
        'danger': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    document.getElementById('import-status').innerHTML = `
        <div class="alert ${alertClass[type]}">
            ${message}
        </div>
    `;
}

// فحص تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkDatabase();
});
</script>

</body>
</html>
