<?php
// قسم التقارير
?>

<!-- عرض التواريخ المتاحة -->
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-calendar-alt"></i> التواريخ المتاحة في قاعدة البيانات:</h6>
            <div id="available-dates">جاري التحميل...</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- التقرير الأسبوعي -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-calendar-week"></i> التقرير الأسبوعي</h5>
            </div>
            <div class="card-body">
                <p>عرض تحصيلات الأسبوع الحالي أو أسبوع محدد</p>
                <form id="weeklyReportForm">
                    <div class="mb-3">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" id="weekly-start" class="form-control" value="<?= date('Y-m-d', strtotime('monday this week')) ?>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" id="weekly-end" class="form-control" value="<?= date('Y-m-d', strtotime('sunday this week')) ?>">
                    </div>
                    <button type="button" class="btn btn-primary" onclick="generateWeeklyReport()">
                        <i class="fas fa-chart-line"></i> إنشاء التقرير
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- التقرير الشهري -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-calendar-alt"></i> التقرير الشهري</h5>
            </div>
            <div class="card-body">
                <p>عرض تحصيلات الشهر الحالي أو شهر محدد</p>
                <form id="monthlyReportForm">
                    <div class="mb-3">
                        <label class="form-label">الشهر والسنة:</label>
                        <input type="month" id="monthly-date" class="form-control" value="<?= date('Y-m') ?>">
                    </div>
                    <button type="button" class="btn btn-success" onclick="generateMonthlyReport()">
                        <i class="fas fa-chart-bar"></i> إنشاء التقرير
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- تقرير حسب الوكيل -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-user-chart"></i> تقرير الوكيل</h5>
            </div>
            <div class="card-body">
                <p>عرض تحصيلات وكيل محدد خلال فترة معينة</p>
                <form id="agentReportForm">
                    <div class="mb-3">
                        <label class="form-label">اختر الوكيل:</label>
                        <select id="agent-select" class="form-control">
                            <option value="">جميع الوكلاء</option>
                            <?php foreach ($agents as $agent): ?>
                            <option value="<?= $agent['agent_id'] ?>"><?= htmlspecialchars($agent['agent_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">من:</label>
                            <input type="date" id="agent-start" class="form-control" value="<?= date('Y-m-01') ?>">
                        </div>
                        <div class="col-6">
                            <label class="form-label">إلى:</label>
                            <input type="date" id="agent-end" class="form-control" value="<?= date('Y-m-d') ?>">
                        </div>
                    </div>
                    <button type="button" class="btn btn-info mt-3" onclick="generateAgentReport()">
                        <i class="fas fa-user"></i> إنشاء التقرير
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- تقرير مخصص -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-white">
                <h5><i class="fas fa-cogs"></i> تقرير مخصص</h5>
            </div>
            <div class="card-body">
                <p>إنشاء تقرير مخصص حسب معايير محددة</p>
                <form id="customReportForm">
                    <div class="mb-3">
                        <label class="form-label">نوع التقرير:</label>
                        <select id="report-type" class="form-control">
                            <option value="summary">ملخص إجمالي</option>
                            <option value="detailed">تفصيلي</option>
                            <option value="comparison">مقارنة</option>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">من:</label>
                            <input type="date" id="custom-start" class="form-control" value="<?= date('Y-m-01') ?>">
                        </div>
                        <div class="col-6">
                            <label class="form-label">إلى:</label>
                            <input type="date" id="custom-end" class="form-control" value="<?= date('Y-m-d') ?>">
                        </div>
                    </div>
                    <button type="button" class="btn btn-warning mt-3" onclick="generateCustomReport()">
                        <i class="fas fa-magic"></i> إنشاء التقرير
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- منطقة عرض التقارير -->
<div id="report-display" class="mt-4" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 id="report-title"><i class="fas fa-chart-line"></i> نتائج التقرير</h5>
            <div>
                <button class="btn btn-sm btn-primary" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-sm btn-success" onclick="exportReport()">
                    <i class="fas fa-file-excel"></i> تصدير
                </button>
                <button class="btn btn-sm btn-secondary" onclick="closeReport()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="report-content">
                <!-- محتوى التقرير سيظهر هنا -->
            </div>
        </div>
    </div>
</div>

<script>
// إنشاء التقرير الأسبوعي
function generateWeeklyReport() {
    const startDate = document.getElementById('weekly-start').value;
    const endDate = document.getElementById('weekly-end').value;

    if (!startDate || !endDate) {
        alert('يرجى تحديد التواريخ');
        return;
    }

    showReport('التقرير الأسبوعي', `weekly_report.php?start=${startDate}&end=${endDate}`);
}

// إنشاء التقرير الشهري
function generateMonthlyReport() {
    const monthDate = document.getElementById('monthly-date').value;

    if (!monthDate) {
        alert('يرجى تحديد الشهر');
        return;
    }

    showReport('التقرير الشهري', `monthly_report.php?month=${monthDate}`);
}

// إنشاء تقرير الوكيل
function generateAgentReport() {
    const agentId = document.getElementById('agent-select').value;
    const startDate = document.getElementById('agent-start').value;
    const endDate = document.getElementById('agent-end').value;

    if (!startDate || !endDate) {
        alert('يرجى تحديد التواريخ');
        return;
    }

    const agentName = agentId ? document.querySelector(`#agent-select option[value="${agentId}"]`).text : 'جميع الوكلاء';
    showReport(`تقرير ${agentName}`, `agent_report.php?agent=${agentId}&start=${startDate}&end=${endDate}`);
}

// إنشاء التقرير المخصص
function generateCustomReport() {
    const reportType = document.getElementById('report-type').value;
    const startDate = document.getElementById('custom-start').value;
    const endDate = document.getElementById('custom-end').value;

    if (!startDate || !endDate) {
        alert('يرجى تحديد التواريخ');
        return;
    }

    showReport('التقرير المخصص', `custom_report.php?type=${reportType}&start=${startDate}&end=${endDate}`);
}

// عرض التقرير
function showReport(title, url) {
    document.getElementById('report-title').innerHTML = `<i class="fas fa-chart-line"></i> ${title}`;
    document.getElementById('report-content').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تحميل التقرير...</div>';
    document.getElementById('report-display').style.display = 'block';

    fetch(url)
        .then(response => response.text())
        .then(data => {
            document.getElementById('report-content').innerHTML = data;
        })
        .catch(error => {
            document.getElementById('report-content').innerHTML = '<div class="alert alert-danger">خطأ في تحميل التقرير</div>';
        });
}

// إغلاق التقرير
function closeReport() {
    document.getElementById('report-display').style.display = 'none';
}

// طباعة التقرير
function printReport() {
    const reportContent = document.getElementById('report-content').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>طباعة التقرير</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="assets/style.css" rel="stylesheet">
        </head>
        <body onload="window.print(); window.close();">
            ${reportContent}
        </body>
        </html>
    `);
}

// تصدير التقرير
function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// تحميل التواريخ المتاحة
function loadAvailableDates() {
    fetch('get_available_dates.php')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.dates.length > 0) {
            let datesHtml = '<div class="d-flex flex-wrap gap-2">';
            data.dates.forEach(dateInfo => {
                const isToday = dateInfo.date === new Date().toISOString().split('T')[0];
                const badgeClass = isToday ? 'bg-success' : 'bg-primary';
                const totalAmount = parseFloat(dateInfo.total_amount || 0);
                datesHtml += `
                    <span class="badge ${badgeClass}" style="cursor: pointer; font-size: 0.9em;"
                          onclick="setDateAndGenerate('${dateInfo.date}')"
                          title="إجمالي: ${totalAmount.toLocaleString('ar-SA')} ريال">
                        📅 ${dateInfo.date}<br>
                        <small>${dateInfo.count} سجل</small>
                    </span>
                `;
            });
            datesHtml += '</div>';
            datesHtml += `<p class="mt-2 mb-0"><small class="text-muted">💡 انقر على أي تاريخ لعرض تقريره</small></p>`;
            document.getElementById('available-dates').innerHTML = datesHtml;
        } else {
            document.getElementById('available-dates').innerHTML =
                '<span class="text-warning">لا توجد بيانات متاحة</span>';
        }
    })
    .catch(error => {
        document.getElementById('available-dates').innerHTML =
            '<span class="text-danger">خطأ في تحميل التواريخ</span>';
    });
}

// تعيين التاريخ وإنشاء التقرير
function setDateAndGenerate(date) {
    document.getElementById('daily-date').value = date;
    generateDailyReport();
}

// تحميل التقرير اليومي والتواريخ المتاحة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableDates();
    generateDailyReport();
});
</script>
