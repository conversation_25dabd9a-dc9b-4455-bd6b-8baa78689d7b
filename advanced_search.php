<?php
require_once 'config/database.php';

$agent_id = $_GET['agent_id'] ?? '';
$from_date = $_GET['from_date'] ?? '';
$to_date = $_GET['to_date'] ?? '';
$min_amount = $_GET['min_amount'] ?? '';
$max_amount = $_GET['max_amount'] ?? '';

// جلب قائمة الوكلاء
$agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll(PDO::FETCH_ASSOC);

$results = [];
if ($_GET) {
    $conditions = [];
    $params = [];
    
    if ($agent_id) {
        $conditions[] = "c.agent_id = ?";
        $params[] = $agent_id;
    }
    
    if ($from_date) {
        $conditions[] = "c.collection_date >= ?";
        $params[] = $from_date;
    }
    
    if ($to_date) {
        $conditions[] = "c.collection_date <= ?";
        $params[] = $to_date;
    }
    
    $having_conditions = [];
    if ($min_amount) {
        $having_conditions[] = "total_amount >= ?";
        $params[] = $min_amount;
    }
    
    if ($max_amount) {
        $having_conditions[] = "total_amount <= ?";
        $params[] = $max_amount;
    }
    
    $where_clause = $conditions ? "WHERE " . implode(" AND ", $conditions) : "";
    $having_clause = $having_conditions ? "HAVING " . implode(" AND ", $having_conditions) : "";
    
    $query = "
        SELECT 
            a.agent_name,
            c.collection_date,
            c.gateway_amount,
            c.riyal_mobile_amount,
            (c.gateway_amount + c.riyal_mobile_amount) as total_amount
        FROM collections c
        JOIN agents a ON c.agent_id = a.agent_id
        $where_clause
        $having_clause
        ORDER BY c.collection_date DESC, a.agent_name
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>البحث المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2 class="text-center mb-4">البحث المتقدم في التحصيلات</h2>
    
    <div class="card mb-4">
        <div class="card-header">معايير البحث</div>
        <div class="card-body">
            <form method="GET">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label>الوكيل:</label>
                        <select name="agent_id" class="form-control">
                            <option value="">جميع الوكلاء</option>
                            <?php foreach ($agents as $agent): ?>
                            <option value="<?= $agent['agent_id'] ?>" <?= $agent_id == $agent['agent_id'] ? 'selected' : '' ?>>
                                <?= $agent['agent_name'] ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label>من تاريخ:</label>
                        <input type="date" name="from_date" class="form-control" value="<?= $from_date ?>">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label>إلى تاريخ:</label>
                        <input type="date" name="to_date" class="form-control" value="<?= $to_date ?>">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label>الحد الأدنى للمبلغ:</label>
                        <input type="number" step="0.01" name="min_amount" class="form-control" value="<?= $min_amount ?>">
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label>الحد الأقصى للمبلغ:</label>
                        <input type="number" step="0.01" name="max_amount" class="form-control" value="<?= $max_amount ?>">
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end mb-3">
                        <button type="submit" class="btn btn-primary me-2">بحث</button>
                        <a href="advanced_search.php" class="btn btn-secondary">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <?php if ($results): ?>
    <div class="card">
        <div class="card-header">
            نتائج البحث (<?= count($results) ?> نتيجة)
            <button onclick="window.print()" class="btn btn-outline-primary btn-sm float-end no-print">طباعة</button>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>الوكيل</th>
                        <th>التاريخ</th>
                        <th>البوابة</th>
                        <th>الريال موبايل</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_gateway = 0;
                    $total_riyal = 0;
                    $grand_total = 0;
                    
                    foreach ($results as $row): 
                        $total_gateway += $row['gateway_amount'];
                        $total_riyal += $row['riyal_mobile_amount'];
                        $grand_total += $row['total_amount'];
                    ?>
                    <tr>
                        <td><?= $row['agent_name'] ?></td>
                        <td><?= $row['collection_date'] ?></td>
                        <td><?= number_format($row['gateway_amount'], 2) ?></td>
                        <td><?= number_format($row['riyal_mobile_amount'], 2) ?></td>
                        <td><?= number_format($row['total_amount'], 2) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-success">
                    <tr>
                        <th colspan="2">الإجمالي</th>
                        <th><?= number_format($total_gateway, 2) ?></th>
                        <th><?= number_format($total_riyal, 2) ?></th>
                        <th><?= number_format($grand_total, 2) ?></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <?php elseif ($_GET): ?>
    <div class="alert alert-info">لم يتم العثور على نتائج تطابق معايير البحث</div>
    <?php endif; ?>
    
    <div class="mt-3 no-print">
        <a href="reports.php" class="btn btn-secondary">العودة للتقارير</a>
    </div>
</div>
</body>
</html>

