<?php
require_once 'auth.php';
require_once 'config/database.php';

$user = getCurrentUser();

// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    logout();
}

// جلب بيانات الوكلاء
$agents_stmt = $pdo->query("SELECT * FROM agents ORDER BY agent_name");
$agents = $agents_stmt->fetchAll();

// جلب إحصائيات سريعة
$today = date('Y-m-d');
$this_month = date('Y-m');

// إجمالي اليوم
$today_stmt = $pdo->prepare("
    SELECT
        SUM(gateway_amount) as total_gateway,
        SUM(riyal_mobile_amount) as total_riyal,
        COUNT(DISTINCT agent_id) as active_agents
    FROM collections
    WHERE collection_date = ?
");
$today_stmt->execute([$today]);
$today_stats = $today_stmt->fetch();

// إجمالي الشهر
$month_stmt = $pdo->prepare("
    SELECT
        SUM(gateway_amount) as total_gateway,
        SUM(riyal_mobile_amount) as total_riyal
    FROM collections
    WHERE DATE_FORMAT(collection_date, '%Y-%m') = ?
");
$month_stmt->execute([$this_month]);
$month_stats = $month_stmt->fetch();
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة تحصيلات الوكلاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <div class="main-container">
        <!-- شريط الأيقونات العلوي -->
        <div class="top-navigation">
            <div class="nav-icons">
                <a href="#" class="nav-icon active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i><br>لوحة المعلومات
                </a>
                <a href="#" class="nav-icon" onclick="showSection('daily-report')">
                    <i class="fas fa-calendar-day"></i><br>التقرير اليومي
                </a>
                <a href="#" class="nav-icon" onclick="showSection('add-collection')">
                    <i class="fas fa-plus-circle"></i><br>إضافة تحصيلات
                </a>
                <a href="#" class="nav-icon" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar"></i><br>التقارير
                </a>
                <a href="#" class="nav-icon" onclick="showSection('agents')">
                    <i class="fas fa-users"></i><br>إدارة الوكلاء
                </a>
                <?php if ($user['permissions'] == 'admin'): ?>
                <a href="#" class="nav-icon" onclick="showSection('users')">
                    <i class="fas fa-user-cog"></i><br>إدارة المستخدمين
                </a>
                <?php endif; ?>
                <a href="#" class="nav-icon" onclick="showSection('backup')">
                    <i class="fas fa-download"></i><br>نسخ احتياطي
                </a>
                <a href="?logout=1" class="nav-icon" onclick="return confirm('هل تريد تسجيل الخروج؟')">
                    <i class="fas fa-sign-out-alt"></i><br>تسجيل الخروج
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- ترحيب بالمستخدم -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <h4>مرحباً <?= htmlspecialchars($user['employee_name']) ?></h4>
                        <p class="mb-0">الصلاحيات: <?= $user['permissions'] == 'admin' ? 'مدير' : ($user['permissions'] == 'user' ? 'مستخدم' : 'مشاهد') ?></p>
                    </div>
                </div>
            </div>

            <!-- لوحة المعلومات -->
            <div id="dashboard" class="content-section active">
                <h3 class="mb-4"><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h3>

                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">تحصيلات اليوم</h5>
                                <h3 class="text-success"><?= number_format(($today_stats['total_gateway'] ?? 0) + ($today_stats['total_riyal'] ?? 0), 2) ?></h3>
                                <small class="text-muted">ريال سعودي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">تحصيلات الشهر</h5>
                                <h3 class="text-primary"><?= number_format(($month_stats['total_gateway'] ?? 0) + ($month_stats['total_riyal'] ?? 0), 2) ?></h3>
                                <small class="text-muted">ريال سعودي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">الوكلاء النشطون اليوم</h5>
                                <h3 class="text-warning"><?= $today_stats['active_agents'] ?? 0 ?></h3>
                                <small class="text-muted">من أصل <?= count($agents) ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-secondary">إجمالي الوكلاء</h5>
                                <h3 class="text-secondary"><?= count($agents) ?></h3>
                                <small class="text-muted">وكيل مسجل</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التقرير اليومي -->
            <div id="daily-report" class="content-section">
                <h3 class="mb-4"><i class="fas fa-calendar-day"></i> التقرير اليومي</h3>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">اختر التاريخ:</label>
                        <input type="date" id="daily-date" class="form-control" value="<?= date('Y-m-d') ?>" onchange="loadDailyReport()">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button class="btn btn-primary" onclick="loadDailyReport()">
                            <i class="fas fa-search"></i> عرض التقرير
                        </button>
                    </div>
                </div>

                <div id="daily-report-content">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>

            <!-- إضافة التحصيلات -->
            <div id="add-collection" class="content-section">
                <h3 class="mb-4"><i class="fas fa-plus-circle"></i> إضافة تحصيلات</h3>
                <div id="add-collection-content">
                    <?php include 'add_collection_section.php'; ?>
                </div>
            </div>

            <div id="reports" class="content-section">
                <h3 class="mb-4"><i class="fas fa-chart-bar"></i> التقارير</h3>
                <div id="reports-content">
                    <?php include 'reports_section.php'; ?>
                </div>
            </div>

            <div id="agents" class="content-section">
                <h3 class="mb-4"><i class="fas fa-users"></i> إدارة الوكلاء</h3>
                <div id="agents-content">
                    <?php include 'agents_management.php'; ?>
                </div>
            </div>

            <?php if ($user['permissions'] == 'admin'): ?>
            <div id="users" class="content-section">
                <h3 class="mb-4"><i class="fas fa-user-cog"></i> إدارة المستخدمين</h3>
                <div id="users-content">
                    <?php include 'users_management.php'; ?>
                </div>
            </div>
            <?php endif; ?>

            <div id="backup" class="content-section">
                <h3 class="mb-4"><i class="fas fa-download"></i> نسخ احتياطي</h3>
                <div id="backup-content">
                    <?php include 'backup_section.php'; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إظهار القسم المحدد
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع الأيقونات
            document.querySelectorAll('.nav-icon').forEach(icon => {
                icon.classList.remove('active');
            });

            // إظهار القسم المحدد
            document.getElementById(sectionId).classList.add('active');

            // إضافة الفئة النشطة للأيقونة المحددة
            event.target.closest('.nav-icon').classList.add('active');
        }

        // تحميل التقرير اليومي
        function loadDailyReport() {
            const date = document.getElementById('daily-date').value;
            const content = document.getElementById('daily-report-content');

            content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

            fetch(`daily_report_ajax.php?date=${date}`)
                .then(response => response.text())
                .then(data => {
                    content.innerHTML = data;
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-danger">خطأ في تحميل التقرير</div>';
                });
        }

        // تحميل التقرير اليومي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('daily-date')) {
                loadDailyReport();
            }
        });
    </script>
</body>
</html>
