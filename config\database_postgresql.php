<?php
// إعدادات قاعدة بيانات PostgreSQL
$postgresql_config = [
    'host' => 'localhost',
    'port' => '5432',
    'dbname' => 'agent',
    'username' => 'postgres',
    'password' => 'yemen123',
    'charset' => 'utf8'
];

try {
    // الاتصال بـ PostgreSQL
    $dsn = "pgsql:host={$postgresql_config['host']};port={$postgresql_config['port']};dbname={$postgresql_config['dbname']};options='--client_encoding={$postgresql_config['charset']}'";
    
    $pdo = new PDO(
        $dsn,
        $postgresql_config['username'],
        $postgresql_config['password']
    );
    
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // إنشاء الجداول إذا لم تكن موجودة
    
    // جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id SERIAL PRIMARY KEY,
            employee_name VARCHAR(255) NOT NULL,
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            permissions VARCHAR(20) DEFAULT 'user' CHECK (permissions IN ('admin', 'user')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        )
    ");
    
    // جدول الوكلاء
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS agents (
            agent_id SERIAL PRIMARY KEY,
            agent_name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // جدول التحصيلات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS collections (
            id SERIAL PRIMARY KEY,
            agent_id INTEGER REFERENCES agents(agent_id) ON DELETE CASCADE,
            gateway_amount DECIMAL(15,2) DEFAULT 0.00,
            riyal_mobile_amount DECIMAL(15,2) DEFAULT 0.00,
            collection_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    // إنشاء فهارس لتحسين الأداء
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_collections_agent_date ON collections(agent_id, collection_date)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_collections_date ON collections(collection_date)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)");
    $pdo->exec("CREATE INDEX IF NOT EXISTS idx_agents_name ON agents(agent_name)");
    
    // إنشاء trigger لتحديث updated_at تلقائياً
    $pdo->exec("
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql'
    ");
    
    $pdo->exec("
        DROP TRIGGER IF EXISTS update_collections_updated_at ON collections;
        CREATE TRIGGER update_collections_updated_at
            BEFORE UPDATE ON collections
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column()
    ");
    
    // التحقق من وجود البيانات الأساسية وإضافتها
    
    // إضافة المستخدمين الافتراضيين
    $user_count = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    if ($user_count == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $user_password = password_hash('user123', PASSWORD_DEFAULT);
        
        $pdo->exec("
            INSERT INTO users (employee_name, username, password, permissions) VALUES
            ('المدير العام', 'admin', '$admin_password', 'admin'),
            ('مستخدم تجريبي', 'user1', '$user_password', 'user')
        ");
    }
    
    // إضافة الوكلاء الافتراضيين
    $agents_count = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
    if ($agents_count == 0) {
        $pdo->exec("
            INSERT INTO agents (agent_name) VALUES
            ('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
            ('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
            ('المترب'), ('باتكو'), ('بران')
        ");
    }
    
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات PostgreSQL: " . $e->getMessage());
}
?>
