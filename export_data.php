<?php
require_once 'auth.php';
require_once 'config/database.php';

$type = $_GET['type'] ?? '';

if (empty($type)) {
    die('نوع التصدير غير محدد');
}

// تحديد نوع المحتوى
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $type . '_' . date('Y-m-d') . '.csv"');

// إضافة BOM للدعم العربي في Excel
echo "\xEF\xBB\xBF";

$output = fopen('php://output', 'w');

try {
    switch ($type) {
        case 'agents':
            // تصدير الوكلاء
            fputcsv($output, ['رقم الوكيل', 'اسم الوكيل', 'عدد التحصيلات', 'إجمالي المبلغ']);
            
            $stmt = $pdo->query("
                SELECT 
                    a.agent_id,
                    a.agent_name,
                    COUNT(c.id) as total_collections,
                    SUM(c.gateway_amount + c.riyal_mobile_amount) as total_amount
                FROM agents a
                LEFT JOIN collections c ON a.agent_id = c.agent_id
                GROUP BY a.agent_id, a.agent_name
                ORDER BY a.agent_name
            ");
            
            while ($row = $stmt->fetch()) {
                fputcsv($output, [
                    $row['agent_id'],
                    $row['agent_name'],
                    $row['total_collections'],
                    number_format($row['total_amount'] ?? 0, 2)
                ]);
            }
            break;
            
        case 'collections':
            // تصدير التحصيلات
            fputcsv($output, ['التاريخ', 'اسم الوكيل', 'جيت واي', 'ريال موبايل', 'الإجمالي']);
            
            $stmt = $pdo->query("
                SELECT 
                    c.collection_date,
                    a.agent_name,
                    c.gateway_amount,
                    c.riyal_mobile_amount,
                    (c.gateway_amount + c.riyal_mobile_amount) as total
                FROM collections c
                JOIN agents a ON c.agent_id = a.agent_id
                ORDER BY c.collection_date DESC, a.agent_name
            ");
            
            while ($row = $stmt->fetch()) {
                fputcsv($output, [
                    $row['collection_date'],
                    $row['agent_name'],
                    number_format($row['gateway_amount'], 2),
                    number_format($row['riyal_mobile_amount'], 2),
                    number_format($row['total'], 2)
                ]);
            }
            break;
            
        case 'users':
            // تصدير المستخدمين (للمدير فقط)
            checkPermission('admin');
            
            fputcsv($output, ['رقم المستخدم', 'اسم الموظف', 'اسم المستخدم', 'الصلاحيات', 'تاريخ الإنشاء', 'آخر دخول']);
            
            $stmt = $pdo->query("
                SELECT 
                    user_id,
                    employee_name,
                    username,
                    permissions,
                    created_at,
                    last_login
                FROM users
                ORDER BY created_at DESC
            ");
            
            while ($row = $stmt->fetch()) {
                fputcsv($output, [
                    $row['user_id'],
                    $row['employee_name'],
                    $row['username'],
                    $row['permissions'],
                    $row['created_at'],
                    $row['last_login'] ?? 'لم يسجل دخول'
                ]);
            }
            break;
            
        default:
            die('نوع تصدير غير مدعوم');
    }
    
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}

fclose($output);
?>
