<?php
try {
    // محاولة الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=localhost;port=3306", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-success'>";
    echo "<h6>✅ تم الاتصال بـ MySQL بنجاح!</h6>";
    echo "</div>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `collections_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: collections_system</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء الجداول
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `user_id` INT AUTO_INCREMENT PRIMARY KEY,
            `employee_name` VARCHAR(255) NOT NULL,
            `username` VARCHAR(100) UNIQUE NOT NULL,
            `password` VARCHAR(255) NOT NULL,
            `permissions` ENUM('admin', 'user') DEFAULT 'user',
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `last_login` TIMESTAMP NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ تم إنشاء جدول المستخدمين</p>";
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `agents` (
            `agent_id` INT AUTO_INCREMENT PRIMARY KEY,
            `agent_name` VARCHAR(255) NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ تم إنشاء جدول الوكلاء</p>";
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `collections` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `agent_id` INT,
            `gateway_amount` DECIMAL(15,2) DEFAULT 0.00,
            `riyal_mobile_amount` DECIMAL(15,2) DEFAULT 0.00,
            `collection_date` DATE NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX `idx_agent_date` (`agent_id`, `collection_date`),
            INDEX `idx_collection_date` (`collection_date`),
            FOREIGN KEY (`agent_id`) REFERENCES `agents`(`agent_id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ تم إنشاء جدول التحصيلات</p>";
    
    // إضافة البيانات الافتراضية
    $user_count = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    if ($user_count == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $user_password = password_hash('user123', PASSWORD_DEFAULT);
        
        $pdo->exec("
            INSERT INTO users (employee_name, username, password, permissions) VALUES
            ('المدير العام', 'admin', '$admin_password', 'admin'),
            ('مستخدم تجريبي', 'user1', '$user_password', 'user')
        ");
        echo "<p>✅ تم إضافة المستخدمين الافتراضيين</p>";
    }
    
    $agents_count = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
    if ($agents_count == 0) {
        $pdo->exec("
            INSERT INTO agents (agent_name) VALUES
            ('المامون'), ('الشرعبي'), ('عومان'), ('الكون'),
            ('سكافا'), ('أبو اسامه'), ('أسس'), ('الاثير'),
            ('المترب'), ('باتكو'), ('بران')
        ");
        echo "<p>✅ تم إضافة الوكلاء الافتراضيين</p>";
    }
    
    // عرض الإحصائيات
    $users = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    $agents = $pdo->query("SELECT COUNT(*) as count FROM agents")->fetch()['count'];
    $collections = $pdo->query("SELECT COUNT(*) as count FROM collections")->fetch()['count'];
    
    echo "<div class='alert alert-success'>";
    echo "<h6>🎉 تم إنشاء قاعدة البيانات بنجاح!</h6>";
    echo "<ul>";
    echo "<li><strong>المستخدمون:</strong> $users</li>";
    echo "<li><strong>الوكلاء:</strong> $agents</li>";
    echo "<li><strong>التحصيلات:</strong> $collections</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<a href='migrate_to_mysql.php?step=4' class='btn btn-primary'>تحديث النظام لاستخدام MySQL</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>❌ خطأ في إنشاء قاعدة البيانات:</h6>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "<p><strong>السبب المحتمل:</strong> MySQL غير مشغل</p>";
        echo "<p><strong>الحل:</strong> تأكد من تشغيل MySQL في XAMPP</p>";
    } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<p><strong>السبب المحتمل:</strong> كلمة مرور MySQL خاطئة</p>";
        echo "<p><strong>الحل:</strong> تحقق من كلمة مرور MySQL في XAMPP</p>";
    }
    
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h6>💡 نصائح لحل المشكلة:</h6>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل XAMPP Control Panel</li>";
    echo "<li>اضغط 'Start' بجانب MySQL</li>";
    echo "<li>تأكد من ظهور اللون الأخضر</li>";
    echo "<li>جرب إعادة تشغيل XAMPP</li>";
    echo "<li>تحقق من عدم استخدام المنفذ 3306 من برنامج آخر</li>";
    echo "</ul>";
    echo "</div>";
}
?>
