<?php
$action = $_POST['backup_action'] ?? '';

if ($action === 'download') {
    $db_file = __DIR__ . '/database/collections_system.db';
    
    if (file_exists($db_file)) {
        $backup_name = 'collections_backup_' . date('Y-m-d_H-i-s') . '.db';
        
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $backup_name . '"');
        header('Content-Length: ' . filesize($db_file));
        
        readfile($db_file);
        exit;
    }
}

if ($action === 'export_sql') {
    try {
        $sql_dump = "-- نسخة احتياطية من قاعدة البيانات\n";
        $sql_dump .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
        
        // جلب جميع الجداول
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
        
        foreach ($tables as $table) {
            $table_name = $table['name'];
            
            // هيكل الجدول
            $create_sql = $pdo->query("SELECT sql FROM sqlite_master WHERE name='$table_name'")->fetch()['sql'];
            $sql_dump .= "-- هيكل الجدول $table_name\n";
            $sql_dump .= $create_sql . ";\n\n";
            
            // بيانات الجدول
            $data = $pdo->query("SELECT * FROM `$table_name`")->fetchAll();
            if (!empty($data)) {
                $sql_dump .= "-- بيانات الجدول $table_name\n";
                
                foreach ($data as $row) {
                    $columns = array_keys($row);
                    $values = array_map(function($value) use ($pdo) {
                        return is_null($value) ? 'NULL' : $pdo->quote($value);
                    }, array_values($row));
                    
                    $sql_dump .= "INSERT INTO `$table_name` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql_dump .= "\n";
            }
        }
        
        $backup_name = 'collections_backup_' . date('Y-m-d_H-i-s') . '.sql';
        
        header('Content-Type: text/plain; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $backup_name . '"');
        header('Content-Length: ' . strlen($sql_dump));
        
        echo $sql_dump;
        exit;
        
    } catch (Exception $e) {
        $error = "خطأ في تصدير SQL: " . $e->getMessage();
    }
}
?>

<div class="card">
    <div class="card-header bg-secondary text-white">
        <h5><i class="fas fa-download"></i> النسخ الاحتياطي والتصدير</h5>
    </div>
    <div class="card-body">
        
        <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-database"></i> نسخة احتياطية من قاعدة البيانات</h6>
                    </div>
                    <div class="card-body">
                        <p>تحميل نسخة كاملة من ملف قاعدة البيانات SQLite.</p>
                        
                        <?php
                        $db_file = __DIR__ . '/database/collections_system.db';
                        if (file_exists($db_file)):
                            $db_size = round(filesize($db_file) / 1024, 2);
                        ?>
                        <ul class="list-unstyled">
                            <li><strong>حجم الملف:</strong> <?= $db_size ?> KB</li>
                            <li><strong>آخر تعديل:</strong> <?= date('Y-m-d H:i:s', filemtime($db_file)) ?></li>
                        </ul>
                        
                        <form method="POST">
                            <input type="hidden" name="backup_action" value="download">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-download"></i> تحميل ملف قاعدة البيانات
                            </button>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-warning">ملف قاعدة البيانات غير موجود</div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-code"></i> تصدير SQL</h6>
                    </div>
                    <div class="card-body">
                        <p>تصدير قاعدة البيانات كملف SQL يحتوي على الهيكل والبيانات.</p>
                        
                        <form method="POST">
                            <input type="hidden" name="backup_action" value="export_sql">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-file-code"></i> تصدير كملف SQL
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> معلومات النسخ الاحتياطي</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>نسخة قاعدة البيانات (.db):</h6>
                                <ul>
                                    <li>نسخة كاملة من ملف SQLite</li>
                                    <li>يمكن استعادتها مباشرة</li>
                                    <li>تحتفظ بجميع الفهارس والعلاقات</li>
                                    <li>حجم أصغر</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>تصدير SQL (.sql):</h6>
                                <ul>
                                    <li>ملف نصي يحتوي على استعلامات SQL</li>
                                    <li>يمكن قراءته وتعديله</li>
                                    <li>متوافق مع قواعد بيانات أخرى</li>
                                    <li>مفيد للمراجعة والتحليل</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-lightbulb"></i> نصائح:</h6>
                            <ul class="mb-0">
                                <li>قم بعمل نسخة احتياطية بانتظام</li>
                                <li>احفظ النسخ في مكان آمن خارج الخادم</li>
                                <li>اختبر استعادة النسخ الاحتياطية دورياً</li>
                                <li>استخدم أسماء ملفات تحتوي على التاريخ والوقت</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar"></i> إحصائيات قاعدة البيانات</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $stats = [];
                            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
                            
                            foreach ($tables as $table) {
                                $table_name = $table['name'];
                                $count = $pdo->query("SELECT COUNT(*) as count FROM `$table_name`")->fetch()['count'];
                                $stats[$table_name] = $count;
                            }
                            
                            echo "<div class='row'>";
                            foreach ($stats as $table => $count) {
                                echo "<div class='col-md-3 mb-2'>";
                                echo "<div class='text-center'>";
                                echo "<h5 class='text-primary'>$count</h5>";
                                echo "<small>$table</small>";
                                echo "</div>";
                                echo "</div>";
                            }
                            echo "</div>";
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-warning'>لا يمكن جلب الإحصائيات: " . $e->getMessage() . "</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
