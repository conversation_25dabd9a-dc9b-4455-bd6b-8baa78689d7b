<?php
$table = $_GET['table'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 50;
$offset = ($page - 1) * $limit;

if (empty($table)) {
    echo "<div class='alert alert-warning'>يرجى اختيار جدول لعرضه</div>";
    return;
}

try {
    // التحقق من وجود الجدول
    $table_exists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table'")->fetch();
    if (!$table_exists) {
        echo "<div class='alert alert-danger'>الجدول '$table' غير موجود</div>";
        return;
    }
    
    // جلب معلومات الجدول
    $total_count = $pdo->query("SELECT COUNT(*) as count FROM `$table`")->fetch()['count'];
    $columns = $pdo->query("PRAGMA table_info(`$table`)")->fetchAll();
    
    echo "<div class='card'>";
    echo "<div class='card-header bg-success text-white d-flex justify-content-between align-items-center'>";
    echo "<h5 class='mb-0'><i class='fas fa-table'></i> جدول: $table</h5>";
    echo "<span class='badge bg-light text-dark'>$total_count سجل</span>";
    echo "</div>";
    
    echo "<div class='card-body'>";
    
    // معلومات الجدول
    echo "<div class='table-info'>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<strong>عدد الأعمدة:</strong> " . count($columns) . "<br>";
    echo "<strong>إجمالي السجلات:</strong> $total_count";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<strong>الصفحة:</strong> $page<br>";
    echo "<strong>عرض:</strong> " . min($limit, $total_count - $offset) . " من $total_count";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    if ($total_count > 0) {
        // جلب البيانات مع التصفح
        $data = $pdo->query("SELECT * FROM `$table` LIMIT $limit OFFSET $offset")->fetchAll();
        
        echo "<div class='table-container'>";
        echo "<table class='table table-striped table-hover table-sm'>";
        echo "<thead class='table-dark sticky-top'>";
        echo "<tr>";
        echo "<th>#</th>";
        foreach ($columns as $column) {
            echo "<th>{$column['name']}</th>";
        }
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($data as $index => $row) {
            echo "<tr>";
            echo "<td>" . ($offset + $index + 1) . "</td>";
            foreach ($columns as $column) {
                $value = $row[$column['name']];
                echo "<td>";
                if (is_null($value)) {
                    echo "<span class='text-muted fst-italic'>NULL</span>";
                } elseif (is_numeric($value)) {
                    echo "<span class='text-primary fw-bold'>$value</span>";
                } elseif (strlen($value) > 100) {
                    echo "<span title='" . htmlspecialchars($value) . "'>";
                    echo htmlspecialchars(substr($value, 0, 100)) . "...";
                    echo "</span>";
                } else {
                    echo htmlspecialchars($value);
                }
                echo "</td>";
            }
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
        // التصفح
        if ($total_count > $limit) {
            $total_pages = ceil($total_count / $limit);
            
            echo "<nav aria-label='تصفح الجدول'>";
            echo "<ul class='pagination pagination-sm justify-content-center'>";
            
            // الصفحة السابقة
            if ($page > 1) {
                echo "<li class='page-item'>";
                echo "<a class='page-link' href='?action=table&table=$table&page=" . ($page - 1) . "'>السابق</a>";
                echo "</li>";
            }
            
            // أرقام الصفحات
            $start_page = max(1, $page - 2);
            $end_page = min($total_pages, $page + 2);
            
            if ($start_page > 1) {
                echo "<li class='page-item'><a class='page-link' href='?action=table&table=$table&page=1'>1</a></li>";
                if ($start_page > 2) {
                    echo "<li class='page-item disabled'><span class='page-link'>...</span></li>";
                }
            }
            
            for ($i = $start_page; $i <= $end_page; $i++) {
                $active = ($i == $page) ? 'active' : '';
                echo "<li class='page-item $active'>";
                echo "<a class='page-link' href='?action=table&table=$table&page=$i'>$i</a>";
                echo "</li>";
            }
            
            if ($end_page < $total_pages) {
                if ($end_page < $total_pages - 1) {
                    echo "<li class='page-item disabled'><span class='page-link'>...</span></li>";
                }
                echo "<li class='page-item'><a class='page-link' href='?action=table&table=$table&page=$total_pages'>$total_pages</a></li>";
            }
            
            // الصفحة التالية
            if ($page < $total_pages) {
                echo "<li class='page-item'>";
                echo "<a class='page-link' href='?action=table&table=$table&page=" . ($page + 1) . "'>التالي</a>";
                echo "</li>";
            }
            
            echo "</ul>";
            echo "</nav>";
        }
        
    } else {
        echo "<div class='alert alert-info text-center'>";
        echo "<i class='fas fa-info-circle fa-2x mb-2'></i>";
        echo "<h6>الجدول فارغ</h6>";
        echo "<p>لا يحتوي هذا الجدول على أي بيانات.</p>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // معلومات إضافية عن الجدول
    echo "<div class='card mt-3'>";
    echo "<div class='card-header'>";
    echo "<h6><i class='fas fa-info'></i> معلومات الأعمدة</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm'>";
    echo "<thead><tr><th>العمود</th><th>النوع</th><th>NULL</th><th>افتراضي</th><th>مفتاح</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><code>{$column['name']}</code></td>";
        echo "<td><span class='badge bg-info'>{$column['type']}</span></td>";
        echo "<td>" . ($column['notnull'] ? '<span class="text-danger">NO</span>' : '<span class="text-success">YES</span>') . "</td>";
        echo "<td>" . ($column['dflt_value'] ? "<code>{$column['dflt_value']}</code>" : '<span class="text-muted">-</span>') . "</td>";
        echo "<td>" . ($column['pk'] ? '<i class="fas fa-key text-warning" title="مفتاح أساسي"></i>' : '-') . "</td>";
        echo "</tr>";
    }
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ في قراءة الجدول</h5>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
