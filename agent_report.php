<?php
require_once 'config/database.php';

$agent_id = $_GET['agent_id'] ?? 0;

if (!$agent_id) {
    header('Location: manage_agents.php');
    exit;
}

// جلب معلومات الوكيل
$agent_stmt = $pdo->prepare("SELECT * FROM agents WHERE agent_id = ?");
$agent_stmt->execute([$agent_id]);
$agent = $agent_stmt->fetch(PDO::FETCH_ASSOC);

if (!$agent) {
    header('Location: manage_agents.php');
    exit;
}

// جلب إحصائيات الوكيل
$stats_stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_days,
        SUM(riyal_mobile_amount) as total_riyal,
        SUM(gateway_amount) as total_gateway,
        SUM(riyal_mobile_amount + gateway_amount) as total_amount,
        AVG(riyal_mobile_amount + gateway_amount) as avg_daily,
        MIN(collection_date) as first_date,
        MAX(collection_date) as last_date
    FROM collections 
    WHERE agent_id = ?
");
$stats_stmt->execute([$agent_id]);
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// جلب آخر 10 تحصيلات
$recent_stmt = $pdo->prepare("
    SELECT * FROM collections 
    WHERE agent_id = ? 
    ORDER BY collection_date DESC 
    LIMIT 10
");
$recent_stmt->execute([$agent_id]);
$recent_collections = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير الوكيل - <?= $agent['agent_name'] ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>تقرير الوكيل: <?= $agent['agent_name'] ?></h2>
        <div>
            <button class="btn btn-warning" onclick="editAgentName()">تعديل الاسم</button>
            <a href="manage_agents.php" class="btn btn-secondary">العودة</a>
        </div>
    </div>
    
    <!-- إحصائيات عامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الأيام</h5>
                    <h3 class="text-primary"><?= $stats['total_days'] ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الريال موبايل</h5>
                    <h3 class="text-success"><?= number_format($stats['total_riyal'], 2) ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">إجمالي البوابة</h5>
                    <h3 class="text-info"><?= number_format($stats['total_gateway'], 2) ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">المجموع الكلي</h5>
                    <h3 class="text-danger"><?= number_format($stats['total_amount'], 2) ?></h3>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">معلومات إضافية</div>
                <div class="card-body">
                    <p><strong>متوسط التحصيل اليومي:</strong> <?= number_format($stats['avg_daily'], 2) ?></p>
                    <p><strong>أول تحصيل:</strong> <?= $stats['first_date'] ?></p>
                    <p><strong>آخر تحصيل:</strong> <?= $stats['last_date'] ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">إجراءات سريعة</div>
                <div class="card-body">
                    <a href="add_collection.php?agent_id=<?= $agent_id ?>" class="btn btn-success mb-2 d-block">
                        إضافة تحصيل جديد
                    </a>
                    <a href="detailed_agent_report.php?agent_id=<?= $agent_id ?>" class="btn btn-info mb-2 d-block">
                        تقرير تفصيلي كامل
                    </a>
                    <button class="btn btn-warning d-block" onclick="editAgentName()">
                        تعديل اسم الوكيل
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- آخر التحصيلات -->
    <div class="card">
        <div class="card-header">آخر 10 تحصيلات</div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الريال موبايل</th>
                            <th>البوابة</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_collections as $collection): ?>
                        <tr>
                            <td><?= $collection['collection_date'] ?></td>
                            <td class="text-end"><?= number_format($collection['riyal_mobile_amount'], 2) ?></td>
                            <td class="text-end"><?= number_format($collection['gateway_amount'], 2) ?></td>
                            <td class="text-end">
                                <strong><?= number_format($collection['riyal_mobile_amount'] + $collection['gateway_amount'], 2) ?></strong>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل الاسم -->
<div class="modal fade" id="editNameModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل اسم الوكيل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="manage_agents.php" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="agent_id" value="<?= $agent_id ?>">
                    <div class="mb-3">
                        <label class="form-label">الاسم الحالي:</label>
                        <input type="text" class="form-control" value="<?= $agent['agent_name'] ?>" disabled>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الجديد:</label>
                        <input type="text" name="new_agent_name" class="form-control" 
                               value="<?= $agent['agent_name'] ?>" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_agent" class="btn btn-primary">حفظ التعديل</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function editAgentName() {
    new bootstrap.Modal(document.getElementById('editNameModal')).show();
}
</script>
</body>
</html>

