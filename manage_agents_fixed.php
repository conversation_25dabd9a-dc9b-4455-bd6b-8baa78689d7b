<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إدارة الوكلاء - النظام الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/icons.css" rel="stylesheet">
</head>
<body>

<?php
require_once 'config/database.php';

$message = '';
$error = '';

// معالجة إضافة وكيل جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_agent'])) {
    $agent_name = trim($_POST['agent_name']);

    if (empty($agent_name)) {
        $error = 'اسم الوكيل مطلوب';
    } else {
        try {
            // التحقق من عدم وجود الوكيل
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM agents WHERE agent_name = ?");
            $check_stmt->execute([$agent_name]);

            if ($check_stmt->fetchColumn() > 0) {
                $error = 'اسم الوكيل موجود بالفعل';
            } else {
                // إضافة الوكيل الجديد
                $stmt = $pdo->prepare("INSERT INTO agents (agent_name) VALUES (?)");
                $stmt->execute([$agent_name]);
                $message = 'تم إضافة الوكيل بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// معالجة حذف وكيل
if (isset($_GET['delete_agent']) && is_numeric($_GET['delete_agent'])) {
    $agent_id = $_GET['delete_agent'];

    try {
        // التحقق من وجود تحصيلات للوكيل في الجداول الجديدة
        $check_gateway = $pdo->prepare("SELECT COUNT(*) FROM gateway WHERE agent_id = ?");
        $check_gateway->execute([$agent_id]);

        $check_riyal = $pdo->prepare("SELECT COUNT(*) FROM riyal_mobile WHERE agent_id = ?");
        $check_riyal->execute([$agent_id]);

        if ($check_gateway->fetchColumn() > 0 || $check_riyal->fetchColumn() > 0) {
            $error = 'لا يمكن حذف الوكيل لوجود تحصيلات مسجلة له';
        } else {
            $stmt = $pdo->prepare("DELETE FROM agents WHERE agent_id = ?");
            $stmt->execute([$agent_id]);
            $message = 'تم حذف الوكيل بنجاح';
        }
    } catch (PDOException $e) {
        $error = 'خطأ في حذف الوكيل: ' . $e->getMessage();
    }
}

// معالجة تعديل اسم وكيل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_agent'])) {
    $agent_id = $_POST['agent_id'];
    $new_name = trim($_POST['new_agent_name']);

    if (empty($new_name)) {
        $error = 'اسم الوكيل مطلوب';
    } else {
        try {
            // التحقق من عدم وجود اسم مشابه
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM agents WHERE agent_name = ? AND agent_id != ?");
            $check_stmt->execute([$new_name, $agent_id]);

            if ($check_stmt->fetchColumn() > 0) {
                $error = 'اسم الوكيل موجود بالفعل';
            } else {
                $stmt = $pdo->prepare("UPDATE agents SET agent_name = ? WHERE agent_id = ?");
                $stmt->execute([$new_name, $agent_id]);
                $message = 'تم تعديل اسم الوكيل بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'خطأ في تعديل الوكيل: ' . $e->getMessage();
        }
    }
}

// جلب قائمة الوكلاء مع إحصائيات
$agents_query = $pdo->query("
    SELECT
        a.*,
        COALESCE(g.gateway_count, 0) as gateway_records,
        COALESCE(g.gateway_total, 0) as gateway_total,
        COALESCE(r.riyal_count, 0) as riyal_records,
        COALESCE(r.riyal_total, 0) as riyal_total
    FROM agents a
    LEFT JOIN (
        SELECT
            agent_id,
            COUNT(*) as gateway_count,
            SUM(amount) as gateway_total
        FROM gateway
        GROUP BY agent_id
    ) g ON a.agent_id = g.agent_id
    LEFT JOIN (
        SELECT
            agent_id,
            COUNT(*) as riyal_count,
            SUM(amount) as riyal_total
        FROM riyal_mobile
        GROUP BY agent_id
    ) r ON a.agent_id = r.agent_id
    ORDER BY a.agent_name
");
$agents = $agents_query->fetchAll();

// إحصائيات عامة
$total_agents = count($agents);
$total_gateway = array_sum(array_column($agents, 'gateway_total'));
$total_riyal = array_sum(array_column($agents, 'riyal_total'));
?>

<div class="container-fluid mt-4">
    <div class="alert alert-primary">
        <h2><span class="icon icon-users icon-lg"></span> إدارة الوكلاء - النظام الجديد</h2>
        <p>إضافة وتعديل وحذف الوكلاء مع عرض إحصائيات التحصيلات</p>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?= $total_agents ?></h3>
                    <p>إجمالي الوكلاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?= number_format($total_gateway, 0) ?></h3>
                    <p>إجمالي البوابة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3><?= number_format($total_riyal, 0) ?></h3>
                    <p>إجمالي ريال موبايل</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?= number_format($total_gateway + $total_riyal, 0) ?></h3>
                    <p>الإجمالي العام</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- نموذج إضافة وكيل جديد -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-user-plus"></i> إضافة وكيل جديد</h5>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم الوكيل:</label>
                            <input type="text" name="agent_name" class="form-control" required
                                   placeholder="أدخل اسم الوكيل" maxlength="100">
                        </div>

                        <button type="submit" name="add_agent" class="btn btn-success w-100">
                            <i class="fas fa-plus"></i> إضافة الوكيل
                        </button>
                    </form>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-link"></i> روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/add_collection_fixed.php" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> إضافة تحصيلات
                        </a>
                        <a href="/daily_report_new.php" class="btn btn-secondary">
                            <i class="fas fa-chart-bar"></i> التقرير اليومي
                        </a>
                        <a href="/manage_users.php" class="btn btn-warning">
                            <i class="fas fa-users-cog"></i> إدارة المستخدمين
                        </a>
                        <a href="/" class="btn btn-success">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الوكلاء -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-list"></i> قائمة الوكلاء (<?= $total_agents ?> وكيل)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($agents)): ?>
                        <div class="alert alert-info text-center">
                            <h5>لا توجد وكلاء مسجلين</h5>
                            <p>ابدأ بإضافة وكيل جديد من النموذج على اليسار</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الوكيل</th>
                                        <th>البوابة</th>
                                        <th>ريال موبايل</th>
                                        <th>الإجمالي</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($agents as $index => $agent): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($agent['agent_name']) ?></strong>
                                            <br><small class="text-muted">معرف: <?= $agent['agent_id'] ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">
                                                <?= number_format($agent['gateway_total'], 0) ?>
                                            </span>
                                            <br><small><?= $agent['gateway_records'] ?> سجل</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">
                                                <?= number_format($agent['riyal_total'], 0) ?>
                                            </span>
                                            <br><small><?= $agent['riyal_records'] ?> سجل</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?= number_format($agent['gateway_total'] + $agent['riyal_total'], 0) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-warning"
                                                        onclick="editAgent(<?= $agent['agent_id'] ?>, '<?= htmlspecialchars($agent['agent_name']) ?>')">
                                                    <i class="fas fa-edit"></i>
                                                </button>

                                                <?php if ($agent['gateway_records'] == 0 && $agent['riyal_records'] == 0): ?>
                                                    <a href="?delete_agent=<?= $agent['agent_id'] ?>"
                                                       class="btn btn-sm btn-danger"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا الوكيل؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-secondary" disabled
                                                            title="لا يمكن حذف الوكيل لوجود تحصيلات">
                                                        <i class="fas fa-lock"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <a href="/daily_report_new.php?agent_id=<?= $agent['agent_id'] ?>"
                                                   class="btn btn-sm btn-info" title="تقرير الوكيل">
                                                    <i class="fas fa-chart-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الوكيل -->
<div class="modal fade" id="editAgentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل اسم الوكيل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="agent_id" id="edit_agent_id">
                    <div class="mb-3">
                        <label class="form-label">اسم الوكيل الجديد:</label>
                        <input type="text" name="new_agent_name" id="edit_agent_name"
                               class="form-control" required maxlength="100">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_agent" class="btn btn-warning">
                        <i class="fas fa-save"></i> حفظ التعديل
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function editAgent(agentId, agentName) {
    document.getElementById('edit_agent_id').value = agentId;
    document.getElementById('edit_agent_name').value = agentName;

    const modal = new bootstrap.Modal(document.getElementById('editAgentModal'));
    modal.show();
}

// إخفاء الرسائل تلقائياً بعد 5 ثوان
setTimeout(() => {
    const alerts = document.querySelectorAll('.alert-success, .alert-danger');
    alerts.forEach(alert => {
        alert.style.transition = 'opacity 0.5s';
        alert.style.opacity = '0';
        setTimeout(() => alert.remove(), 500);
    });
}, 5000);
</script>

<script src="/assets/icons.js"></script>
</body>
</html>
