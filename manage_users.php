<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إدارة المستخدمين والصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<?php
try {
    $pdo = new PDO("mysql:host=localhost;dbname=collections_system;port=3306", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معالجة العمليات
    if ($_POST['action'] ?? '' === 'update_user') {
        $user_id = $_POST['user_id'];
        $employee_name = $_POST['employee_name'];
        $username = $_POST['username'];
        $department = $_POST['department'];
        $phone = $_POST['phone'];
        $email = $_POST['email'];
        $permissions = $_POST['permissions'];
        $can_view_all = isset($_POST['can_view_all']) ? 1 : 0;
        $can_add_data = isset($_POST['can_add_data']) ? 1 : 0;
        $can_edit_data = isset($_POST['can_edit_data']) ? 1 : 0;
        $can_delete_data = isset($_POST['can_delete_data']) ? 1 : 0;
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $assigned_agents = implode(',', $_POST['assigned_agents'] ?? []);
        
        $stmt = $pdo->prepare("
            UPDATE users SET 
                employee_name = ?, username = ?, department = ?, phone = ?, email = ?,
                permissions = ?, can_view_all = ?, can_add_data = ?, can_edit_data = ?, 
                can_delete_data = ?, is_active = ?, assigned_agents = ?
            WHERE user_id = ?
        ");
        
        $stmt->execute([
            $employee_name, $username, $department, $phone, $email,
            $permissions, $can_view_all, $can_add_data, $can_edit_data,
            $can_delete_data, $is_active, $assigned_agents, $user_id
        ]);
        
        echo "<div class='alert alert-success'>✅ تم تحديث بيانات المستخدم بنجاح</div>";
    }
    
    if ($_POST['action'] ?? '' === 'add_user') {
        $employee_name = $_POST['employee_name'];
        $username = $_POST['username'];
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $department = $_POST['department'];
        $phone = $_POST['phone'];
        $email = $_POST['email'];
        $permissions = $_POST['permissions'];
        $can_view_all = isset($_POST['can_view_all']) ? 1 : 0;
        $can_add_data = isset($_POST['can_add_data']) ? 1 : 0;
        $can_edit_data = isset($_POST['can_edit_data']) ? 1 : 0;
        $can_delete_data = isset($_POST['can_delete_data']) ? 1 : 0;
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $assigned_agents = implode(',', $_POST['assigned_agents'] ?? []);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (
                employee_name, username, password, department, phone, email,
                permissions, can_view_all, can_add_data, can_edit_data, 
                can_delete_data, is_active, assigned_agents
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $employee_name, $username, $password, $department, $phone, $email,
            $permissions, $can_view_all, $can_add_data, $can_edit_data,
            $can_delete_data, $is_active, $assigned_agents
        ]);
        
        echo "<div class='alert alert-success'>✅ تم إضافة المستخدم الجديد بنجاح</div>";
    }
    
    // جلب المستخدمين
    $users = $pdo->query("SELECT * FROM users ORDER BY employee_name")->fetchAll();
    
    // جلب الوكلاء
    $agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll();
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>خطأ: " . $e->getMessage() . "</div>";
}
?>

<div class="container-fluid mt-4">
    <div class="alert alert-primary">
        <h2><i class="fas fa-users-cog"></i> إدارة المستخدمين والصلاحيات</h2>
        <p>إضافة وتعديل المستخدمين وإدارة صلاحياتهم</p>
    </div>

    <!-- قائمة المستخدمين الحاليين -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-list"></i> المستخدمون الحاليون</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>اسم المستخدم</th>
                                    <th>القسم</th>
                                    <th>الصلاحية</th>
                                    <th>الحالة</th>
                                    <th>الصلاحيات</th>
                                    <th>الوكلاء المخصصين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($user['employee_name']) ?></strong>
                                        <?php if ($user['email']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($user['username']) ?></td>
                                    <td><?= htmlspecialchars($user['department'] ?? 'غير محدد') ?></td>
                                    <td>
                                        <span class="badge bg-<?= $user['permissions'] == 'admin' ? 'danger' : 'primary' ?>">
                                            <?= $user['permissions'] == 'admin' ? 'مدير' : 'مستخدم' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                            <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?= $user['can_view_all'] ? '👁️ مشاهدة ' : '' ?>
                                            <?= $user['can_add_data'] ? '➕ إضافة ' : '' ?>
                                            <?= $user['can_edit_data'] ? '✏️ تعديل ' : '' ?>
                                            <?= $user['can_delete_data'] ? '🗑️ حذف ' : '' ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($user['assigned_agents']): ?>
                                            <small><?= count(explode(',', $user['assigned_agents'])) ?> وكيل</small>
                                        <?php else: ?>
                                            <small class="text-muted">جميع الوكلاء</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="editUser(<?= $user['user_id'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل مستخدم -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="userForm">
                        <input type="hidden" name="action" value="add_user" id="formAction">
                        <input type="hidden" name="user_id" id="userId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الموظف:</label>
                                    <input type="text" name="employee_name" class="form-control" required id="employeeName">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم:</label>
                                    <input type="text" name="username" class="form-control" required id="username">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور:</label>
                                    <input type="password" name="password" class="form-control" id="password">
                                    <small class="text-muted">اتركها فارغة للاحتفاظ بكلمة المرور الحالية</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">القسم:</label>
                                    <input type="text" name="department" class="form-control" id="department">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الهاتف:</label>
                                    <input type="tel" name="phone" class="form-control" id="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني:</label>
                                    <input type="email" name="email" class="form-control" id="email">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الصلاحية:</label>
                                    <select name="permissions" class="form-control" id="permissions">
                                        <option value="user">مستخدم عادي</option>
                                        <option value="admin">مدير</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الوكلاء المخصصين:</label>
                                    <select name="assigned_agents[]" class="form-control" multiple id="assignedAgents">
                                        <?php foreach ($agents as $agent): ?>
                                            <option value="<?= $agent['agent_id'] ?>">
                                                <?= htmlspecialchars($agent['agent_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="text-muted">اتركها فارغة للوصول لجميع الوكلاء</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">الصلاحيات التفصيلية:</label>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox" name="can_view_all" class="form-check-input" id="canViewAll">
                                                <label class="form-check-label">مشاهدة شاملة</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox" name="can_add_data" class="form-check-input" id="canAddData" checked>
                                                <label class="form-check-label">إضافة بيانات</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox" name="can_edit_data" class="form-check-input" id="canEditData">
                                                <label class="form-check-label">تعديل بيانات</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox" name="can_delete_data" class="form-check-input" id="canDeleteData">
                                                <label class="form-check-label">حذف بيانات</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="form-check mb-3">
                                    <input type="checkbox" name="is_active" class="form-check-input" id="isActive" checked>
                                    <label class="form-check-label">المستخدم نشط</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-success me-2">
                                <i class="fas fa-save"></i> حفظ المستخدم
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-info-circle"></i> معلومات الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <h6>أنواع الصلاحيات:</h6>
                    <ul class="list-unstyled">
                        <li><strong>مدير:</strong> صلاحيات كاملة</li>
                        <li><strong>مستخدم:</strong> صلاحيات محدودة</li>
                    </ul>
                    
                    <h6>الصلاحيات التفصيلية:</h6>
                    <ul class="list-unstyled">
                        <li><strong>👁️ مشاهدة شاملة:</strong> رؤية جميع البيانات</li>
                        <li><strong>➕ إضافة:</strong> إضافة بيانات جديدة</li>
                        <li><strong>✏️ تعديل:</strong> تعديل البيانات الموجودة</li>
                        <li><strong>🗑️ حذف:</strong> حذف البيانات</li>
                    </ul>
                    
                    <div class="alert alert-info">
                        <small>
                            <strong>ملاحظة:</strong> يمكن تخصيص وكلاء معينين لكل مستخدم لتقييد الوصول
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-link"></i> روابط مفيدة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="view_new_structure.php" class="btn btn-info">عرض هيكل قاعدة البيانات</a>
                        <a href="add_gateway_data.php" class="btn btn-warning">إضافة بيانات البوابة</a>
                        <a href="add_riyal_data.php" class="btn btn-danger">إضافة بيانات ريال موبايل</a>
                        <a href="daily_report_new.php" class="btn btn-success">التقرير اليومي</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editUser(userId) {
    // جلب بيانات المستخدم وملء النموذج
    fetch(`get_user_data.php?user_id=${userId}`)
    .then(response => response.json())
    .then(user => {
        document.getElementById('formAction').value = 'update_user';
        document.getElementById('userId').value = user.user_id;
        document.getElementById('employeeName').value = user.employee_name;
        document.getElementById('username').value = user.username;
        document.getElementById('department').value = user.department || '';
        document.getElementById('phone').value = user.phone || '';
        document.getElementById('email').value = user.email || '';
        document.getElementById('permissions').value = user.permissions;
        document.getElementById('canViewAll').checked = user.can_view_all == 1;
        document.getElementById('canAddData').checked = user.can_add_data == 1;
        document.getElementById('canEditData').checked = user.can_edit_data == 1;
        document.getElementById('canDeleteData').checked = user.can_delete_data == 1;
        document.getElementById('isActive').checked = user.is_active == 1;
        
        // تحديد الوكلاء المخصصين
        if (user.assigned_agents) {
            const assignedAgents = user.assigned_agents.split(',');
            const select = document.getElementById('assignedAgents');
            for (let option of select.options) {
                option.selected = assignedAgents.includes(option.value);
            }
        }
        
        // تغيير عنوان النموذج
        document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-user-edit"></i> تعديل المستخدم';
        document.getElementById('password').required = false;
    });
}

function resetForm() {
    document.getElementById('userForm').reset();
    document.getElementById('formAction').value = 'add_user';
    document.getElementById('userId').value = '';
    document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-user-plus"></i> إضافة مستخدم جديد';
    document.getElementById('password').required = true;
}
</script>

</body>
</html>
