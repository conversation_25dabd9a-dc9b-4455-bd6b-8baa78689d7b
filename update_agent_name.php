<?php
require_once 'config/database.php';

header('Content-Type: application/json');

if ($_POST && isset($_POST['agent_id']) && isset($_POST['new_name'])) {
    $agent_id = $_POST['agent_id'];
    $new_name = trim($_POST['new_name']);
    
    if (!empty($new_name)) {
        try {
            $stmt = $pdo->prepare("UPDATE agents SET agent_name = ? WHERE agent_id = ?");
            $stmt->execute([$new_name, $agent_id]);
            
            echo json_encode(['success' => true, 'message' => 'تم التحديث بنجاح']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'الاسم لا يمكن أن يكون فارغاً']);
    }
} else {
    echo json_encode(['success' => false, 'error' => 'بيانات غير صحيحة']);
}
?>

