// استبدال أيقونات Font Awesome بأيقونات Unicode
document.addEventListener('DOMContentLoaded', function() {
    
    // خريطة استبدال الأيقونات
    const iconMap = {
        // أيقونات أساسية
        'fas fa-home': 'icon icon-home',
        'fas fa-plus': 'icon icon-plus',
        'fas fa-plus-circle': 'icon icon-plus',
        'fas fa-chart-bar': 'icon icon-chart',
        'fas fa-chart-line': 'icon icon-chart',
        'fas fa-users': 'icon icon-users',
        'fas fa-user': 'icon icon-user',
        'fas fa-building': 'icon icon-building',
        'fas fa-calendar': 'icon icon-calendar',
        'fas fa-calendar-day': 'icon icon-calendar',
        'fas fa-calendar-alt': 'icon icon-calendar',
        'fas fa-door-open': 'icon icon-door',
        'fas fa-mobile-alt': 'icon icon-mobile',
        'fas fa-save': 'icon icon-save',
        'fas fa-edit': 'icon icon-edit',
        'fas fa-trash': 'icon icon-delete',
        'fas fa-search': 'icon icon-search',
        'fas fa-cog': 'icon icon-settings',
        'fas fa-database': 'icon icon-database',
        'fas fa-check': 'icon icon-check',
        'fas fa-check-circle': 'icon icon-check',
        'fas fa-times': 'icon icon-error',
        'fas fa-times-circle': 'icon icon-error',
        'fas fa-exclamation-triangle': 'icon icon-warning',
        'fas fa-info-circle': 'icon icon-info',
        'fas fa-money-bill': 'icon icon-money',
        'fas fa-print': 'icon icon-print',
        'fas fa-download': 'icon icon-download',
        'fas fa-upload': 'icon icon-upload',
        'fas fa-sync-alt': 'icon icon-refresh',
        'fas fa-lock': 'icon icon-lock',
        'fas fa-unlock': 'icon icon-unlock',
        'fas fa-eye': 'icon icon-eye',
        'fas fa-star': 'icon icon-star',
        'fas fa-heart': 'icon icon-heart',
        'fas fa-envelope': 'icon icon-mail',
        'fas fa-phone': 'icon icon-phone',
        'fas fa-map-marker-alt': 'icon icon-location',
        'fas fa-clock': 'icon icon-time',
        'fas fa-file': 'icon icon-file',
        'fas fa-folder': 'icon icon-folder',
        'fas fa-link': 'icon icon-link',
        'fas fa-copy': 'icon icon-copy',
        'fas fa-cut': 'icon icon-cut',
        'fas fa-arrow-up': 'icon icon-up',
        'fas fa-arrow-down': 'icon icon-down',
        'fas fa-arrow-left': 'icon icon-left',
        'fas fa-arrow-right': 'icon icon-right',
        'fas fa-bars': 'icon icon-menu',
        'fas fa-ellipsis-h': 'icon icon-more',
        
        // أيقونات خاصة بالنظام
        'fas fa-tachometer-alt': 'icon icon-chart',
        'fas fa-user-plus': 'icon icon-add-user',
        'fas fa-user-edit': 'icon icon-edit-user',
        'fas fa-user-times': 'icon icon-delete-user',
        'fas fa-crown': 'icon icon-admin',
        'fas fa-key': 'icon icon-login',
        'fas fa-sign-out-alt': 'icon icon-logout',
        'fas fa-backup': 'icon icon-backup',
        'fas fa-undo': 'icon icon-undo',
        'fas fa-redo': 'icon icon-redo',
        'fas fa-file-export': 'icon icon-export',
        'fas fa-file-import': 'icon icon-import',
        'fas fa-server': 'icon icon-database',
        'fas fa-tools': 'icon icon-settings',
        'fas fa-wrench': 'icon icon-settings',
        'fas fa-rocket': 'icon icon-success',
        'fas fa-magic': 'icon icon-star',
        'fas fa-eraser': 'icon icon-delete',
        'fas fa-external-link-alt': 'icon icon-link',
        'fas fa-sitemap': 'icon icon-chart',
        'fas fa-stethoscope': 'icon icon-check',
        'fas fa-bolt': 'icon icon-warning',
        
        // أيقونات الحالة
        'text-success': 'icon-success',
        'text-danger': 'icon-danger',
        'text-warning': 'icon-warning',
        'text-info': 'icon-info',
        'text-primary': 'icon-primary',
        'text-secondary': 'icon-secondary'
    };
    
    // استبدال الأيقونات
    function replaceIcons() {
        // البحث عن جميع العناصر التي تحتوي على أيقونات Font Awesome
        const elements = document.querySelectorAll('[class*="fas fa-"], [class*="far fa-"], [class*="fab fa-"]');
        
        elements.forEach(element => {
            const classes = element.className;
            
            // البحث عن تطابق في خريطة الأيقونات
            for (const [oldClass, newClass] of Object.entries(iconMap)) {
                if (classes.includes(oldClass)) {
                    // إزالة الفئات القديمة وإضافة الجديدة
                    element.className = element.className.replace(oldClass, newClass);
                    break;
                }
            }
        });
        
        // استبدال الأيقونات في النصوص
        const textElements = document.querySelectorAll('*');
        textElements.forEach(element => {
            if (element.children.length === 0 && element.textContent) {
                let text = element.innerHTML;
                
                // استبدال رموز HTML للأيقونات
                text = text.replace(/&lt;i class="fas fa-([^"]+)"&gt;&lt;\/i&gt;/g, function(match, iconName) {
                    const fullClass = 'fas fa-' + iconName;
                    return iconMap[fullClass] ? `<span class="${iconMap[fullClass]}"></span>` : match;
                });
                
                if (text !== element.innerHTML) {
                    element.innerHTML = text;
                }
            }
        });
    }
    
    // تشغيل استبدال الأيقونات
    replaceIcons();
    
    // مراقبة التغييرات في DOM لاستبدال الأيقونات الجديدة
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // البحث عن أيقونات في العنصر الجديد
                        const iconElements = node.querySelectorAll ? 
                            node.querySelectorAll('[class*="fas fa-"], [class*="far fa-"], [class*="fab fa-"]') : [];
                        
                        iconElements.forEach(element => {
                            const classes = element.className;
                            for (const [oldClass, newClass] of Object.entries(iconMap)) {
                                if (classes.includes(oldClass)) {
                                    element.className = element.className.replace(oldClass, newClass);
                                    break;
                                }
                            }
                        });
                    }
                });
            }
        });
    });
    
    // بدء مراقبة التغييرات
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // دالة لإضافة أيقونة جديدة
    window.addIcon = function(element, iconClass) {
        if (iconMap[iconClass]) {
            element.className = (element.className + ' ' + iconMap[iconClass]).trim();
        }
    };
    
    // دالة لتغيير أيقونة موجودة
    window.changeIcon = function(element, oldIconClass, newIconClass) {
        if (iconMap[oldIconClass] && iconMap[newIconClass]) {
            element.className = element.className.replace(iconMap[oldIconClass], iconMap[newIconClass]);
        }
    };
    
    // إضافة أيقونات للأزرار التي لا تحتوي على أيقونات
    const buttons = document.querySelectorAll('button, .btn');
    buttons.forEach(button => {
        const text = button.textContent.trim();
        
        // إضافة أيقونات بناءً على النص
        if (text.includes('حفظ') || text.includes('save')) {
            button.innerHTML = '<span class="icon icon-save"></span>' + button.innerHTML;
        } else if (text.includes('إضافة') || text.includes('add')) {
            button.innerHTML = '<span class="icon icon-plus"></span>' + button.innerHTML;
        } else if (text.includes('تعديل') || text.includes('edit')) {
            button.innerHTML = '<span class="icon icon-edit"></span>' + button.innerHTML;
        } else if (text.includes('حذف') || text.includes('delete')) {
            button.innerHTML = '<span class="icon icon-delete"></span>' + button.innerHTML;
        } else if (text.includes('بحث') || text.includes('search')) {
            button.innerHTML = '<span class="icon icon-search"></span>' + button.innerHTML;
        } else if (text.includes('طباعة') || text.includes('print')) {
            button.innerHTML = '<span class="icon icon-print"></span>' + button.innerHTML;
        } else if (text.includes('تحديث') || text.includes('refresh')) {
            button.innerHTML = '<span class="icon icon-refresh"></span>' + button.innerHTML;
        }
    });
    
    console.log('تم تحميل نظام الأيقونات البديل بنجاح');
});

// دالة لإضافة أيقونة ديناميكياً
function addDynamicIcon(selector, iconClass) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        if (!element.querySelector('.icon')) {
            const iconSpan = document.createElement('span');
            iconSpan.className = 'icon ' + iconClass;
            element.insertBefore(iconSpan, element.firstChild);
        }
    });
}

// تطبيق الأيقونات على عناصر محددة
setTimeout(() => {
    addDynamicIcon('.btn-primary', 'icon-primary');
    addDynamicIcon('.btn-success', 'icon-success');
    addDynamicIcon('.btn-warning', 'icon-warning');
    addDynamicIcon('.btn-danger', 'icon-danger');
    addDynamicIcon('.btn-info', 'icon-info');
}, 1000);
