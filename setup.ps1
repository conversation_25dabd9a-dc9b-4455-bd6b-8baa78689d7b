# سكريبت إعداد النظام على Windows Server
Write-Host "إعداد نظام إدارة تحصيلات الوكلاء..." -ForegroundColor Green

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "يجب تشغيل هذا السكريبت كمدير" -ForegroundColor Red
    exit 1
}

# إنشاء مجلد الموقع
$sitePath = "C:\inetpub\wwwroot\collections_system"
if (!(Test-Path $sitePath)) {
    New-Item -ItemType Directory -Path $sitePath -Force
    Write-Host "تم إنشاء مجلد الموقع: $sitePath" -ForegroundColor Green
}

# نسخ الملفات
Copy-Item -Path ".\*" -Destination $sitePath -Recurse -Force
Write-Host "تم نسخ ملفات النظام" -ForegroundColor Green

# إعداد IIS Site
Import-Module WebAdministration
if (Get-Website -Name "CollectionsSystem" -ErrorAction SilentlyContinue) {
    Remove-Website -Name "CollectionsSystem"
}

New-Website -Name "CollectionsSystem" -Port 7445 -PhysicalPath $sitePath
Write-Host "تم إنشاء موقع IIS على المنفذ 7445" -ForegroundColor Green

# فتح المنفذ في جدار الحماية
New-NetFirewallRule -DisplayName "Collections System Port 7445" -Direction Inbound -Protocol TCP -LocalPort 7445 -Action Allow
Write-Host "تم فتح المنفذ 7445 في جدار الحماية" -ForegroundColor Green

# بدء الموقع
Start-Website -Name "CollectionsSystem"
Write-Host "تم بدء تشغيل الموقع" -ForegroundColor Green

Write-Host "`nالنظام جاهز للاستخدام:" -ForegroundColor Yellow
Write-Host "الرابط الداخلي: http://localhost:7445" -ForegroundColor Cyan
Write-Host "الرابط الخارجي: http://***********:7445" -ForegroundColor Cyan
