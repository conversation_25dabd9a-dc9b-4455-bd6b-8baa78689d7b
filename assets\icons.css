/* أيقونات بديلة باستخدام Unicode */
.icon {
    font-family: Arial, sans-serif;
    font-weight: bold;
    display: inline-block;
    margin-right: 5px;
}

/* أيقونات أساسية */
.icon-home::before { content: "🏠"; }
.icon-plus::before { content: "➕"; }
.icon-chart::before { content: "📊"; }
.icon-users::before { content: "👥"; }
.icon-building::before { content: "🏢"; }
.icon-calendar::before { content: "📅"; }
.icon-door::before { content: "🚪"; }
.icon-mobile::before { content: "📱"; }
.icon-save::before { content: "💾"; }
.icon-edit::before { content: "✏️"; }
.icon-delete::before { content: "🗑️"; }
.icon-search::before { content: "🔍"; }
.icon-settings::before { content: "⚙️"; }
.icon-database::before { content: "🗄️"; }
.icon-report::before { content: "📈"; }
.icon-check::before { content: "✅"; }
.icon-warning::before { content: "⚠️"; }
.icon-error::before { content: "❌"; }
.icon-info::before { content: "ℹ️"; }
.icon-success::before { content: "✅"; }
.icon-money::before { content: "💰"; }
.icon-print::before { content: "🖨️"; }
.icon-download::before { content: "⬇️"; }
.icon-upload::before { content: "⬆️"; }
.icon-refresh::before { content: "🔄"; }
.icon-lock::before { content: "🔒"; }
.icon-unlock::before { content: "🔓"; }
.icon-eye::before { content: "👁️"; }
.icon-star::before { content: "⭐"; }
.icon-heart::before { content: "❤️"; }
.icon-mail::before { content: "📧"; }
.icon-phone::before { content: "📞"; }
.icon-location::before { content: "📍"; }
.icon-time::before { content: "⏰"; }
.icon-date::before { content: "📆"; }
.icon-file::before { content: "📄"; }
.icon-folder::before { content: "📁"; }
.icon-link::before { content: "🔗"; }
.icon-copy::before { content: "📋"; }
.icon-paste::before { content: "📌"; }
.icon-cut::before { content: "✂️"; }
.icon-undo::before { content: "↶"; }
.icon-redo::before { content: "↷"; }
.icon-up::before { content: "⬆️"; }
.icon-down::before { content: "⬇️"; }
.icon-left::before { content: "⬅️"; }
.icon-right::before { content: "➡️"; }
.icon-close::before { content: "❌"; }
.icon-menu::before { content: "☰"; }
.icon-more::before { content: "⋯"; }

/* أحجام الأيقونات */
.icon-sm { font-size: 0.8em; }
.icon-lg { font-size: 1.5em; }
.icon-xl { font-size: 2em; }
.icon-2x { font-size: 2em; }
.icon-3x { font-size: 3em; }

/* ألوان الأيقونات */
.icon-primary { color: #007bff; }
.icon-secondary { color: #6c757d; }
.icon-success { color: #28a745; }
.icon-danger { color: #dc3545; }
.icon-warning { color: #ffc107; }
.icon-info { color: #17a2b8; }
.icon-light { color: #f8f9fa; }
.icon-dark { color: #343a40; }

/* تأثيرات الأيقونات */
.icon-spin {
    animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.icon-pulse {
    animation: icon-pulse 1s ease-in-out infinite alternate;
}

@keyframes icon-pulse {
    0% { opacity: 1; }
    100% { opacity: 0.5; }
}

/* أيقونات خاصة بالنظام */
.icon-gateway::before { content: "🚪"; }
.icon-riyal::before { content: "📱"; }
.icon-agent::before { content: "🏢"; }
.icon-collection::before { content: "💰"; }
.icon-daily::before { content: "📅"; }
.icon-monthly::before { content: "📊"; }
.icon-yearly::before { content: "📈"; }
.icon-total::before { content: "🧮"; }
.icon-add-user::before { content: "👤➕"; }
.icon-edit-user::before { content: "👤✏️"; }
.icon-delete-user::before { content: "👤🗑️"; }
.icon-admin::before { content: "👑"; }
.icon-user::before { content: "👤"; }
.icon-login::before { content: "🔑"; }
.icon-logout::before { content: "🚪"; }
.icon-backup::before { content: "💾"; }
.icon-restore::before { content: "🔄"; }
.icon-export::before { content: "📤"; }
.icon-import::before { content: "📥"; }

/* أيقونات الحالة */
.icon-online::before { content: "🟢"; }
.icon-offline::before { content: "🔴"; }
.icon-pending::before { content: "🟡"; }
.icon-active::before { content: "✅"; }
.icon-inactive::before { content: "⭕"; }

/* أيقونات الأرقام */
.icon-1::before { content: "1️⃣"; }
.icon-2::before { content: "2️⃣"; }
.icon-3::before { content: "3️⃣"; }
.icon-4::before { content: "4️⃣"; }
.icon-5::before { content: "5️⃣"; }
.icon-6::before { content: "6️⃣"; }
.icon-7::before { content: "7️⃣"; }
.icon-8::before { content: "8️⃣"; }
.icon-9::before { content: "9️⃣"; }
.icon-0::before { content: "0️⃣"; }

/* تخصيص إضافي */
.btn .icon {
    margin-right: 5px;
    margin-left: 0;
}

.card-header .icon {
    margin-right: 8px;
}

.nav-link .icon {
    margin-right: 6px;
}

.alert .icon {
    margin-right: 5px;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .icon {
        font-size: 1.1em;
    }
    
    .icon-lg {
        font-size: 1.3em;
    }
    
    .icon-xl {
        font-size: 1.8em;
    }
}
