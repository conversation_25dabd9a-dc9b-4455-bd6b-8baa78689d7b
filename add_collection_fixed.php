<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>إضافة التحصيلات - النظام الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .agent-card { transition: all 0.3s; cursor: pointer; }
        .agent-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .amount-input { font-size: 1.1em; text-align: center; }
        .save-btn { position: fixed; bottom: 20px; right: 20px; z-index: 1000; }
        .stats-card { background: linear-gradient(45deg, #007bff, #0056b3); }
    </style>
</head>
<body>

<?php
require_once 'config/database.php';

// معالجة حفظ البيانات
if ($_POST['action'] ?? '' === 'save_collections') {
    $selected_date = $_POST['selected_date'];
    $collections_data = $_POST['collections_data'] ?? [];
    $user_id = $_SESSION['user_id'] ?? 1;
    
    $saved_count = 0;
    $updated_count = 0;
    $errors = [];
    
    try {
        $pdo->beginTransaction();
        
        // البحث عن day_id أو إنشاؤه
        $day_stmt = $pdo->prepare("SELECT day_id FROM days WHERE date = ?");
        $day_stmt->execute([$selected_date]);
        $day = $day_stmt->fetch();
        
        if (!$day) {
            // إنشاء اليوم إذا لم يكن موجوداً
            $day_name = date('l', strtotime($selected_date));
            $month_name = date('F', strtotime($selected_date));
            $year = date('Y', strtotime($selected_date));
            
            $day_names_ar = [
                'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء', 'Wednesday' => 'الأربعاء',
                'Thursday' => 'الخميس', 'Friday' => 'الجمعة', 'Saturday' => 'السبت', 'Sunday' => 'الأحد'
            ];
            $month_names_ar = [
                'January' => 'يناير', 'February' => 'فبراير', 'March' => 'مارس',
                'April' => 'أبريل', 'May' => 'مايو', 'June' => 'يونيو',
                'July' => 'يوليو', 'August' => 'أغسطس', 'September' => 'سبتمبر',
                'October' => 'أكتوبر', 'November' => 'نوفمبر', 'December' => 'ديسمبر'
            ];
            
            $insert_day = $pdo->prepare("INSERT INTO days (date, day_name, month_name, year) VALUES (?, ?, ?, ?)");
            $insert_day->execute([$selected_date, $day_names_ar[$day_name], $month_names_ar[$month_name], $year]);
            $day_id = $pdo->lastInsertId();
        } else {
            $day_id = $day['day_id'];
        }
        
        // معالجة بيانات البوابة
        if (isset($collections_data['gateway'])) {
            foreach ($collections_data['gateway'] as $agent_id => $amount) {
                if ($amount > 0) {
                    // التحقق من وجود السجل
                    $check_stmt = $pdo->prepare("SELECT gateway_id FROM gateway WHERE agent_id = ? AND day_id = ?");
                    $check_stmt->execute([$agent_id, $day_id]);
                    $existing = $check_stmt->fetch();
                    
                    if ($existing) {
                        // تحديث السجل الموجود
                        $update_stmt = $pdo->prepare("
                            UPDATE gateway SET 
                                amount = ?, 
                                updated_at = NOW(), 
                                updated_by = ?
                            WHERE gateway_id = ?
                        ");
                        $update_stmt->execute([$amount, $user_id, $existing['gateway_id']]);
                        $updated_count++;
                    } else {
                        // إنشاء سجل جديد
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO gateway (agent_id, day_id, amount, created_by) 
                            VALUES (?, ?, ?, ?)
                        ");
                        $insert_stmt->execute([$agent_id, $day_id, $amount, $user_id]);
                        $saved_count++;
                    }
                }
            }
        }
        
        // معالجة بيانات ريال موبايل
        if (isset($collections_data['riyal_mobile'])) {
            foreach ($collections_data['riyal_mobile'] as $agent_id => $amount) {
                if ($amount > 0) {
                    // التحقق من وجود السجل
                    $check_stmt = $pdo->prepare("SELECT riyal_id FROM riyal_mobile WHERE agent_id = ? AND day_id = ?");
                    $check_stmt->execute([$agent_id, $day_id]);
                    $existing = $check_stmt->fetch();
                    
                    if ($existing) {
                        // تحديث السجل الموجود
                        $update_stmt = $pdo->prepare("
                            UPDATE riyal_mobile SET 
                                amount = ?, 
                                updated_at = NOW(), 
                                updated_by = ?
                            WHERE riyal_id = ?
                        ");
                        $update_stmt->execute([$amount, $user_id, $existing['riyal_id']]);
                        $updated_count++;
                    } else {
                        // إنشاء سجل جديد
                        $insert_stmt = $pdo->prepare("
                            INSERT INTO riyal_mobile (agent_id, day_id, amount, created_by) 
                            VALUES (?, ?, ?, ?)
                        ");
                        $insert_stmt->execute([$agent_id, $day_id, $amount, $user_id]);
                        $saved_count++;
                    }
                }
            }
        }
        
        $pdo->commit();
        
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ تم حفظ البيانات بنجاح!</h5>";
        echo "<p>تم إنشاء $saved_count سجل جديد وتحديث $updated_count سجل موجود</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        $pdo->rollback();
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ خطأ في حفظ البيانات</h5>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// جلب الوكلاء
$agents = $pdo->query("SELECT * FROM agents ORDER BY agent_name")->fetchAll();

// التاريخ المحدد
$selected_date = $_POST['selected_date'] ?? date('Y-m-d');

// جلب البيانات الموجودة
$existing_gateway = [];
$existing_riyal = [];

$gateway_stmt = $pdo->prepare("
    SELECT g.agent_id, g.amount 
    FROM gateway g
    JOIN days d ON g.day_id = d.day_id
    WHERE d.date = ?
");
$gateway_stmt->execute([$selected_date]);
$gateway_results = $gateway_stmt->fetchAll();

foreach ($gateway_results as $row) {
    $existing_gateway[$row['agent_id']] = $row['amount'];
}

$riyal_stmt = $pdo->prepare("
    SELECT r.agent_id, r.amount 
    FROM riyal_mobile r
    JOIN days d ON r.day_id = d.day_id
    WHERE d.date = ?
");
$riyal_stmt->execute([$selected_date]);
$riyal_results = $riyal_stmt->fetchAll();

foreach ($riyal_results as $row) {
    $existing_riyal[$row['agent_id']] = $row['amount'];
}
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-primary">
                <h2><i class="fas fa-plus-circle"></i> إضافة التحصيلات - النظام الجديد</h2>
                <p>إدخال بيانات البوابة وريال موبايل لجميع الوكلاء</p>
            </div>
        </div>
    </div>

    <form method="POST" id="collectionsForm">
        <input type="hidden" name="action" value="save_collections">
        
        <!-- اختيار التاريخ والإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-calendar"></i> اختيار التاريخ</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">التاريخ:</label>
                            <input type="date" name="selected_date" class="form-control" 
                                   value="<?= $selected_date ?>" onchange="this.form.submit()">
                        </div>
                        
                        <div class="alert alert-info">
                            <small>
                                <strong>التاريخ المحدد:</strong><br>
                                <?= date('l, d F Y', strtotime($selected_date)) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card stats-card text-white">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="total-agents"><?= count($agents) ?></h4>
                                    <small>إجمالي الوكلاء</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="gateway-agents"><?= count($existing_gateway) ?></h4>
                                    <small>بوابة مدخلة</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="riyal-agents"><?= count($existing_riyal) ?></h4>
                                    <small>ريال مدخل</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="gateway-total"><?= number_format(array_sum($existing_gateway), 0) ?></h4>
                                    <small>إجمالي البوابة</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="riyal-total"><?= number_format(array_sum($existing_riyal), 0) ?></h4>
                                    <small>إجمالي ريال</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <h4 id="grand-total"><?= number_format(array_sum($existing_gateway) + array_sum($existing_riyal), 0) ?></h4>
                                    <small>الإجمالي العام</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات الوكلاء -->
        <div class="row">
            <?php foreach ($agents as $agent): ?>
                <div class="col-md-4 mb-3">
                    <div class="card agent-card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-building"></i>
                                <?= htmlspecialchars($agent['agent_name']) ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">البوابة:</label>
                                    <input type="number" 
                                           name="collections_data[gateway][<?= $agent['agent_id'] ?>]" 
                                           class="form-control amount-input gateway-input" 
                                           step="0.01" 
                                           min="0"
                                           value="<?= $existing_gateway[$agent['agent_id']] ?? '' ?>"
                                           placeholder="0.00"
                                           onchange="updateStats()">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">ريال موبايل:</label>
                                    <input type="number" 
                                           name="collections_data[riyal_mobile][<?= $agent['agent_id'] ?>]" 
                                           class="form-control amount-input riyal-input" 
                                           step="0.01" 
                                           min="0"
                                           value="<?= $existing_riyal[$agent['agent_id']] ?? '' ?>"
                                           placeholder="0.00"
                                           onchange="updateStats()">
                                </div>
                            </div>
                            
                            <?php 
                            $has_gateway = isset($existing_gateway[$agent['agent_id']]) && $existing_gateway[$agent['agent_id']] > 0;
                            $has_riyal = isset($existing_riyal[$agent['agent_id']]) && $existing_riyal[$agent['agent_id']] > 0;
                            ?>
                            
                            <?php if ($has_gateway || $has_riyal): ?>
                                <div class="alert alert-success alert-sm mt-2">
                                    <small>
                                        <i class="fas fa-check"></i> 
                                        <?php if ($has_gateway && $has_riyal): ?>
                                            البوابة وريال موبايل محفوظان
                                        <?php elseif ($has_gateway): ?>
                                            البوابة محفوظة
                                        <?php else: ?>
                                            ريال موبايل محفوظ
                                        <?php endif; ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- أزرار التحكم -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success btn-lg me-3">
                            <i class="fas fa-save"></i> حفظ جميع البيانات
                        </button>
                        
                        <button type="button" class="btn btn-warning me-3" onclick="clearAll()">
                            <i class="fas fa-eraser"></i> مسح الكل
                        </button>
                        
                        <button type="button" class="btn btn-info me-3" onclick="fillSample()">
                            <i class="fas fa-magic"></i> بيانات تجريبية
                        </button>
                        
                        <a href="/daily_report_new.php?date=<?= $selected_date ?>" class="btn btn-secondary me-3">
                            <i class="fas fa-chart-bar"></i> عرض التقرير
                        </a>
                        
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- زر الحفظ السريع -->
<button type="button" class="btn btn-success btn-lg save-btn" onclick="document.getElementById('collectionsForm').submit()">
    <i class="fas fa-save"></i>
</button>

<script>
function updateStats() {
    let gatewayTotal = 0;
    let riyalTotal = 0;
    let gatewayAgents = 0;
    let riyalAgents = 0;
    
    document.querySelectorAll('.gateway-input').forEach(input => {
        const value = parseFloat(input.value) || 0;
        if (value > 0) {
            gatewayTotal += value;
            gatewayAgents++;
        }
    });
    
    document.querySelectorAll('.riyal-input').forEach(input => {
        const value = parseFloat(input.value) || 0;
        if (value > 0) {
            riyalTotal += value;
            riyalAgents++;
        }
    });
    
    document.getElementById('gateway-agents').textContent = gatewayAgents;
    document.getElementById('riyal-agents').textContent = riyalAgents;
    document.getElementById('gateway-total').textContent = gatewayTotal.toLocaleString('ar-SA', {maximumFractionDigits: 0});
    document.getElementById('riyal-total').textContent = riyalTotal.toLocaleString('ar-SA', {maximumFractionDigits: 0});
    document.getElementById('grand-total').textContent = (gatewayTotal + riyalTotal).toLocaleString('ar-SA', {maximumFractionDigits: 0});
}

function clearAll() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        document.querySelectorAll('.amount-input').forEach(input => {
            input.value = '';
        });
        updateStats();
    }
}

function fillSample() {
    if (confirm('هل تريد ملء البيانات التجريبية؟')) {
        document.querySelectorAll('.gateway-input').forEach(input => {
            input.value = (Math.random() * 10000 + 1000).toFixed(2);
        });
        document.querySelectorAll('.riyal-input').forEach(input => {
            input.value = (Math.random() * 8000 + 500).toFixed(2);
        });
        updateStats();
    }
}

// تحديث الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateStats);
</script>

</body>
</html>
